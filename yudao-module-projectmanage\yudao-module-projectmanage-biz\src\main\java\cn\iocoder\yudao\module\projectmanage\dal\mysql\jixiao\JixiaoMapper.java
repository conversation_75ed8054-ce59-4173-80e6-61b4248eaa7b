package cn.iocoder.yudao.module.projectmanage.dal.mysql.jixiao;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.projectmanage.dal.dataobject.jixiao.JixiaoDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import cn.iocoder.yudao.module.projectmanage.controller.admin.jixiao.vo.*;

/**
 * 绩效考核 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface JixiaoMapper extends BaseMapperX<JixiaoDO> {

    default PageResult<JixiaoDO> selectPage(JixiaoPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<JixiaoDO>()
                .eqIfPresent(JixiaoDO::getContractId, reqVO.getContractId())
                .eqIfPresent(JixiaoDO::getProjectId, reqVO.getProjectId())
                .likeIfPresent(JixiaoDO::getProjectName, reqVO.getProjectName())
                .eqIfPresent(JixiaoDO::getProjectNature, reqVO.getProjectNature())
                .eqIfPresent(JixiaoDO::getProjectType, reqVO.getProjectType())
                .eqIfPresent(JixiaoDO::getProjectCoefficient, reqVO.getProjectCoefficient())
                .eqIfPresent(JixiaoDO::getContractWorkday, reqVO.getContractWorkday())
                .eqIfPresent(JixiaoDO::getProjectWorkday, reqVO.getProjectWorkday())
                .eqIfPresent(JixiaoDO::getPersonNum, reqVO.getPersonNum())
                .eqIfPresent(JixiaoDO::getProjectManage, reqVO.getProjectManage())
                .eqIfPresent(JixiaoDO::getProjectPerson, reqVO.getProjectPerson())
                .eqIfPresent(JixiaoDO::getProjectProportions, reqVO.getProjectProportions())
                .eqIfPresent(JixiaoDO::getSeasonWorkday, reqVO.getSeasonWorkday())
                .eqIfPresent(JixiaoDO::getSeasonAllocate, reqVO.getSeasonAllocate())
                .eqIfPresent(JixiaoDO::getSeasonPersonallocate, reqVO.getSeasonPersonallocate())
                .eqIfPresent(JixiaoDO::getAnnualWorkday, reqVO.getAnnualWorkday())
                .eqIfPresent(JixiaoDO::getAnnualAllocate, reqVO.getAnnualAllocate())
                .eqIfPresent(JixiaoDO::getAnnualPersonallocate, reqVO.getAnnualPersonallocate())
                .eqIfPresent(JixiaoDO::getExWorkday, reqVO.getExWorkday())
                .eqIfPresent(JixiaoDO::getDeductions, reqVO.getDeductions())
                .eqIfPresent(JixiaoDO::getRemarks, reqVO.getRemarks())
                .eqIfPresent(JixiaoDO::getYearmonth, reqVO.getYearmonth())
                .betweenIfPresent(JixiaoDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(JixiaoDO::getYearmonth));
    }

    @Select("""
        <script>
        SELECT 
            pp.id as projectPersonId,
            p.id as projectId,
            c.id as contractId,
            pp.time as monthPeriod,
            p.project_name as projectName,
            p.nature as projectNature,
            p.managing_department as managingDepartment,
            p.project_type as projectType,
            p.type as type,
            p.coefficient as projectCoefficient,
            p.project_manager_name as projectManagerName,
            p.total_amount as totalAmount,
            p.importance as importance,
            c.work_day as contractDuration,
            (SELECT COUNT(DISTINCT pp3.id) 
             FROM projectmanage_project_person pp3 
             WHERE pp3.project_id = p.id 
               AND pp3.deleted = 0 
               AND pp3.name IS NOT NULL
               AND pp3.time IS NOT NULL) as memberCount,
            pp.name as memberName,
            pp.percentage as personalPercentage,
            pp.reject_reason as rejectReason
        FROM 
            projectmanage_project p
            INNER JOIN projectmanage_project_person pp ON p.id = pp.project_id
            INNER JOIN projectmanage_contract c ON c.project_id = p.id
        WHERE 
            p.deleted = 0 
            AND pp.deleted = 0
            AND c.deleted = 0
            AND (c.main_or_patch != '主' OR c.main_or_patch IS NULL)
            AND p.pay_reci_relation = '收' 
            AND pp.name IS NOT NULL
            AND p.coefficient IS NOT NULL
            AND pp.time IS NOT NULL
            <choose>
                <when test="status == 'rejected'">
                    <!-- 已驳回：查询manager_confirm = '驳回' 的数据 -->
                    AND pp.manager_confirm = '驳回'
                </when>
                <otherwise>
                    <!-- 待确认：查询manager_confirm = '未确认' 或 null 的数据，且reject_reason为空 -->
                    AND (pp.manager_confirm = '未确认' OR pp.manager_confirm IS NULL)
                    AND (pp.reject_reason IS NULL OR pp.reject_reason = '')
                </otherwise>
            </choose>
            <if test="monthPeriod != null and monthPeriod != ''">
                AND pp.time = #{monthPeriod}
            </if>
            <if test="managingDepartment != null and managingDepartment != ''">
                AND p.managing_department = #{managingDepartment}
            </if>
            AND EXISTS (
                SELECT 1
                FROM (
                    SELECT 
                        pp2.project_id,
                        pp2.time,
                        SUM(CAST(pp2.percentage AS DECIMAL(10,2))) as total_percentage
                    FROM projectmanage_project_person pp2
                    WHERE pp2.deleted = 0
                      AND pp2.name IS NOT NULL
                      AND pp2.time IS NOT NULL
                      AND pp2.percentage IS NOT NULL
                      AND pp2.project_id = p.id
                      AND pp2.time = pp.time
                    GROUP BY pp2.project_id, pp2.time
                ) percentage_check
                WHERE percentage_check.total_percentage = 100
            )
        ORDER BY 
            p.project_name, pp.name
        </script>
    """)
    List<DepartmentPerformanceRespVO> selectDepartmentPerformanceData(DepartmentPerformanceReqVO reqVO);

    /**
     * 更新项目人员的经理确认状态
     */
    @Update("""
        <script>
        UPDATE projectmanage_project_person 
        SET manager_confirm = '确认',
            manager_operate_time = NOW()
        WHERE time = #{monthPeriod}
        AND project_id IN
        <foreach item="projectId" collection="projectIds" open="(" separator="," close=")">
            #{projectId}
        </foreach>
        AND deleted = 0
        </script>
    """)
    void updateProjectPersonManagerConfirm(String monthPeriod, java.util.Set<Long> projectIds);

    /**
     * 根据项目人员ID直接更新经理确认状态
     */
    @Update("""
        <script>
        UPDATE projectmanage_project_person 
        SET manager_confirm = '确认',
            manager_operate_time = NOW()
        WHERE id IN
        <foreach item="id" collection="projectPersonIds" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND deleted = 0
        </script>
    """)
    void updateProjectPersonManagerConfirmByIds(java.util.Set<Long> projectPersonIds);

    /**
     * 根据项目ID和月期批量驳回项目成员的绩效占比
     */
    @Update("""
        <script>
        UPDATE projectmanage_project_person 
        SET manager_confirm = '驳回',
            reject_reason = #{rejectReason},
            manager_reject_time = NOW(),
            manager_operate_time = NOW()
        WHERE project_id IN
        <foreach item="projectId" collection="projectIds" open="(" separator="," close=")">
            #{projectId}
        </foreach>
        AND time = #{monthPeriod}
        AND deleted = 0
        </script>
    """)
    void rejectProjectPersonByProjectIds(String monthPeriod, java.util.Set<Long> projectIds, String rejectReason);

    /**
     * 根据项目ID和月期批量撤销驳回项目成员的绩效占比
     */
    @Update("""
        <script>
        UPDATE projectmanage_project_person 
        SET manager_confirm = '未确认',
            reject_reason = NULL,
            manager_reject_time = NULL,
            manager_operate_time = NOW()
        WHERE project_id IN
        <foreach item="projectId" collection="projectIds" open="(" separator="," close=")">
            #{projectId}
        </foreach>
        AND time = #{monthPeriod}
        AND deleted = 0
        </script>
    """)
    void revokeRejectProjectPersonByProjectIds(String monthPeriod, java.util.Set<Long> projectIds);


}