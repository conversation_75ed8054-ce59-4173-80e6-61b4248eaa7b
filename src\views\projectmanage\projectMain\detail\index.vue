<template>
  <ContentWrap>
    <div class="project-overview-container" ref="containerRef">
      <el-row>
        <el-col :span="24">
          <!-- 内容区域 -->
          <div class="content-section">
            <!-- 项目信息 -->
            <ProjectInfo
              :id="queryParams.id"
              :project-group-code="queryParams.projectGropNumber"
              :project-group-manager="projectGroupManager"
              :project-managers="projectManagers"
              :is-project-member="!!isProjectMember"

              @row-click="handleProjectItemClick"
            />

            <!-- 财务情况 -->
            <ProjectFinance
              :id="selectedProjectId"
              :contract-id="selectedContractId"
              :project-group-manager="projectGroupManager"

              :project-managers="projectManagers"
            />

            <!-- 系统记录 -->
            <ProjectSystem
              :project-group-id="queryParams.id"
              :project-id="selectedProjectId"
              :project-group-manager="projectGroupManager"

              :project-managers="projectManagers"
            />

            <!-- 项目阶段 -->
            <ProjectStages
              :project-id="selectedProjectId"
              :project-group-code="queryParams.projectGropNumber"
              :project-group-id="queryParams.id"
              :project-group-name="queryParams.projectGropName"
              :project-name="selectedProjectName"
              :project-group-manager="projectGroupManager"

              :project-managers="projectManagers"
            />

            <!-- 项目文档 -->
            <ProjectDocuments
              :id="projectId"
              :project-id="selectedProjectId"
              :project-group-id="queryParams.id"
              :report-documents="reportDocuments"
              :plan-documents="planDocuments"
              :weekly-documents="weeklyDocuments"
              :project-group-manager="projectGroupManager"

              :project-managers="projectManagers"
            />


            <!-- 项目通讯录 -->
            <ProjectAddressList
              :project-id="selectedProjectId"
              :project-group-id="queryParams.id"
              :project-group-name="queryParams.projectGropName"
              :project-group-code="queryParams.projectGropNumber"
              :project-name="selectedProjectName"
              :project-group-manager="projectGroupManager"

              :project-managers="projectManagers"
            />

            <!-- 项目成员 -->
            <ProjectMembers
              :id="selectedProjectId"
              :project-group-code="queryParams.projectGropNumber"
              :project-group-name="queryParams.projectGropName"
              :project-name="selectedProjectName"
              :project-code="selectedProjectCode"
              :project-group-id="queryParams.id"
              :project-group-manager="projectGroupManager"

              :project-managers="projectManagers"
            />

          </div>
        </el-col>
      </el-row>

      <!-- 右下角竖直锚点导航 -->
      <div class="corner-anchor">
        <el-anchor
          class="project-anchor"
          :offset="80"
          container=".project-overview-container"
          @change="handleAnchorChange"
          @click="handleAnchorClick"
        >
          <el-anchor-link href="#project-info" title="项目信息" />
          <el-anchor-link href="#project-items" title="项目条目" />
          <el-anchor-link href="#project-finance" title="财务情况" />
          <el-anchor-link href="#project-system" title="系统条目" />
          <el-anchor-link href="#project-stages" title="项目阶段" />
          <el-anchor-link href="#project-documents" title="项目文档" />
          <el-anchor-link href="#project-contacts" title="项目通讯录" />
          <el-anchor-link href="#project-members" title="项目成员" />
        </el-anchor>
      </div>

      <!-- 新增项目成员导航组件 -->
      <ProjectNavigation
        container=".project-overview-container"
        position="right"
        :project-group-id="queryParams.id"
        :project-group-name="queryParams.projectGropName"
        :project-group-code="queryParams.projectGropNumber"
        :selected-project-id="selectedProjectId"
        :project-group-manager="projectGroupManager"

        :project-managers="projectManagers"
        @anchor-change="handleSimpleNavChange"
        @anchor-click="handleSimpleNavClick"
      />
    </div>


  </ContentWrap>
</template>


<script setup lang="ts">
import {ref, onMounted, reactive, onBeforeUnmount, watch} from 'vue'
import { useRoute } from 'vue-router'
import ProjectInfo from './components/ProjectInfo.vue'
import ProjectMembers from './components/ProjectMembers.vue'
import ProjectStages from './components/ProjectStages.vue'
import ProjectDocuments from './components/ProjectDocuments.vue'
import ProjectFinance from './components/ProjectFinance.vue'
import ProjectNavigation from './components/ProjectNavigation.vue'
import ProjectSystem from './components/ProjectSystem.vue'
import ProjectAddressList from './components/ProjectAddressList.vue'
import emitter from '@/utils/eventBus'
import { ProjectGropApi } from '@/api/projectmanage/projectgrop'
import { ProjectApi, ProjectVO } from '@/api/projectmanage/project'
import {error} from "echarts/types/src/util/log";
import {ProjectPersonApi} from "@/api/projectmanage/projectperson";
import {CACHE_KEY, useCache} from "@/hooks/web/useCache";
import {useUserStoreWithOut} from "@/store/modules/user";
import StagePlan from "@/views/projectmanage/projectMain/detail/components/stages/StagePlan.vue";

// 定义组件名称
defineOptions({ name: 'ProjectOverview' })

// 类型定义
interface Project {
  id: string | number
  projectCode: string
  projectName: string
  contractId: string | number
  projectManagerName?: string
}

interface Document {
  id: string | number
  fileList?: any[]
  time?: string
  createTime?: string
  title?: string
  name?: string
  content?: string
  description?: string
}

// 获取路由参数
const route = useRoute()

// 状态定义
const projectId = ref<string>('')
const selectedProjectId = ref<string>('')
const selectedContractId = ref<string>('')
const selectedProjectCode = ref<string>('')
const selectedProjectName = ref<string>('')

const projectManagers = ref<Array<{id: string | number, projectManagerName: string}>>([]) // 保存所有项目的负责人信息
const reportDocuments = ref<Document[]>([])
const planDocuments = ref<Document[]>([])
const weeklyDocuments = ref<Document[]>([])
const containerRef = ref<HTMLElement | null>(null)
const projectList = ref<ProjectVO[]>([])  // 项目列表

const projectGroupManager = ref<string>('')

// 查询参数
const queryParams = reactive({
  id: '',
  projectGropNumber: '',
  projectGropName: ''
})

// 获取项目组信息，包括项目经理
const fetchProjectGroupInfo = async () => {
  try {
    // console.log('获取项目组信息，ID:', queryParams.id)
    if (!queryParams.id) return

    const data = await ProjectGropApi.getProjectGropStatus(Number(queryParams.id))
    // console.log('获取到项目组信息:', data)

    // 设置项目组经理
    if (data && data.projectManager) {
      if (Array.isArray(data.projectManager)) {
        projectGroupManager.value = data.projectManager.join(',')
      } else {
        projectGroupManager.value = data.projectManager
      }
      // console.log('项目组经理:', projectGroupManager.value)
    }
  } catch (error) {
    console.error('获取项目组信息失败:', error)
  }
}

// 获取项目组下的所有项目
const fetchProjects = async () => {
  if (!queryParams.projectGropNumber) {
    // console.log('项目组编号为空，无法获取项目列表')
    return
  }

  try {
    // console.log('获取项目组下的所有项目，项目组编号:', queryParams.projectGropNumber)

    // 使用 API 获取项目组下的项目
    const result = await ProjectApi.getProjectByGroupCode(queryParams.projectGropNumber)
    projectList.value = result || []

    // console.log('获取到项目列表:', projectList.value)

    // 提取所有项目的负责人信息
    projectManagers.value = projectList.value.map(project => ({
      id: project.id,
      projectManagerName: project.projectManagerName || ''
    }))
    // console.log('项目负责人信息:', projectManagers.value)



  } catch (error) {
    console.error('获取项目列表失败:', error)
    projectList.value = []
    projectManagers.value = []
  }
}



// 事件处理函数
const handleProjectItemClick = (row: Project) => {
  if (!row.contractId){
    return
  }
  selectedProjectId.value = String(row.id)
  selectedContractId.value = String(row.contractId)
  selectedProjectCode.value = String(row.projectCode)
  selectedProjectName.value = String(row.projectName)

}

const handleAnchorChange = (link: string) => {
  // console.log('当前锚点变化为:', link)
}

const handleAnchorClick = (e: Event, link: string) => {
  e.preventDefault()
  const targetElement = document.querySelector(link)
  if (targetElement) {
    targetElement.scrollIntoView({
      behavior: 'smooth',
      block: 'start'
    })
  }
}

const handleSimpleNavChange = (link: string) => {
  // console.log('导航链接变化:', link)
}

const handleSimpleNavClick = (e: Event, link: string) => {
  // console.log('导航链接点击:', link)
}

// 计算项目组ID (优先使用传入的projectGroupId，如果没有则从projectGroupCode获取)
const projectGroupId = computed(() => {
  return queryParams.id || queryParams.projectGropNumber || ''
})
// 初始化用户相关工具
const { wsCache } = useCache()
const userStore = useUserStoreWithOut()
// 获取当前用户信息
const getCurrentUsername = () => {
  const userInfo = wsCache.get(CACHE_KEY.USER)
  return userInfo?.user?.nickname || ''
}
// 定义项目成员权限状态
const isProjectMember = ref(false)
// 定义是否正在加载项目成员数据
const loadingMembers = ref(false)
// 获取项目组成员并检查当前用户是否为项目成员
const checkProjectMembership = async () => {
  if (!projectGroupId.value) {
    // console.log('项目组ID为空，无法获取项目成员')
    isProjectMember.value = false
    return
  }

  try {
    loadingMembers.value = true
    // console.log('获取项目组成员，项目组ID:', projectGroupId.value)

    // 调用API获取项目组下的所有成员
    const members = await ProjectPersonApi.getProjectPersonListByProjectGroupId(projectGroupId.value.toString())
    // console.log('获取到的项目组成员:', members)

    // 获取当前登录用户的用户名
    const currentUsername = getCurrentUsername()

    // 检查当前用户是否在项目成员列表中
    if (Array.isArray(members) && members.length > 0) {
      // 检查是否是项目成员
      isProjectMember.value = members.some(member => member.name === currentUsername)

      // 检查是否是超级管理员
      const isAdmin = currentUsername === '超级管理员'

      // 检查是否是项目组经理
      const isProjectGroupManager = currentUsername && projectGroupManager.value && projectGroupManager.value.includes(currentUsername)

      // 检查是否是项目负责人
      const isProjectManagerInList = projectManagers.value && projectManagers.value.some(
        manager => manager.projectManagerName === currentUsername
      )

      // 最终权限状态 = 是成员 OR 是管理员 OR 是项目组经理 OR 是项目负责人
      isProjectMember.value = isProjectMember.value || isAdmin || isProjectGroupManager || isProjectManagerInList

      console.log('当前用户是否为项目成员:', isProjectMember.value)
    } else {
      console.log('未找到项目成员数据或数据格式不正确')
      isProjectMember.value = false
    }
  } catch (error) {
    console.error('获取项目成员失败:', error)
    isProjectMember.value = false
  } finally {
    loadingMembers.value = false
  }
}




// 文档处理函数
const processDocuments = (data: Document[], type: 'report' | 'plan' | 'weekly') => {
  if (!Array.isArray(data)) return []

  return data.flatMap(item =>
    (item.fileList || []).map(file => ({
      ...file,
      [`${type}Id`]: item.id,
      [`${type}Time`]: item.time || item.createTime,
      [`${type}Title`]: item.title || item.name,
      [`${type}Content`]: item.content || item.description
    }))
  )
}

// 事件监听器设置
const setupEventListeners = () => {
  emitter.on('update-stage-report', (data: Document[]) => {
    reportDocuments.value = processDocuments(data, 'report')
  })

  emitter.on('update-stage-plan', (data: Document[]) => {
    planDocuments.value = processDocuments(data, 'plan')
  })

  emitter.on('update-stage-weekly', (data: Document[]) => {
    weeklyDocuments.value = processDocuments(data, 'weekly')
  })

  emitter.on('update-plan-documents', (data: Document[]) => {
    if (Array.isArray(data)) {
      planDocuments.value = data
    }
  })

  emitter.on('update-weekly-documents', (data: Document[]) => {
    if (Array.isArray(data)) {
      weeklyDocuments.value = data
    }
  })

  emitter.on('project-selected', (project: Project) => {
    selectedProjectId.value = String(project.id)
    selectedProjectCode.value = String(project.projectCode)
    selectedProjectName.value = String(project.projectName)

  })
}
watch(() => queryParams.id, () => {
  checkProjectMembership()
})
// 生命周期钩子
onMounted(() => {

  // 获取路由参数
  const id = route.params.id
  projectId.value = typeof id === 'string' ? id : (Array.isArray(id) ? id[0] : '')

  // 设置查询参数
  queryParams.id = route.query.id as string || route.params.id as string
  queryParams.projectGropNumber = route.query.projectGropNumber as string
  queryParams.projectGropName = decodeURIComponent(route.query.projectGropName as string)

  if (!projectId.value && !queryParams.id) {
    // console.error('未找到项目ID！')
  }



  setupEventListeners()

  // 获取项目组信息，包括项目经理
  fetchProjectGroupInfo()

  // 获取项目列表并设置项目经理名称
  fetchProjects()
})

onBeforeUnmount(() => {
  // 清理事件监听器
  emitter.off('update-stage-report')
  emitter.off('update-stage-plan')
  emitter.off('update-stage-weekly')
  emitter.off('update-plan-documents')
  emitter.off('update-weekly-documents')
  emitter.off('project-selected')
})
</script>

<style scoped>
.project-overview-container {
  padding: 10px;
  position: relative;
  height: calc(100vh - 120px);
  overflow-y: auto;
}

.project-anchor {
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 4px;
  padding: 10px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  width: 75px;
}

.corner-anchor {
  position: fixed;
  right: 30px;
  bottom: 30px;
  z-index: 999;
}

.content-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}
</style>
