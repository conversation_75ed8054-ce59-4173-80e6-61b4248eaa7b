package cn.iocoder.yudao.module.projectmanage.dal.mysql.projectmaterialprocurement;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.projectmanage.dal.dataobject.projectmaterialprocurement.ProjectMaterialProcurementDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.projectmanage.controller.admin.projectmaterialprocurement.vo.*;

/**
 * 物资采购审批 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProjectMaterialProcurementMapper extends BaseMapperX<ProjectMaterialProcurementDO> {

    default PageResult<ProjectMaterialProcurementDO> selectPage(ProjectMaterialProcurementPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ProjectMaterialProcurementDO>()
                .eqIfPresent(ProjectMaterialProcurementDO::getProjectId, reqVO.getProjectId())
                .eqIfPresent(ProjectMaterialProcurementDO::getFdTemplataId, reqVO.getFdTemplataId())
                .eqIfPresent(ProjectMaterialProcurementDO::getFlowId, reqVO.getFlowId())
                .eqIfPresent(ProjectMaterialProcurementDO::getDocCreator, reqVO.getDocCreator())
                .likeIfPresent(ProjectMaterialProcurementDO::getProjectName, reqVO.getProjectName())
                .eqIfPresent(ProjectMaterialProcurementDO::getDetail, reqVO.getDetail())
                .eqIfPresent(ProjectMaterialProcurementDO::getEstimateMoney, reqVO.getEstimateMoney())
                .eqIfPresent(ProjectMaterialProcurementDO::getStatus, reqVO.getStatus())
                .eqIfPresent(ProjectMaterialProcurementDO::getPriceComparison, reqVO.getPriceComparison())
                .eqIfPresent(ProjectMaterialProcurementDO::getIsAssets, reqVO.getIsAssets())
                .betweenIfPresent(ProjectMaterialProcurementDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ProjectMaterialProcurementDO::getId));
    }

}