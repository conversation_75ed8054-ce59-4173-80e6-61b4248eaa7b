package cn.iocoder.yudao.module.projectmanage.controller.admin.jixiao.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 部门经理绩效表 Response VO")
@Data
public class DepartmentPerformanceRespVO {

    @Schema(description = "项目人员ID", example = "789")
    private Long projectPersonId;

    @Schema(description = "项目ID", example = "123")
    private Long projectId;

    @Schema(description = "合同ID", example = "456")
    private Long contractId;

    @Schema(description = "月期", example = "2024-01")
    private String monthPeriod;

    @Schema(description = "项目名称", example = "项目管理系统")
    private String projectName;

    @Schema(description = "项目性质", example = "自主研发")
    private String projectNature;

    @Schema(description = "主办部门", example = "软件开发一部")
    private String managingDepartment;

    @Schema(description = "项目类别", example = "系统开发")
    private String projectType;

    @Schema(description = "类型", example = "收入")
    private String type;

    @Schema(description = "项目系数", example = "1.2")
    private String projectCoefficient;

    @Schema(description = "项目经理", example = "张三")
    private String projectManagerName;

    @Schema(description = "合同工期", example = "90")
    private String contractDuration;

    @Schema(description = "成员数量", example = "5")
    private Long memberCount;

    @Schema(description = "成员姓名", example = "李四")
    private String memberName;

    @Schema(description = "个人占比", example = "25.5")
    private String personalPercentage;

    @Schema(description = "重要性", example = "重要")
    private String importance;

    @Schema(description = "合同金额", example = "5000")
    private BigDecimal totalAmount;
    
    @Schema(description = "驳回原因", example = "绩效占比分配不合理")
    private String rejectReason;
} 