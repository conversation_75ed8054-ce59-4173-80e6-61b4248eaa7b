package cn.iocoder.yudao.module.projectmanage.service.jixiao;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import cn.iocoder.yudao.module.projectmanage.controller.admin.jixiao.vo.*;
import cn.iocoder.yudao.module.projectmanage.dal.dataobject.jixiao.JixiaoDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.projectmanage.dal.mysql.jixiao.JixiaoMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.projectmanage.enums.ErrorCodeConstants.*;

/**
 * 绩效考核 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class JixiaoServiceImpl implements JixiaoService {

    @Resource
    private JixiaoMapper jixiaoMapper;



    @Override
    public Long createJixiao(JixiaoSaveReqVO createReqVO) {
        // 插入
        JixiaoDO jixiao = BeanUtils.toBean(createReqVO, JixiaoDO.class);
        jixiaoMapper.insert(jixiao);
        // 返回
        return jixiao.getId();
    }

    @Override
    public void updateJixiao(JixiaoSaveReqVO updateReqVO) {
        // 校验存在
        validateJixiaoExists(updateReqVO.getId());
        // 更新
        JixiaoDO updateObj = BeanUtils.toBean(updateReqVO, JixiaoDO.class);
        jixiaoMapper.updateById(updateObj);
    }

    @Override
    public void deleteJixiao(Long id) {
        // 校验存在
        validateJixiaoExists(id);
        // 删除
        jixiaoMapper.deleteById(id);
    }

    private void validateJixiaoExists(Long id) {
        if (jixiaoMapper.selectById(id) == null) {
            throw exception(new ErrorCode(258, id+"绩效表不存在"));
        }
    }

    @Override
    public JixiaoDO getJixiao(Long id) {
        return jixiaoMapper.selectById(id);
    }

    @Override
    public PageResult<JixiaoDO> getJixiaoPage(JixiaoPageReqVO pageReqVO) {
        return jixiaoMapper.selectPage(pageReqVO);
    }

    @Override
    public List<DepartmentPerformanceRespVO> getDepartmentPerformanceData(DepartmentPerformanceReqVO reqVO) {
        return jixiaoMapper.selectDepartmentPerformanceData(reqVO);
    }

    @Override
    @Transactional
    public void confirmDepartmentPerformance(ConfirmDepartmentPerformanceReqVO reqVO) {
        // 1. 将数据插入jixiao表
        List<JixiaoDO> jixiaoList = convertToJixiaoList(reqVO);
        for (JixiaoDO jixiao : jixiaoList) {
            jixiaoMapper.insert(jixiao);
        }

        // 2. 更新projectmanage_project_person的manager_confirm为'确认'
        // 获取所有项目人员ID
        Set<Long> projectPersonIds = reqVO.getData().stream()
                .map(DepartmentPerformanceRespVO::getProjectPersonId)
                .collect(java.util.stream.Collectors.toSet());

        jixiaoMapper.updateProjectPersonManagerConfirmByIds(projectPersonIds);
    }

    @Override
    @Transactional
    public void rejectDepartmentPerformance(RejectDepartmentPerformanceReqVO reqVO) {
        // 获取被驳回的项目ID集合
        Set<Long> projectIds = reqVO.getData().stream()
                .map(DepartmentPerformanceRespVO::getProjectId)
                .collect(java.util.stream.Collectors.toSet());

        // 按项目级别驳回 - 同一个项目的所有员工都会被驳回，状态设为'驳回'
        jixiaoMapper.rejectProjectPersonByProjectIds(
                reqVO.getMonthPeriod(),
                projectIds,
                reqVO.getRejectReason()
        );
    }

    @Override
    @Transactional
    public void revokeRejectDepartmentPerformance(RevokeRejectDepartmentPerformanceReqVO reqVO) {
        // 获取要撤销驳回的项目ID集合
        Set<Long> projectIds = reqVO.getData().stream()
                .map(DepartmentPerformanceRespVO::getProjectId)
                .collect(java.util.stream.Collectors.toSet());

        // 按项目级别撤销驳回 - 将项目状态重新设为'未确认'，等待重新审批
        jixiaoMapper.revokeRejectProjectPersonByProjectIds(
                reqVO.getMonthPeriod(),
                projectIds
        );
    }

    /**
     * 将部门绩效数据转换为JixiaoDO列表
     */
    private List<JixiaoDO> convertToJixiaoList(ConfirmDepartmentPerformanceReqVO reqVO) {
        List<JixiaoDO> result = new ArrayList<>();

        for (DepartmentPerformanceRespVO data : reqVO.getData()) {
            JixiaoDO jixiao = new JixiaoDO();

            // ID字段映射
            jixiao.setProjectId(data.getProjectId());
            jixiao.setContractId(data.getContractId());

            // 基本字段映射
            jixiao.setYearmonth(data.getMonthPeriod());
            jixiao.setProjectName(data.getProjectName());
            jixiao.setProjectNature(data.getProjectNature());
            jixiao.setProjectType(data.getProjectType());
            jixiao.setProjectCoefficient(data.getProjectCoefficient());
            jixiao.setProjectManage(data.getProjectManagerName());
            jixiao.setContractWorkday(data.getContractDuration());
            jixiao.setPersonNum(data.getMemberCount());
            jixiao.setProjectPerson(data.getMemberName());
            jixiao.setProjectProportions(data.getPersonalPercentage());

            // 在备注中记录主办部门和类型信息
            String remarks = "主办部门:" + data.getManagingDepartment();
            if (data.getType() != null && !data.getType().isEmpty()) {
                remarks += ",类型:" + data.getType();
            }
            jixiao.setRemarks(remarks);
            jixiao.setProjectWorkday(null);
            jixiao.setSeasonWorkday(null);
            jixiao.setSeasonAllocate(null);
            jixiao.setSeasonPersonallocate(null);
            jixiao.setAnnualWorkday(null);
            jixiao.setAnnualAllocate(null);
            jixiao.setAnnualPersonallocate(null);
            jixiao.setExWorkday(null);
            jixiao.setDeductions(null);

            result.add(jixiao);
        }

        return result;
    }

}