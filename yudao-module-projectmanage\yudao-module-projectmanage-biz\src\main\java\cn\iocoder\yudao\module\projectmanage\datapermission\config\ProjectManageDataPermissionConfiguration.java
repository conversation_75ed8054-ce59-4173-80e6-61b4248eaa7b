package cn.iocoder.yudao.module.projectmanage.datapermission.config;

import cn.iocoder.yudao.framework.datapermission.core.rule.dept.DeptDataPermissionRuleCustomizer;
import cn.iocoder.yudao.module.projectmanage.dal.dataobject.meeting.LeadersDO;
import cn.iocoder.yudao.module.projectmanage.dal.dataobject.meeting.MeetingDO;
import cn.iocoder.yudao.module.projectmanage.dal.dataobject.project.projectperson.ProjectPersonDO;
import cn.iocoder.yudao.module.projectmanage.dal.dataobject.projectgrop.ProjectGropDO;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 劳务 模块的数据权限 Configuration
 *
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
public class ProjectManageDataPermissionConfiguration {

    @Bean
    public DeptDataPermissionRuleCustomizer ProjectManageDataPermissionRuleCustomizer() {
        return rule -> {
            // 添加那个where  dept_id =
            rule.addDeptColumn(MeetingDO.class);
            rule.addDeptColumn(LeadersDO.class);
            rule.addDeptColumn(ProjectGropDO.class);
        };
    }

}
