package cn.iocoder.yudao.module.projectmanage.controller.admin.projectmaterialprocurement.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 物资采购审批新增/修改 Request VO")
@Data
public class ProjectMaterialProcurementSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "32742")
    private Long id;

    @Schema(description = "项目id", example = "31102")
    private Long projectId;

    @Schema(description = "模版id", example = "18503")
    private String fdTemplataId;

    @Schema(description = "流程id", example = "12756")
    private String flowId;

    @Schema(description = "发起人")
    private String docCreator;

    @Schema(description = "项目名称", example = "芋艿")
    private String projectName;

    @Schema(description = "具体内容")
    private String detail;

    @Schema(description = "预算金额")
    private BigDecimal estimateMoney;

    @Schema(description = "比价邀标情况")
    private String priceComparison;

    @Schema(description = "审批状态", example = "1")
    private String status;

    @Schema(description = "是否列入固定资产")
    private String isAssets;

}