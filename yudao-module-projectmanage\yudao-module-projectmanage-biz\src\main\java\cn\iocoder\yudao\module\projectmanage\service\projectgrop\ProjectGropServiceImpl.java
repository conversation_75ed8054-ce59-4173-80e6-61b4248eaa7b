package cn.iocoder.yudao.module.projectmanage.service.projectgrop;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;
import cn.iocoder.yudao.framework.security.core.LoginUser;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.projectmanage.controller.admin.fee.vo.FeeSaveReqVO;
import cn.iocoder.yudao.module.projectmanage.controller.admin.project.vo.ProjectSaveReqVO;
import cn.iocoder.yudao.module.projectmanage.controller.admin.project.vo.ProjectWithFeesRespVO;
import cn.iocoder.yudao.module.projectmanage.dal.dataobject.feedetail.FeeDetailDO;
import cn.iocoder.yudao.module.projectmanage.dal.mysql.feedetail.FeeDetailMapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.Collections;

import cn.iocoder.yudao.module.projectmanage.controller.admin.projectgrop.vo.*;
import cn.iocoder.yudao.module.projectmanage.dal.dataobject.projectgrop.ProjectGropDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.projectmanage.dal.mysql.projectgrop.ProjectGropMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 项目组 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class ProjectGropServiceImpl implements ProjectGropService {

    @Resource
    private ProjectGropMapper projectGropMapper;

    @Resource
    private FeeDetailMapper feeDetailMapper;

    @Override
    @Transactional
    public synchronized String generateProjectGropNumber() {
        // 获取当前年份
        String yearPrefix = String.format("G%s", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy")));
        // 查询当前年份最大编号
        String maxNumber = projectGropMapper.getMaxProjectGropNumberByYear(yearPrefix);

        int sequence;
        if (maxNumber == null) {
            sequence = 1;
        } else {
            // 提取序号部分并加1
            sequence = Integer.parseInt(maxNumber.substring(yearPrefix.length())) + 1;
        }

        // 格式化新编号
        return String.format("%s%03d", yearPrefix, sequence);
    }
    @Override
    @Transactional
    public synchronized String generateAppointProjectGropNumber(Long year) {
        // 获取当前年份
        String yearPrefix = String.format("G%s",String.valueOf(year));
        // 查询当前年份最大编号

        String maxNumber = projectGropMapper.getMaxProjectGropNumberByYear(yearPrefix);

        int sequence;
        if (maxNumber == null) {
            sequence = 1;
        } else {
            // 提取序号部分并加1
            sequence = Integer.parseInt(maxNumber.substring(yearPrefix.length())) + 1;
        }

        // 格式化新编号
        return String.format("%s%03d", yearPrefix, sequence);
    }
    @Override
    @Transactional
    public Long createProjectGrop(ProjectGropSaveReqVO createReqVO) {
        // 校验项目组编号是否已存在
        if (projectGropMapper.selectByProjectGropNumber(createReqVO.getProjectGropNumber()) != null) {
            // 如果已存在，重新生成新编号
            createReqVO.setProjectGropNumber(generateProjectGropNumber());
        }

        // 插入项目组
        ProjectGropDO projectGrop = BeanUtils.toBean(createReqVO, ProjectGropDO.class);
        projectGropMapper.insert(projectGrop);
        // 返回 id
        return projectGrop.getId();
    }

    @Override
    public void updateProjectGrop(ProjectGropSaveReqVO updateReqVO) {
        // 校验存在
        validateProjectGropExists(updateReqVO.getId());
        // 更新
        ProjectGropDO updateObj = BeanUtils.toBean(updateReqVO, ProjectGropDO.class);
        projectGropMapper.updateById(updateObj);
    }

    @Override
    public void deleteProjectGrop(Long id) {
        // 校验存在
        validateProjectGropExists(id);
        // 删除
        projectGropMapper.deleteById(id);
    }

    private void validateProjectGropExists(Long id) {
        if (projectGropMapper.selectById(id) == null) {
            throw exception(new ErrorCode(222, id + ",demo不存在"));
        }
    }

    @Override
    public ProjectGropDO getProjectGrop(Long id) {
        return projectGropMapper.selectById(id);
    }

    @Override
    public PageResult<ProjectGropDO> getProjectGropPage(ProjectGropPageReqVO pageReqVO) {
        PageResult<ProjectGropDO> pageResult;

        // 获取当前登录用户信息
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Long userId = SecurityFrameworkUtils.getLoginUserId();

        // 根据用户权限进行查询
        if (loginUser != null && loginUser.getInfo() != null && userId != null) {
            // 获取用户角色信息

            List<String> roleNames = projectGropMapper.selectUserRoleNamesByUserId(userId);
            boolean isProjectPersonRole = roleNames != null && roleNames.contains("项目人员");
            boolean hasManagerRole = roleNames != null && roleNames.stream().anyMatch(role -> role.contains("经理"));

            // 获取用户昵称和部门ID
            String loginUserName = loginUser.getInfo().get("nickname");
            Long userDeptId = SecurityFrameworkUtils.getLoginUserDeptId();

            // 如果用户既有项目人员角色又有经理角色，或者只有经理角色但没有项目人员角色
            if (hasManagerRole) {
                // 优先使用部门ID查询
                if (userDeptId != null) {
                    // 经理通过部门ID查询
                    Map<String, Object> params = new HashMap<>();
                    params.put("reqVO", pageReqVO);
                    params.put("loginUserName", loginUserName); // 仍然保留用户名，便于查询自己参与的项目
                    params.put("userDeptId", userDeptId); // 用于查询部门下所有项目
                    params.put("offset", (pageReqVO.getPageNo() - 1) * pageReqVO.getPageSize());
                    params.put("isManager", true); // 标记是经理角色查询

                    // 使用带权限过滤的查询
                    System.out.println("使用带权限过滤的查询");
                    List<ProjectGropDO> list = projectGropMapper.selectPageByPermission(params);
                    Long total = projectGropMapper.selectCountByPermission(params);
                    pageResult = new PageResult<>(list, total);
                } else {
                    // 部门ID为空，降级为普通查询
                    if (isProjectPersonRole) {
                        // 如果也是项目人员，则查询参与的项目
                        Map<String, Object> params = new HashMap<>();
                        params.put("reqVO", pageReqVO);
                        params.put("loginUserName", loginUserName);
                        params.put("offset", (pageReqVO.getPageNo() - 1) * pageReqVO.getPageSize());

                        // 使用带权限过滤的查询
                        System.out.println("isProjectPersonRole使用带权限过滤的查询");
                        List<ProjectGropDO> list = projectGropMapper.selectPageByPermission(params);
                        Long total = projectGropMapper.selectCountByPermission(params);
                        pageResult = new PageResult<>(list, total);
                    } else {
                        System.out.println("经理但没有部门ID，也不是项目人员，显示所有数据");
                        // 经理但没有部门ID，也不是项目人员，显示所有数据
                        pageResult = projectGropMapper.selectPage(pageReqVO);
                    }
                }
            }
            // 只有项目人员角色，则查询参与的项目
            else if (isProjectPersonRole) {
                // 检查用户是否有权限约束（是否在任何项目组的manager中或是否在项目人员表中）
                boolean hasPermissionRestriction = checkUserHasPermissionRestriction(loginUserName);

                if (hasPermissionRestriction) {
                    // 用户有权限约束，只显示相关数据
                    // 计算分页偏移量
                    Map<String, Object> params = new HashMap<>();
                    params.put("reqVO", pageReqVO);
                    params.put("loginUserName", loginUserName);
                    params.put("offset", (pageReqVO.getPageNo() - 1) * pageReqVO.getPageSize());

                    // 使用带权限过滤的查询
                    List<ProjectGropDO> list = projectGropMapper.selectPageByPermission(params);
                    Long total = projectGropMapper.selectCountByPermission(params);
                    pageResult = new PageResult<>(list, total);
                } else {
                    // 用户既不在manager中也不在项目人员表中，不展示数据
                    pageResult = new PageResult<>(Collections.emptyList(), 0L);
                }
            } else {
                System.out.println("既不是项目人员也不是经理，显示所有数据");
                // 既不是项目人员也不是经理，显示所有数据（超级管理员等）
                // 检查是否有项目成员搜索条件，如果有则使用XML查询支持项目成员筛选
                if (pageReqVO.getProjectMemberName() != null && !pageReqVO.getProjectMemberName().trim().isEmpty()) {
                    // 使用XML查询方法支持项目成员搜索
                    Map<String, Object> params = new HashMap<>();
                    params.put("reqVO", pageReqVO);
                    params.put("offset", (pageReqVO.getPageNo() - 1) * pageReqVO.getPageSize());

                    List<ProjectGropDO> list = projectGropMapper.selectPageByPermission(params);
                    Long total = projectGropMapper.selectCountByPermission(params);
                    pageResult = new PageResult<>(list, total);
                } else {
                    // 没有项目成员搜索条件，使用默认查询
                    pageResult = projectGropMapper.selectPage(pageReqVO);
                }
            }
        } else {
            System.out.println("未登录或无法获取用户信息，使用默认查询");
            // 未登录或无法获取用户信息，使用默认查询
            pageResult = projectGropMapper.selectPage
                    (pageReqVO);
        }

        // 批量获取所有项目组的合同金额
        for (ProjectGropDO projectGrop : pageResult.getList()) {
            // 获取合同金额
            Map<String, BigDecimal> contractAmounts = getContractAmounts(projectGrop.getProjectGropNumber());
            projectGrop.setReceivable(contractAmounts.get("receivableAmount"));
            projectGrop.setPayable(contractAmounts.get("payableAmount"));

            // 注意：receivedAmount和paidAmount直接从项目组表获取，不在这里设置
            // 这部分在Controller层处理
        }

        return pageResult;
    }

    //项目经理为空
    @Override
    public PageResult<ProjectGropDO> getProjectGropPageAndManagerIsNull(ProjectGropPageReqVO pageReqVO) {
        PageResult<ProjectGropDO> pageResult;

        // 获取当前登录用户信息
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Long userId = SecurityFrameworkUtils.getLoginUserId();

        // 根据用户权限进行查询
        if (loginUser != null && loginUser.getInfo() != null && userId != null) {
            // 获取用户角色信息

            List<String> roleNames = projectGropMapper.selectUserRoleNamesByUserId(userId);
            boolean isProjectPersonRole = roleNames != null && roleNames.contains("项目人员");
            boolean hasManagerRole = roleNames != null && roleNames.stream().anyMatch(role -> role.contains("经理"));

            // 获取用户昵称和部门ID
            String loginUserName = loginUser.getInfo().get("nickname");
            Long userDeptId = SecurityFrameworkUtils.getLoginUserDeptId();

            // 如果用户既有项目人员角色又有经理角色，或者只有经理角色但没有项目人员角色
            if (hasManagerRole) {
                // 优先使用部门ID查询
                if (userDeptId != null) {
                    // 经理通过部门ID查询
                    Map<String, Object> params = new HashMap<>();
                    params.put("reqVO", pageReqVO);
                    params.put("loginUserName", loginUserName); // 仍然保留用户名，便于查询自己参与的项目
                    params.put("userDeptId", userDeptId); // 用于查询部门下所有项目
                    params.put("offset", (pageReqVO.getPageNo() - 1) * pageReqVO.getPageSize());
                    params.put("isManager", true); // 标记是经理角色查询

                    // 使用带权限过滤的查询
                    System.out.println("使用带权限过滤的查询");
                    List<ProjectGropDO> list = projectGropMapper.selectPageByPermissionAndManagerIsNull(params);
                    Long total = projectGropMapper.selectCountByPermissionAndManagerIsNull(params);
                    pageResult = new PageResult<>(list, total);
                } else {
                    // 部门ID为空，降级为普通查询
                    if (isProjectPersonRole) {
                        // 如果也是项目人员，则查询参与的项目
                        Map<String, Object> params = new HashMap<>();
                        params.put("reqVO", pageReqVO);
                        params.put("loginUserName", loginUserName);
                        params.put("offset", (pageReqVO.getPageNo() - 1) * pageReqVO.getPageSize());

                        // 使用带权限过滤的查询
                        System.out.println("isProjectPersonRole使用带权限过滤的查询");
                        List<ProjectGropDO> list = projectGropMapper.selectPageByPermissionAndManagerIsNull(params);
                        Long total = projectGropMapper.selectCountByPermissionAndManagerIsNull(params);
                        pageResult = new PageResult<>(list, total);
                    } else {
                        System.out.println("经理但没有部门ID，也不是项目人员，显示所有数据");
                        // 经理但没有部门ID，也不是项目人员，显示所有数据
                        pageResult = projectGropMapper.selectPageByManagerIsNullOrEmpty(pageReqVO);
                    }
                }
            }
            // 只有项目人员角色，则查询参与的项目
            else if (isProjectPersonRole) {
                // 检查用户是否有权限约束（是否在任何项目组的manager中或是否在项目人员表中）
                boolean hasPermissionRestriction = checkUserHasPermissionRestriction(loginUserName);

                if (hasPermissionRestriction) {
                    // 用户有权限约束，只显示相关数据
                    // 计算分页偏移量
                    Map<String, Object> params = new HashMap<>();
                    params.put("reqVO", pageReqVO);
                    params.put("loginUserName", loginUserName);
                    params.put("offset", (pageReqVO.getPageNo() - 1) * pageReqVO.getPageSize());

                    // 使用带权限过滤的查询
                    List<ProjectGropDO> list = projectGropMapper.selectPageByPermissionAndManagerIsNull(params);
                    Long total = projectGropMapper.selectCountByPermissionAndManagerIsNull(params);
                    pageResult = new PageResult<>(list, total);
                } else {
                    // 用户既不在manager中也不在项目人员表中，不展示数据
                    pageResult = new PageResult<>(Collections.emptyList(), 0L);
                }
            } else {
                System.out.println("既不是项目人员也不是经理，显示所有数据");
                // 既不是项目人员也不是经理，显示所有数据（超级管理员等）
                // 检查是否有项目成员搜索条件，如果有则使用XML查询支持项目成员筛选
                if (pageReqVO.getProjectMemberName() != null && !pageReqVO.getProjectMemberName().trim().isEmpty()) {
                    // 使用XML查询方法支持项目成员搜索
                    Map<String, Object> params = new HashMap<>();
                    params.put("reqVO", pageReqVO);
                    params.put("offset", (pageReqVO.getPageNo() - 1) * pageReqVO.getPageSize());

                    List<ProjectGropDO> list = projectGropMapper.selectPageByPermissionAndManagerIsNull(params);
                    Long total = projectGropMapper.selectCountByPermissionAndManagerIsNull(params);
                    pageResult = new PageResult<>(list, total);
                } else {
                    // 没有项目成员搜索条件，使用默认查询
                    pageResult = projectGropMapper.selectPageByManagerIsNullOrEmpty(pageReqVO);
                }
            }
        } else {
            System.out.println("未登录或无法获取用户信息，使用默认查询");
            // 未登录或无法获取用户信息，使用默认查询
            pageResult = projectGropMapper.selectPageByManagerIsNullOrEmpty(pageReqVO);
        }

        // 批量获取所有项目组的合同金额
        for (ProjectGropDO projectGrop : pageResult.getList()) {
            // 获取合同金额
            Map<String, BigDecimal> contractAmounts = getContractAmounts(projectGrop.getProjectGropNumber());
            projectGrop.setReceivable(contractAmounts.get("receivableAmount"));
            projectGrop.setPayable(contractAmounts.get("payableAmount"));

            // 注意：receivedAmount和paidAmount直接从项目组表获取，不在这里设置
            // 这部分在Controller层处理
        }

        return pageResult;
    }

    /**
     * 检查用户是否为"项目人员"角色
     *
     * @param userId 用户ID
     * @return 是否为项目人员角色
     */
    private boolean isUserProjectPersonRole(Long userId) {
        List<String> roleNames = projectGropMapper.selectUserRoleNamesByUserId(userId);
        return roleNames != null && roleNames.contains("项目人员");
    }

    /**
     * 检查用户是否拥有包含"经理"字样的角色
     *
     * @param userId 用户ID
     * @return 是否有包含"经理"字样的角色
     */
    private boolean isUserManagerRole(Long userId) {
        List<String> roleNames = projectGropMapper.selectUserRoleNamesByUserId(userId);
        if (roleNames == null || roleNames.isEmpty()) {
            return false;
        }
        // 检查是否有包含"经理"字样的角色
        return roleNames.stream().anyMatch(role -> role.contains("经理"));
    }

    /**
     * 检查用户是否同时具有"项目人员"角色和包含"经理"字样的角色
     *
     * @param userId 用户ID
     * @return 是否同时具有两种角色
     */
    private boolean hasProjectPersonAndManagerRoles(Long userId) {
        List<String> roleNames = projectGropMapper.selectUserRoleNamesByUserId(userId);
        if (roleNames == null || roleNames.isEmpty()) {
            return false;
        }
        // 检查是否同时有"项目人员"角色和包含"经理"字样的角色
        return roleNames.contains("项目人员") &&
               roleNames.stream().anyMatch(role -> role.contains("经理"));
    }

    @Override
    public boolean checkIsDepartmentManager(Long userId) {
        try {
            // 获取用户的角色标识列表
            List<String> roleCodes = projectGropMapper.selectUserRoleCodesByUserId(userId);
            if (roleCodes == null || roleCodes.isEmpty()) {
                return false;
            }

            // 检查是否具有部门经理角色标识
            // yb_manager: 软件开发一部经理
            // eb_manager: 软件开发二部经理
            // jc_manager: 系统集成部经理
            // super_admin: 超级管理员
            return roleCodes.contains("yb_manager") ||
                   roleCodes.contains("eb_manager") ||
                   roleCodes.contains("jc_manager") ||
                   roleCodes.contains("super_admin");
        } catch (Exception e) {
            log.error("检查用户是否为部门经理失败，userId: {}", userId, e);
            return false;
        }
    }

    @Override
    public List<ProjectSaveReqVO> getProjectsByGroupCode(String projectGroupCode) {
        return projectGropMapper.selectProjectsByGroupCode(projectGroupCode);
    }

    @Override
    public List<FeeSaveReqVO> getFeesByGroupCode(String projectGroupCode) {
        return projectGropMapper.selectFeesByGroupCode(projectGroupCode);
    }

    @Override
    public List<FeeSaveReqVO> getFeesByProjectCode(String projectCode) {
        return projectGropMapper.selectFeesByProjectCode(projectCode);
    }

    @Override
    public List<FeeSaveReqVO> getFeeByContractId(String contractId) {
        return projectGropMapper.selectFeeByContractId(contractId);
    }

    @Override
    public List<ProjectWithFeesRespVO> getReceivableProjects(String projectGropNumber) {
        return projectGropMapper.selectReceivableProjects(projectGropNumber);
    }

    @Override
    public ProjectGropDO getProjectGropByNumber(String projectGropNumber) {
        // 调用 Mapper 方法根据项目组编号获取项目组详情
        return projectGropMapper.selectByProjectGropNumber(projectGropNumber);
    }

    @Override
    public List<ProjectWithFeesRespVO> getPayableProjects(String projectGropNumber) {
        return projectGropMapper.selectPayableProjects(projectGropNumber);
    }

    @Override
    public Map<String, BigDecimal> getContractAmounts(String projectGropNumber) {
        // 优先考虑项目的收付款金额
        Map<String, BigDecimal> amounts = projectGropMapper.selectProjectAmounts(projectGropNumber);
        if (amounts == null) {
            amounts = projectGropMapper.selectContractAmounts(projectGropNumber);
        }
        if (amounts == null) {
            amounts = new HashMap<>();
            amounts.put("receivableAmount", BigDecimal.ZERO);
            amounts.put("payableAmount", BigDecimal.ZERO);
        }
        return amounts;
    }

    @Override
    public Map<String, BigDecimal> getPaymentAmounts(String projectGropNumber) {
        Map<String, BigDecimal> result = new HashMap<>();

        // 查询开票总额
        BigDecimal invAmount = new LambdaQueryChainWrapper<>(feeDetailMapper)
                .eq(FeeDetailDO::getGroupCode, projectGropNumber)
                .eq(FeeDetailDO::getYwType, "开票")
                .select(FeeDetailDO::getDetailAmount)
                .list()
                .stream()
                .map(FeeDetailDO::getDetailAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 查询收票总额
        BigDecimal invPayAmount = new LambdaQueryChainWrapper<>(feeDetailMapper)
                .eq(FeeDetailDO::getGroupCode, projectGropNumber)
                .eq(FeeDetailDO::getYwType, "收票")
                .select(FeeDetailDO::getDetailAmount)
                .list()
                .stream()
                .map(FeeDetailDO::getDetailAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        result.put("invAmount", invAmount);
        result.put("invPayAmount", invPayAmount);

        return result;
    }

    @Override
    public List<Map<String, Object>> getProjectSystemList(Long projectGropId) {
        return projectGropMapper.selectProjectSystemList(projectGropId);
    }


    public PageResult<ProjectGropDO> selectProjectListByYear(String year,ProjectGropPageReqVO pageReqVO) {
        // 计算分页偏移量
        Map<String, Object> params = new HashMap<>();
        params.put("year", year);
        params.put("pageSize", pageReqVO.getPageSize());
        params.put("offset", (pageReqVO.getPageNo() - 1) * pageReqVO.getPageSize());
        // 统计年份的记录数
        Long total = (long) projectGropMapper.selectGropListByYear(params).size();
        // 查询出项目组数据
        List<ProjectGropDO> list = projectGropMapper.selectGropListByYear(params);
        return new PageResult<>(list, total);
    }

    @Override
    public PageResult<ProjectGropDO> getProjectGropByDepartment(String department, ProjectGropPageReqVO pageReqVO) {
        // 计算分页偏移量
        Map<String, Object> params = new HashMap<>();
        params.put("department", department);
        params.put("pageSize", pageReqVO.getPageSize());
        params.put("offset", (pageReqVO.getPageNo() - 1) * pageReqVO.getPageSize());

        // 查询总记录数
        Long total = projectGropMapper.selectCountByDepartment(department);

        // 分页查询项目组数据
        List<ProjectGropDO> list = projectGropMapper.selectPageByDepartment(params);

        // 批量获取所有项目组的合同金额
        for (ProjectGropDO projectGrop : list) {
            // 获取合同金额
            Map<String, BigDecimal> contractAmounts = getContractAmounts(projectGrop.getProjectGropNumber());
            projectGrop.setReceivable(contractAmounts.get("receivableAmount"));
            projectGrop.setPayable(contractAmounts.get("payableAmount"));
        }

        return new PageResult<>(list, total);
    }

    /**
     * 检查用户是否有权限约束
     *
     * @param loginUserName 登录用户名
     * @return 是否有权限约束
     */
    private boolean checkUserHasPermissionRestriction(String loginUserName) {
        if (loginUserName == null || loginUserName.isEmpty()) {
            return false;
        }

        // 检查用户是否在任何项目组的manager中
        Integer managerCount = projectGropMapper.countProjectGropByManager(loginUserName);
        if (managerCount != null && managerCount > 0) {
            return true;
        }

        // 检查用户是否在项目人员表中
        Integer personCount = projectGropMapper.countProjectPersonByName(loginUserName);
        if (personCount != null && personCount > 0) {
            return true;
        }

        return false;
    }

    @Override
    public BigDecimal calculateProjectCoefficient(Integer deptId, String nature, String importance, BigDecimal receivable, List<String> type) {
        // 检查是否为支持的部门(199、201、203)
        if (deptId == null || (deptId != 199 && deptId != 201 && deptId != 203)) {
            return BigDecimal.ZERO; // 非支持部门返回0
        }

        // 检查必要参数
        if (nature == null || importance == null || receivable == null || type == null || type.isEmpty()) {
            return BigDecimal.ZERO;
        }

        // 获取项目组类型的第一个值
        String originalType = type.get(0);

        // 根据性质和类型映射到实际的算法类型
        String mappedType = mapProjectType(nature, originalType, deptId);

        // 将金额转换为万元
        BigDecimal amountInWan = receivable.divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP);

        // 系统集成部使用不同的计算逻辑
        if (deptId == 203) {
            return calculateSystemIntegrationCoefficient(nature, mappedType, amountInWan);
        }

        // 软件开发一部和二部的计算逻辑
        if ("项目建设类".equals(nature) || "项目建设".equals(nature)) {
            return calculateProjectConstructionCoefficient(importance, mappedType, amountInWan);
        } else if ("运维类".equals(nature) || "运维服务".equals(nature)) {
            return calculateMaintenanceCoefficient(mappedType, amountInWan);
        }

        return BigDecimal.ZERO;
    }

    /**
     * 根据项目组性质和类型映射到实际的算法类型
     */
    private String mapProjectType(String nature, String originalType, Integer deptId) {
        // 研发项目保持不变
        if ("研发项目".equals(originalType)) {
            return "研发项目";
        }

        // 根据部门ID和性质类型组合映射
        if (deptId == 203) {
            // 系统集成部的映射逻辑
            if ("项目建设类".equals(nature) || "项目建设".equals(nature)) {
                if ("自主建设".equals(originalType) || "自主实施".equals(originalType)) {
                    return "自主实施";
                } else if ("合作建设".equals(originalType) || "合作实施".equals(originalType)) {
                    return "合作实施";
                } else if ("委外建设".equals(originalType) || "委外实施".equals(originalType)) {
                    return "委外实施";
                }
            } else if ("运维类".equals(nature) || "运维服务".equals(nature)) {
                if ("自主建设".equals(originalType) || "自主实施".equals(originalType)) {
                    return "自主运维";
                } else if ("合作建设".equals(originalType) || "合作实施".equals(originalType)) {
                    return "合作运维";
                } else if ("委外建设".equals(originalType) || "委外实施".equals(originalType)) {
                    return "委外运维";
                }
            }
        } else {
            // 软件开发一部和二部的映射逻辑
            if ("项目建设类".equals(nature) || "项目建设".equals(nature)) {
                if ("自主建设".equals(originalType)) {
                    return "自主开发";
                } else if ("合作建设".equals(originalType)) {
                    return "合作开发";
                } else if ("委外建设".equals(originalType)) {
                    return "委外开发";
                }
            } else if ("运维类".equals(nature) || "运维服务".equals(nature)) {
                if ("自主建设".equals(originalType)) {
                    return "自主运维";
                } else if ("合作建设".equals(originalType)) {
                    return "合作运维";
                } else if ("委外建设".equals(originalType)) {
                    return "委外运维";
                }
            }
        }

        // 如果没有匹配到，返回原始类型
        return originalType;
    }

    /**
     * 计算项目建设类系数
     */
    private BigDecimal calculateProjectConstructionCoefficient(String importance, String projectType, BigDecimal amountInWan) {
        // 研发项目特殊处理
        if ("研发项目".equals(projectType)) {
            if ("重点项目".equals(importance)) {
                return new BigDecimal("5.000");
            } else if ("一般项目".equals(importance)) {
                return new BigDecimal("3.000");
            }
            return BigDecimal.ZERO;
        }

        // 根据重要性和项目类型计算系数
        if ("重点项目".equals(importance)) {
            return getConstructionCoefficientForImportant(projectType, amountInWan);
        } else if ("一般项目".equals(importance)) {
            return getConstructionCoefficientForGeneral(projectType, amountInWan);
        }

        return BigDecimal.ZERO;
    }

    /**
     * 重点项目建设类系数
     */
    private BigDecimal getConstructionCoefficientForImportant(String projectType, BigDecimal amountInWan) {
        if ("自主开发".equals(projectType)) {
            if (amountInWan.compareTo(new BigDecimal("200")) >= 0) {
                return new BigDecimal("6.000");
            } else if (amountInWan.compareTo(new BigDecimal("80")) >= 0) {
                return new BigDecimal("4.500");
            } else if (amountInWan.compareTo(new BigDecimal("10")) >= 0) {
                return new BigDecimal("3.000");
            } else {
                return new BigDecimal("1.500");
            }
        } else if ("合作开发".equals(projectType)) {
            if (amountInWan.compareTo(new BigDecimal("200")) >= 0) {
                return new BigDecimal("4.500");
            } else if (amountInWan.compareTo(new BigDecimal("80")) >= 0) {
                return new BigDecimal("3.375");
            } else if (amountInWan.compareTo(new BigDecimal("10")) >= 0) {
                return new BigDecimal("2.250");
            } else {
                return new BigDecimal("1.125");
            }
        } else if ("委外开发".equals(projectType)) {
            if (amountInWan.compareTo(new BigDecimal("200")) >= 0) {
                return new BigDecimal("3.000");
            } else if (amountInWan.compareTo(new BigDecimal("80")) >= 0) {
                return new BigDecimal("2.250");
            } else if (amountInWan.compareTo(new BigDecimal("10")) >= 0) {
                return new BigDecimal("1.500");
            } else {
                return new BigDecimal("0.750");
            }
        }
        return BigDecimal.ZERO;
    }

    /**
     * 一般项目建设类系数
     */
    private BigDecimal getConstructionCoefficientForGeneral(String projectType, BigDecimal amountInWan) {
        if ("自主开发".equals(projectType)) {
            if (amountInWan.compareTo(new BigDecimal("200")) >= 0) {
                return new BigDecimal("6.000");
            } else if (amountInWan.compareTo(new BigDecimal("80")) >= 0) {
                return new BigDecimal("4.500");
            } else if (amountInWan.compareTo(new BigDecimal("10")) >= 0) {
                return new BigDecimal("3.000");
            } else {
                return new BigDecimal("1.500");
            }
        } else if ("合作开发".equals(projectType)) {
            if (amountInWan.compareTo(new BigDecimal("200")) >= 0) {
                return new BigDecimal("4.500");
            } else if (amountInWan.compareTo(new BigDecimal("80")) >= 0) {
                return new BigDecimal("3.375");
            } else if (amountInWan.compareTo(new BigDecimal("10")) >= 0) {
                return new BigDecimal("2.250");
            } else {
                return new BigDecimal("1.125");
            }
        } else if ("委外开发".equals(projectType)) {
            if (amountInWan.compareTo(new BigDecimal("200")) >= 0) {
                return new BigDecimal("3.000");
            } else if (amountInWan.compareTo(new BigDecimal("80")) >= 0) {
                return new BigDecimal("2.250");
            } else if (amountInWan.compareTo(new BigDecimal("10")) >= 0) {
                return new BigDecimal("1.500");
            } else {
                return new BigDecimal("0.750");
            }
        }
        return BigDecimal.ZERO;
    }

    /**
     * 计算运维类系数
     */
    private BigDecimal calculateMaintenanceCoefficient(String projectType, BigDecimal amountInWan) {
        if ("自主运维".equals(projectType)) {
            if (amountInWan.compareTo(new BigDecimal("24")) >= 0) {
                return new BigDecimal("1.000");
            } else if (amountInWan.compareTo(new BigDecimal("10")) >= 0) {
                return new BigDecimal("0.800");
            } else {
                return new BigDecimal("0.500");
            }
        } else if ("合作运维".equals(projectType)) {
            if (amountInWan.compareTo(new BigDecimal("24")) >= 0) {
                return new BigDecimal("0.750");
            } else if (amountInWan.compareTo(new BigDecimal("10")) >= 0) {
                return new BigDecimal("0.600");
            } else {
                return new BigDecimal("0.375");
            }
        } else if ("委外运维".equals(projectType)) {
            if (amountInWan.compareTo(new BigDecimal("24")) >= 0) {
                return new BigDecimal("0.500");
            } else if (amountInWan.compareTo(new BigDecimal("10")) >= 0) {
                return new BigDecimal("0.400");
            } else {
                return new BigDecimal("0.250");
            }
        }
        return BigDecimal.ZERO;
    }

    /**
     * 计算系统集成部项目系数
     */
    private BigDecimal calculateSystemIntegrationCoefficient(String nature, String projectType, BigDecimal amountInWan) {
        // 根据性质判断（支持多种可能的值）
        if ("项目建设类".equals(nature) || "项目建设".equals(nature)) {
            return calculateSystemIntegrationConstructionCoefficient(projectType, amountInWan);
        } else if ("运维类".equals(nature) || "运维服务".equals(nature)) {
            return calculateSystemIntegrationMaintenanceCoefficient(projectType, amountInWan);
        }

        return BigDecimal.ZERO;
    }

    /**
     * 系统集成部项目建设类系数
     */
    private BigDecimal calculateSystemIntegrationConstructionCoefficient(String projectType, BigDecimal amountInWan) {
        if ("自主实施".equals(projectType)) {
            if (amountInWan.compareTo(new BigDecimal("400")) >= 0) {
                return new BigDecimal("4.000");
            } else if (amountInWan.compareTo(new BigDecimal("200")) >= 0) {
                return new BigDecimal("3.600");
            } else if (amountInWan.compareTo(new BigDecimal("80")) >= 0) {
                return new BigDecimal("3.000");
            } else if (amountInWan.compareTo(new BigDecimal("30")) >= 0) {
                return new BigDecimal("2.400");
            } else if (amountInWan.compareTo(new BigDecimal("5")) >= 0) {
                return new BigDecimal("2.000");
            }
        } else if ("合作实施".equals(projectType)) {
            if (amountInWan.compareTo(new BigDecimal("400")) >= 0) {
                return new BigDecimal("3.200");
            } else if (amountInWan.compareTo(new BigDecimal("200")) >= 0) {
                return new BigDecimal("2.880");
            } else if (amountInWan.compareTo(new BigDecimal("80")) >= 0) {
                return new BigDecimal("2.400");
            } else if (amountInWan.compareTo(new BigDecimal("30")) >= 0) {
                return new BigDecimal("1.920");
            } else if (amountInWan.compareTo(new BigDecimal("5")) >= 0) {
                return new BigDecimal("1.600");
            }
        } else if ("委外实施".equals(projectType)) {
            if (amountInWan.compareTo(new BigDecimal("400")) >= 0) {
                return new BigDecimal("1.200");
            } else if (amountInWan.compareTo(new BigDecimal("200")) >= 0) {
                return new BigDecimal("1.080");
            } else if (amountInWan.compareTo(new BigDecimal("80")) >= 0) {
                return new BigDecimal("0.900");
            } else if (amountInWan.compareTo(new BigDecimal("30")) >= 0) {
                return new BigDecimal("0.720");
            } else if (amountInWan.compareTo(new BigDecimal("5")) >= 0) {
                return new BigDecimal("0.600");
            }
        }
        return BigDecimal.ZERO;
    }

    /**
     * 系统集成部运维类系数
     */
    private BigDecimal calculateSystemIntegrationMaintenanceCoefficient(String projectType, BigDecimal amountInWan) {
        if ("自主运维".equals(projectType)) {
            if (amountInWan.compareTo(new BigDecimal("400")) >= 0) {
                return new BigDecimal("4.000");
            } else if (amountInWan.compareTo(new BigDecimal("200")) >= 0) {
                return new BigDecimal("3.600");
            } else if (amountInWan.compareTo(new BigDecimal("80")) >= 0) {
                return new BigDecimal("3.000");
            } else if (amountInWan.compareTo(new BigDecimal("30")) >= 0) {
                return new BigDecimal("2.400");
            } else if (amountInWan.compareTo(new BigDecimal("5")) >= 0) {
                return new BigDecimal("2.000");
            }
        } else if ("委外运维".equals(projectType)) {
            if (amountInWan.compareTo(new BigDecimal("400")) >= 0) {
                return new BigDecimal("1.000");
            } else if (amountInWan.compareTo(new BigDecimal("200")) >= 0) {
                return new BigDecimal("0.900");
            } else if (amountInWan.compareTo(new BigDecimal("80")) >= 0) {
                return new BigDecimal("0.750");
            } else if (amountInWan.compareTo(new BigDecimal("30")) >= 0) {
                return new BigDecimal("0.600");
            } else if (amountInWan.compareTo(new BigDecimal("5")) >= 0) {
                return new BigDecimal("0.500");
            }
        }
        // 合作运维暂时不做自动计算，返回0
        return BigDecimal.ZERO;
    }

}
