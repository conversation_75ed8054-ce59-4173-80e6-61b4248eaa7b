package cn.iocoder.yudao.module.projectmanage.controller.admin.projectmaterialprocurement.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 物资采购审批 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ProjectMaterialProcurementRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "32742")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "项目id", example = "31102")
    @ExcelProperty("项目id")
    private Long projectId;

    @Schema(description = "模版id", example = "18503")
    @ExcelProperty("模版id")
    private String fdTemplataId;

    @Schema(description = "流程id", example = "12756")
    @ExcelProperty("流程id")
    private String flowId;

    @Schema(description = "发起人")
    @ExcelProperty("发起人")
    private String docCreator;

    @Schema(description = "项目名称", example = "芋艿")
    @ExcelProperty("项目名称")
    private String projectName;

    @Schema(description = "具体内容")
    @ExcelProperty("具体内容")
    private String detail;

    @Schema(description = "预算金额")
    @ExcelProperty("预算金额")
    private BigDecimal estimateMoney;

    @Schema(description = "比价邀标情况")
    @ExcelProperty("比价邀标情况")
    private String priceComparison;

    @Schema(description = "是否列入固定资产")
    @ExcelProperty("是否列入固定资产")
    private String isAssets;

    @Schema(description = "审批状态", example = "1")
    private String status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}