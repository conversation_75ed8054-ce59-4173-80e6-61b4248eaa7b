package cn.iocoder.yudao.module.projectmanage.dal.dataobject.projectmaterialprocurement;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 物资采购审批 DO
 *
 * <AUTHOR>
 */
@TableName("projectmanage_project_material_procurement")
@KeySequence("projectmanage_project_material_procurement_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectMaterialProcurementDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 项目id
     */
    private Long projectId;
    /**
     * 模版id
     */
    private String fdTemplataId;
    /**
     * 流程id
     */
    private String flowId;
    /**
     * 发起人
     */
    private String docCreator;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 具体内容
     */
    private String detail;
    /**
     * 预算金额
     */
    private BigDecimal estimateMoney;
    /**
     * 比价邀标情况
     */
    private String priceComparison;
    /**
     * 是否列入固定资产
     */
    private String isAssets;

    private String status;
}