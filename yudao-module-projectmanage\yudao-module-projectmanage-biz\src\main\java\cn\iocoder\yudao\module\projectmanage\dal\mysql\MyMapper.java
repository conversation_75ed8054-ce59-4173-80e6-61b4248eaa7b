package cn.iocoder.yudao.module.projectmanage.dal.mysql;

import cn.iocoder.yudao.module.projectmanage.controller.admin.contract.vo.ContractRespVO;
import cn.iocoder.yudao.module.projectmanage.controller.admin.dicontract.vo.DicontractRespVO;
import cn.iocoder.yudao.module.projectmanage.controller.admin.oacontract.vo.OacontractSaveReqVO;
import cn.iocoder.yudao.module.projectmanage.dal.dataobject.customer.CustomerDO;
import cn.iocoder.yudao.module.projectmanage.dal.dataobject.infrafile.InfraFileDO;
import cn.iocoder.yudao.module.projectmanage.dal.dataobject.project.bidmessage.BidMessageDO;
import cn.iocoder.yudao.module.projectmanage.dal.dataobject.projectgrop.ProjectGropDO;
import cn.iocoder.yudao.module.system.dal.dataobject.dict.DictDataDO;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Mapper
public interface MyMapper {

    @Select("update projectmanage_fee set is_inv = '否' where id = #{id}")
    void updateFeeIsInv2No(Integer id);

    // 更新fee_detail的isRed变为1
    @Select("update projectmanage_fee_detail set is_red = 1 where yw_code = #{ywCode}   ")
    void updateFeeDetailIsRed2Red(String ywCode);

    // 将infra_file的 business_id=0和creator为当前登录用户的的数据的，business_id进行修改
    @Select("update infra_file set business_id = #{businessId} where business_id = #{businessIdFlage}  and creator = #{creator} and file_business_type=#{fileBusinessType} and file_business_type_detail=#{fileBusinessTypeDetail}")
    void attachInfraFileAndDetailBusinessId(Long businessId, int businessIdFlage, Long creator, String fileBusinessType, String fileBusinessTypeDetail);

    // 将infra_file的 business_id=0和creator为当前登录用户的的数据的，business_id进行修改
    @Select("update infra_file set business_id = #{businessId} where business_id = #{businessIdFlage}  and creator = #{creator} and file_business_type=#{fileBusinessType}")
    void attachInfraFileBusinessId(Long businessId, int businessIdFlage, Long creator, String fileBusinessType);

    @Select("select * from projectmanage_customer where name = #{name} and deleted = 0")
    List<CustomerDO> selectCustomerByName(String name);

    // 更具feeId，物理删除发票明细
    @Select("delete from projectmanage_inv_detail where fee_id = #{feeId}")
    void deleteInvDetailByFeeId(Long feeId);

    // 根据feeId，查询fee_detail,sum(detail_amount)
    @Select("select IFNULL(SUM(detail_amount), 0) from projectmanage_fee_detail where fee_id = #{feeId} and yw_type = #{ywType} and deleted = 0")
    BigDecimal selectSumDetailAmountByFeeId(Long feeId, String ywType);

    @Select("select IFNULL(SUM(detail_amount), 0) from projectmanage_fee_detail where fee_id = #{feeId} and yw_type = #{ywType} and deleted = 0 and (is_red != 1 or is_red is null)")
    BigDecimal selectSumDetailAmountByFeeId_inv(Long feeId, String ywType);

    // 根据项目组code和收付关系查询个数
    @Select("select count(1) from projectmanage_project where project_group_code = #{projectGropCode} and pay_reci_relation = #{receivePayType}")
    Integer selectCountByProjectGropCodeAndReceivePayType(String projectGropCode, String receivePayType);

    // 根据year,查询合同表的最大合同编号
    @Select("select count(1) from projectmanage_contract where contract_year = #{year}")
    Integer selectCountByYear(String year);

    // 更具一个项目id,和ywType,查询fee_detail表, sum(detail_amount)
    @Select("select IFNULL(SUM(detail_amount), 0) from projectmanage_fee_detail where project_id = #{projectId} and yw_type = #{ywType} and deleted = 0")
    BigDecimal selectSumDetailAmountByProjectId(Long projectId, String ywType);


    // 更具一个项目组code,和ywType,查询feedetail表, sum(detail_amount)
    @Select("select IFNULL(SUM(detail_amount), 0) from projectmanage_fee_detail where group_code = #{groupCode} and yw_type = #{ywType} and deleted = 0")
    BigDecimal selectSumDetailAmountByProjectGroupCode(String groupCode, String ywType);

    @Select("select IFNULL(SUM(detail_amount), 0) from projectmanage_fee_detail where group_code = #{groupCode} and yw_type = #{ywType} and deleted = 0 and (is_red != 1 or is_red is null)")
    BigDecimal selectSumDetailAmountByProjectGroupCode_inv(String groupCode, String ywType);

    // 根据项目编号查询bidMessage数据
    @Select("select * from projectmanage_bid_message where project_code = #{projectCode} and deleted = 0")
    List<BidMessageDO> selectBidMessageByProjectCode(String projectCode);


    @Select("select count(1) from projectmanage_contract where contract_name = #{contractName} and amount = #{amount} and deleted = 0")
    Integer selectCountByContractNameAndAmount(String contractName, BigDecimal amount);

    // 根据项目组ID查询最早的合同年份
    @Select("SELECT MIN(contract_year) FROM projectmanage_contract WHERE project_group_id = #{projectGroupId} AND deleted = 0")
    String selectEarliestContractYearByGroupId(@Param("projectGroupId") Long projectGroupId);

    @Select("SELECT managing_department FROM projectmanage_project " +
            "WHERE project_group_id = #{projectGroupId} AND deleted = 0 " +
            "GROUP BY managing_department HAVING COUNT(DISTINCT managing_department) = 1 " +
            "LIMIT 1")
    String selectManagingDept(@Param("projectGroupId") Long projectGroupId);

    @Select("SELECT COUNT(*) FROM projectmanage_project " +
            "WHERE project_group_id = #{projectGroupId} " +
            "AND pay_reci_relation = '收' " +
            "AND (if_received_fully IS NULL OR if_received_fully != '是') " +
            "AND total_amount > 0 " +
            "AND deleted = 0")
    Integer selectUnfinishedReceiveProjects(@Param("projectGroupId") Long projectGroupId);

    @Update({
            "UPDATE projectmanage_jixiao",
            "SET deleted = 1",
            "WHERE (project_id, yearmonth) IN (",
            "SELECT project_id, yearmonth",
            "FROM (",
            "SELECT project_id, yearmonth",
            "FROM projectmanage_jixiao",
            "WHERE id = #{id}",
            ") AS tmp",
            ")"
    })
    void JixiaoDeleted(@Param("id") Long id);

    @Update({
            "UPDATE projectmanage_project_person",
            "SET manager_confirm = '未确认'",
            "WHERE (project_id, time) IN (",
            "SELECT project_id, yearmonth",
            "FROM (",
            "SELECT project_id, yearmonth",
            "FROM projectmanage_jixiao",
            "WHERE id = #{id}",
            ") AS tmp",
            ")"
    })
    void PersonUnconfirm(@Param("id") Long id);

    @Select("SELECT COUNT(*) FROM projectmanage_project " +
            "WHERE project_group_id = #{projectGroupId} " +
            "AND pay_reci_relation = '付' " +
            "AND (if_paid_fully IS NULL OR if_paid_fully != '是') " +
            "AND total_amount > 0 " +
            "AND deleted = 0")
    Integer selectUnfinishedPayProjects(@Param("projectGroupId") Long projectGroupId);

    @Select("SELECT COUNT(*) FROM projectmanage_project " +
            "WHERE project_group_id = #{projectGroupId} " +
            "AND total_amount > 0 " +
            "AND deleted = 0")
    Integer selectTotalProjectsByGroupId(@Param("projectGroupId") Long projectGroupId);

    //金额超五万项目数
    @Select("SELECT COUNT(*) FROM projectmanage_project " +
            "WHERE project_group_id = #{projectGroupId} " +
            "AND total_amount > 50000 " +
            "AND deleted = 0")
    Integer selectCountOverFiveWanByGroupId(@Param("projectGroupId") Long projectGroupId);

    @Select("SELECT COUNT(*) FROM projectmanage_weekly WHERE project_group_id = #{projectGroupId} AND deleted = 0")
    Integer selectWeeklyCountByProjectGroupId(@Param("projectGroupId") Long projectGroupId);

    // 根据项目组ID查询最早合同年份的工期
    @Select("SELECT work_day FROM projectmanage_contract " +
            "WHERE project_group_id = #{projectGroupId} " +
            "AND contract_year = (SELECT MIN(contract_year) FROM projectmanage_contract WHERE project_group_id = #{projectGroupId} AND deleted = 0) " +
            "AND deleted = 0 " +
            "ORDER BY create_time ASC LIMIT 1")
    Integer selectWorkDayByGroupId(@Param("projectGroupId") Long projectGroupId);

    //  根据项目组ID查询人员数量
    @Select("SELECT COUNT(*) FROM projectmanage_project_person " +
            "WHERE project_group_id = #{projectGroupId} " +
            "AND deleted = 0")
    Integer selectPersonCountByGroupId(@Param("projectGroupId") Long projectGroupId);

    @Select("SELECT status FROM projectmanage_project_manager_approval " +
            "WHERE project_grop_id = #{projectGroupId} AND deleted = 0 " +
            "ORDER BY create_time DESC LIMIT 1")
    String selectStatusByProjectGroupId(Long projectGropId);

    @Select("SELECT " +
            "CASE " +
            "  WHEN COUNT(*) = 0 THEN '进行中' " +
            "  WHEN SUM(CASE " +
            "             WHEN p.pay_reci_relation = '收' " +
            "              AND c.end_time IS NOT NULL " +
            "              AND (p.if_accepted IS NULL OR p.if_accepted != '是') " +
            "              AND NOW() > c.end_time THEN 1 " +
            "             ELSE 0 " +
            "           END) > 0 THEN '已超期' " +
            "  WHEN SUM(CASE " +
            "             WHEN (p.if_accepted IS NULL OR p.if_accepted != '是') " +
            "              AND (p.pay_reci_relation != '收' OR (p.pay_reci_relation = '收' AND (c.end_time IS NULL OR NOW() <= c.end_time))) THEN 1 " +
            "             ELSE 0 " +
            "           END) > 0 THEN '进行中' " +
            "  ELSE '已验收' " +
            "END AS status " +
            "FROM projectmanage_project p " +
            "LEFT JOIN projectmanage_contract c ON p.contract_id = c.id " +
            "WHERE p.project_group_id = #{projectGroupId} " +
            "  AND p.deleted = 0")
    String selectStatusTextByProjectGroupId(@Param("projectGroupId") Long projectGroupId);


    // 根据项目名查询项目
    @Select("select * from projectmanage_project where project_name = #{projectName}")
    List<ProjectGropDO> selectProjectByName(String projectName);

    // 根据project_group_code查询projetmanage_fee表，sum(amount)
    @Select("select IFNULL(SUM(amount), 0) from projectmanage_fee where project_group_code = #{projectGroupCode} and is_inv ='是' and deleted = 0")
    BigDecimal selectSumInvAmountByProjectGroupCode(String projectGroupCode);

    @Select("select IFNULL(SUM(amount), 0) from projectmanage_fee where project_group_code = #{projectGroupCode} and is_inv_pay ='是' and deleted = 0")
    BigDecimal selectSumInvPayAmountByProjectGroupCode(String projectGroupCode);

    // 查询项目经理为空的项目数量
    @Select("SELECT COUNT(*) FROM projectmanage_project_grop " +
            "WHERE (project_manager IS NULL OR project_manager = '') " +
            "AND deleted = 0")
    Integer selectCountManagerIsNull();


    @Select("SELECT BASE_SEQ.NEXTVAL FROM DUAL")
    @DS("vcrDataBase")
    Long getSeq();

    // 更具userID
    @Select("select username from system_users where id=#{id}")
    String selectUsernameById(Long id);

    @Select("SELECT MAX(weeks) FROM projectmanage_weekly WHERE project_name = #{projectName} AND deleted = 0")
    Integer getMaxWeeksByProjectName(@Param("projectName") String projectName);

    @Select("SELECT MAX(weeks) FROM projectmanage_weekly WHERE project_name = #{projectName} AND system_name = #{systemName} AND deleted = 0")
    Integer getMaxWeeksByProjectNameAndSystemName(@Param("projectName") String projectName, @Param("systemName") String systemName);

    @Select("SELECT report_time_start FROM projectmanage_weekly WHERE project_name = #{projectName} AND deleted = 0 ORDER BY weeks DESC LIMIT 1")
    LocalDateTime getLatestReportTimeStartByProjectName(@Param("projectName") String projectName);

    @Select("SELECT report_time_start FROM projectmanage_weekly WHERE project_name = #{projectName} AND system_name = #{systemName} AND deleted = 0 ORDER BY weeks DESC LIMIT 1")
    LocalDateTime getLatestReportTimeStartByProjectNameAndSystemName(@Param("projectName") String projectName, @Param("systemName") String systemName);

    @Select("select value from system_dict_data where label=#{label}  and deleted = 0")
    String selectDictValue(String label);

    @Select("select remark from system_dict_data where label=#{label} and dict_type = 'xmjl' and deleted = 0")
    String selectDictRemark(String label);

    @Select("select * from system_dict_data where label=#{label}  and deleted = 0")
    List<DictDataDO> selectDict(String label);

    @Select("select * from infra_file where business_id = #{businessID} and file_business_type='项目经理审批' and deleted = 0")
    List<InfraFileDO> selectProjectManagerInfraFile(Long projectID);

    @Select("select * from infra_file where business_id = #{businessID} and file_business_type='项目审批' and deleted = 0")
    List<InfraFileDO> selectProjectApprovalInfraFile(Long projectID);

    @Select("select * from infra_file where business_id = #{businessID} and file_business_type='物资采购' and deleted = 0")
    List<InfraFileDO> selectMaterialApprovalInfraFile(Long projectID);

    @Select("select remark from system_dict_data where label=#{label} and dict_type = 'oa审批人' and deleted = 0")
    String selectIdByname(String label);

    @Select("select * from infra_file where business_id = #{businessID} and file_business_type='合同' and deleted = 0")
    List<InfraFileDO> selectContractInfraFile(Long contractId);

    @Select("select flow_id from projectmanage_oacontract where contract_id = #{contractId} and deleted in (0, 1) order by deleted ASC,update_time DESC limit 1")
    String selectFlowIdByContractId(Long contractId);

    @Select("select contract_id from projectmanage_oacontract where flow_id = #{flowId} and deleted = 0 limit 1")
    Long selectcontractIdByFlowId(String flowId);

    @Select("select * from projectmanage_oacontract where flow_id = #{flowId} and deleted = 0")
    List<OacontractSaveReqVO> getApprovedList(String flowId);

    @Select("select * from projectmanage_contract where project_id = #{projectId} and deleted = 0")
    List<ContractRespVO> getContractByprojectId(String projectId);

    @Select("select * from projectmanage_contract where project_group_id = #{projectGroupId} and deleted = 0")
    List<ContractRespVO> getContractByprojectGroup(String projectId);

//    @Select("select * from projectmanage_contract where system_id = #{systemId} and deleted = 0")
//    List<ContractRespVO> getContractBySystemId(String systemId);

    @Update("update projectmanage_oacontract set deleted = 1 where flow_id = #{flowId}")
    void updateDeletedByFlowId(String flowId);

    @Update("update projectmanage_contract set status = 'OA审批完成' where id = #{contractId}")
    void updateStatusByContractId(Long contractId);


    @Select("select label from system_dict_data where value = #{value}  and deleted = 0")
    String selectDictLabel(String value);

    @Select("select username from system_users where nickname = #{nickname} and deleted = 0")
    String selectUsernameByNickname(String nickname);

    @Select("select username,email from system_users where nickname = #{nickname} and deleted = 0")
    List<Map<String, Object>> selectUsernameAndEmailByNickname(String nickname);

    @Select("select mobile from system_users where nickname = #{nickname} and deleted = 0")
    String selectMobileByNickname(String nickname);

    @Select("SELECT flow_id FROM projectmanage_project_manager_approval " +
            "WHERE project_grop_id = #{projectGroupId} AND deleted = 0 " +
            "ORDER BY create_time DESC LIMIT 1")
    String selectFlowIdByProjectGroupId(@Param("projectGroupId") Long projectGroupId);

    //查找审批状态
    @Select("SELECT status FROM projectmanage_project_approval " +
            "WHERE project_id = #{projectId} AND deleted = 0 " +
            "ORDER BY create_time DESC LIMIT 1")
    String selectApprovalStatusByProjectId(@Param("projectId") Long projectId);

    @Select("SELECT flow_id FROM projectmanage_project_approval " +
            "WHERE project_id = #{projectId} AND deleted = 0 " +
            "ORDER BY create_time DESC LIMIT 1")
    String selectFlowIdByProjectId(@Param("projectId") Long projectId);

    @Select("SELECT percentage FROM projectmanage_project  " +
            "WHERE id = #{projectId} AND deleted = 0")
    String selectProjectProgressByProjectId(@Param("projectId") Long projectId);

    @Select(
            "SELECT " +
                    "  GROUP_CONCAT(p.name ORDER BY p.id SEPARATOR ',') " +
                    "FROM system_users u " +
                    "JOIN system_post p " +
                    "  ON JSON_CONTAINS(u.post_ids, CAST(p.id AS JSON), '$') " +
                    "WHERE u.nickname = #{nickname} " +
                    "GROUP BY u.id"
    )
    List<String> selectPostNamesByNickname(String nickname);

    // 根据nickname，mobile查询用户
    @Select("select * from system_users where nickname = #{nickname} and mobile = #{mobile} and deleted = 0")
    List<String> selectUserByNicknameAndMobile(String nickname, String mobile);

    // 根据项目组编号从项目组表中获取已收款金额
    @Select("select IFNULL(received_amount, 0) from projectmanage_project_grop where project_grop_number = #{projectGropNumber} and deleted = 0")
    BigDecimal selectReceivedAmountFromProjectGrop(String projectGropNumber);

    // 根据项目组编号从项目组表中获取已付款金额
    @Select("select IFNULL(paid_amount, 0) from projectmanage_project_grop where project_grop_number = #{projectGropNumber} and deleted = 0")
    BigDecimal selectPaidAmountFromProjectGrop(String projectGropNumber);

    //实际发生金额
    @Select("select sum(detail_amount) as already_amount from projectmanage_fee_detail where fee_id = #{feeId} and deleted = 0 and yw_type = '收款'")
    String selectReceiveAmountByFeeId(Long feeId);

    //实际发生金额
    @Select("select sum(detail_amount) as already_amount from projectmanage_fee_detail where fee_id = #{feeId} and deleted = 0 and yw_type = '付款'")
    String selectPayAmountByFeeId(Long feeId);

//    @Select("SELECT i.inv_no " +
//            "FROM projectmanage_fee f " +
//            "LEFT JOIN projectmanage_fee_detail d ON f.id = d.fee_id AND d.deleted = 0 " +
//            "LEFT JOIN projectmanage_inv_main i ON d.yw_code = i.inv_bill_code AND d.yw_type = '开票' AND i.deleted = 0 " +
//            "WHERE f.id = #{feeId} AND i.inv_no IS NOT NULL ")
//    List<String> selectInvNosByFeeId(@Param("feeId") Long feeId);


    @Select("SELECT DISTINCT i.inv_no " +
            "FROM projectmanage_fee f " +
            "LEFT JOIN projectmanage_fee_detail d ON f.id = d.fee_id AND d.deleted = 0 " +
            "LEFT JOIN projectmanage_inv_main i ON d.yw_code = i.inv_bill_code " +
            "   AND d.yw_type = '开票' " +
            "   AND i.deleted = 0 " +
            "   AND i.inv_no IS NOT NULL " +
            "WHERE f.id = #{feeId}")
    List<String> selectInvNosByFeeId(@Param("feeId") Long feeId);

    // 查询软件开发一部项目经理项目数量
    @Select("SELECT project_manager_name, COUNT(*) AS project_count " +
            "FROM projectmanage_project " +
            "WHERE managing_department IN ('软件开发一部') " +
            "  AND project_manager_name IS NOT NULL " +
            "  AND project_manager_name != '' " +
            "  AND project_manager_name NOT LIKE '%、%' " +
            "  AND deleted = 0 " +
            "GROUP BY project_manager_name " +
            "ORDER BY project_count DESC " +
            "LIMIT 10")
    List<Map<String, Object>> selectManagerProjectStats();

    // 查询软件开发二部项目经理项目数量
    @Select("SELECT project_manager_name, COUNT(*) AS project_count " +
            "FROM projectmanage_project " +
            "WHERE managing_department IN ('软件开发二部') " +
            "  AND project_manager_name IS NOT NULL " +
            "  AND project_manager_name != '' " +
            "  AND project_manager_name NOT LIKE '%、%' " +
            "  AND deleted = 0 " +
            "GROUP BY project_manager_name " +
            "ORDER BY project_count DESC " +
            "LIMIT 10")
    List<Map<String, Object>> selectManagerTwoProjectStats();

    // 查询系统集成部项目经理项目数量
    @Select("SELECT project_manager_name, COUNT(*) AS project_count " +
            "FROM projectmanage_project " +
            "WHERE managing_department IN ('系统集成部') " +
            "  AND project_manager_name IS NOT NULL " +
            "  AND project_manager_name != '' " +
            "  AND project_manager_name NOT LIKE '%、%' " +
            "  AND deleted = 0 " +
            "GROUP BY project_manager_name " +
            "ORDER BY project_count DESC " +
            "LIMIT 10")
    List<Map<String, Object>> selectManagerSystemProjectStats();

    @Select({"<script>",
            "SELECT ",
            "  COUNT(*) AS totalCount,",
            "  SUM(CASE WHEN p.pay_reci_relation = '收' THEN 1 ELSE 0 END) AS receiveCount,",
            "  SUM(CASE WHEN p.pay_reci_relation = '付' THEN 1 ELSE 0 END) AS payCount",
            "FROM projectmanage_project_person per",
            "JOIN projectmanage_project p ON per.project_id = p.id",
            "WHERE per.name = #{currentUsername}",
            "  AND per.deleted = 0",
            "  AND p.deleted = 0",
            "</script>"})
    Map<String, Object> getProjectCount(@Param("currentUsername") String currentUsername);

    @Select({"<script>",
            "SELECT *",
            "FROM projectmanage_dicontract",
            "WHERE deleted = 0",
            "<if test=\"contractId != null\">",
            "  AND contract_id = #{contractId}",
            "</if>",
            "<if test=\"projectId != null\">",
            "  AND project_id = #{projectId}",
            "</if>",
            "ORDER BY proportion DESC",
            "</script>"})
    List<DicontractRespVO> selectDicontract(@Param("contractId") Long contractId, @Param("projectId") Long projectId);


    /**
     * 查询合同相关文档
     *
     * @param projectId      项目ID
     * @param projectGroupId 项目组ID
     * @return 合同文档列表
     */
    @Select({"<script>",
            "SELECT c.*, pg.project_grop_name AS project_group_name, p.project_name AS project_name, ",
            "f.id AS file_id, f.name AS file_name, f.url AS file_url, f.type AS file_type, ",
            "f.size AS file_size, f.create_time AS file_create_time ",
            "FROM projectmanage_contract c ",
            "LEFT JOIN projectmanage_project_grop pg ON c.project_group_id = pg.id ",
            "LEFT JOIN projectmanage_project p ON c.project_id = p.id ",
            "LEFT JOIN infra_file f ON c.id = f.business_id AND f.file_business_type = '合同' AND f.deleted = 0 ",
            "<where>",
            "<if test=\"projectId != null and projectId != ''\">",
            "AND c.project_id = #{projectId}",
            "</if>",
            "<if test=\"projectGroupId != null and projectGroupId != ''\">",
            "AND c.project_group_id = #{projectGroupId}",
            "</if>",
            "AND c.deleted = 0",
            "</where>",
            "ORDER BY c.create_time DESC",
            "</script>"})
    List<Map<String, Object>> selectContractDocumentsByProjectIdAndGroupId(String projectId, String projectGroupId);

    /**
     * 查询运维相关文档
     *
     * @param projectId      项目ID
     * @param projectGroupId 项目组ID
     * @return 运维文档列表
     */
    @Select({"<script>",
            "SELECT m.*, pg.project_grop_name AS project_group_name, p.project_name AS project_name, ",
            "f.id AS file_id, f.name AS file_name, f.url AS file_url, f.type AS file_type, ",
            "f.size AS file_size, f.create_time AS file_create_time ",
            "FROM projectmanage_project_manager_maintenance m ",
            "LEFT JOIN projectmanage_project_grop pg ON m.project_group_id = pg.id ",
            "LEFT JOIN projectmanage_project p ON m.project_id = p.id ",
            "LEFT JOIN infra_file f ON m.id = f.business_id AND f.file_business_type = '运维附件' AND f.deleted = 0 ",
            "<where>",
            "<if test=\"projectId != null and projectId != ''\">",
            "AND m.project_id = #{projectId}",
            "</if>",
            "<if test=\"projectGroupId != null and projectGroupId != ''\">",
            "AND m.project_group_id = #{projectGroupId}",
            "</if>",
            "AND m.deleted = 0",
            "</where>",
            "ORDER BY m.create_time DESC",
            "</script>"})
    List<Map<String, Object>> selectMaintenanceDocumentsByProjectIdAndGroupId(String projectId, String projectGroupId);

    /**
     * 查询上线相关文档
     *
     * @param projectId      项目ID
     * @param projectGroupId 项目组ID
     * @return 上线文档列表
     */
    @Select({"<script>",
            "SELECT l.*, pg.project_grop_name AS project_group_name, p.project_name AS project_name, ",
            "f.id AS file_id, f.name AS file_name, f.url AS file_url, f.type AS file_type, ",
            "f.size AS file_size, f.create_time AS file_create_time ",
            "FROM projectmanage_launch l ",
            "LEFT JOIN projectmanage_project_grop pg ON l.project_group_id = pg.id ",
            "LEFT JOIN projectmanage_project p ON l.project_id = p.id ",
            "LEFT JOIN infra_file f ON l.id = f.business_id AND f.file_business_type = '上线文件' AND f.deleted = 0 ",
            "<where>",
            "<if test=\"projectId != null and projectId != ''\">",
            "AND l.project_id = #{projectId}",
            "</if>",
            "<if test=\"projectGroupId != null and projectGroupId != ''\">",
            "AND l.project_group_id = #{projectGroupId}",
            "</if>",

            "AND l.deleted = 0",
            "</where>",
            "ORDER BY l.create_time DESC",
            "</script>"})
    List<Map<String, Object>> selectLaunchDocumentsByProjectIdAndGroupId(String projectId, String projectGroupId);

    /**
     * 查询实施相关文档
     *
     * @param projectId      项目ID
     * @param projectGroupId 项目组ID
     * @return 实施文档列表
     */
    @Select({"<script>",
            "SELECT i.*, pg.project_grop_name AS project_group_name, p.project_name AS project_name, ",
            "f.id AS file_id, f.name AS file_name, f.url AS file_url, f.type AS file_type, ",
            "f.size AS file_size, f.create_time AS file_create_time ",
            "FROM projectmanage_implement i ",
            "LEFT JOIN projectmanage_project_grop pg ON i.project_group_id = pg.id ",
            "LEFT JOIN projectmanage_project p ON i.project_id = p.id ",
            "LEFT JOIN infra_file f ON i.id = f.business_id AND f.file_business_type = '实施附件' AND f.deleted = 0 ",
            "<where>",
            "<if test=\"projectId != null and projectId != ''\">",
            "AND i.project_id = #{projectId}",
            "</if>",
            "<if test=\"projectGroupId != null and projectGroupId != ''\">",
            "AND i.project_group_id = #{projectGroupId}",
            "</if>",
            "AND i.deleted = 0",
            "</where>",
            "ORDER BY i.create_time DESC",
            "</script>"})
    List<Map<String, Object>> selectImplementDocumentsByProjectIdAndGroupId(String projectId, String projectGroupId);

    // 更具部门id查询部门name
    @Select("select name from system_dept where id = #{deptId}")
    String selectDeptNameById(Long deptId);

    // 更具部门name查询部门id
    @Select("select id from system_dept where name = #{deptName}")
    Integer selectIdByDeptName(String deptName);

    @Select("SELECT id FROM projectmanage_weekly WHERE project_name = #{projectName} AND weeks = #{weeks} AND deleted = 0")
    Long getWeeklyIdByProjectNameAndWeeks(@Param("projectName") String projectName, @Param("weeks") Integer weeks);

    @Select("SELECT id FROM projectmanage_weekly WHERE project_name = #{projectName} AND system_name = #{systemName} AND weeks = #{weeks} AND deleted = 0")
    Long getWeeklyIdByProjectNameSystemNameAndWeeks(@Param("projectName") String projectName, @Param("systemName") String systemName, @Param("weeks") Integer weeks);

    @Select("SELECT d.name AS dept_name, u.nickname AS user_name " +
            "FROM system_dept d " +
            "LEFT JOIN system_users u ON d.id = u.dept_id AND u.deleted = 0 " +
            "WHERE d.id IN (199, 201,203) " +
            "AND d.deleted = 0 " +
            "ORDER BY d.id")
    List<Map<String, Object>> selectDeptUsersForSpecifiedDepts();

    /**
     * 批量更新合同到期状态
     * 1. 质保期到期（7天内）设置 ifWarrantyTime='是'
     * 2. 工期到期（3天内）设置 ifWorkday='是'
     * @return 影响记录数
     */
    /**
     * 批量更新合同到期状态
     * 1. 质保期到期（7天内）设置 ifWarrantyTime='是'
     * 2. 工期到期（3天内）设置 ifWorkday='是'
     * 3. 服务到期（30天内且financial_classification='服务'）设置 ifFuwuService='是'
     *
     * @return 影响记录数
     */
    @Update({"<script>",
            "UPDATE projectmanage_contract SET",
            "  if_warranty_time = CASE",
            "    WHEN warranty_due_time IS NOT NULL AND if_warranty_time IS NULL AND DATEDIFF(warranty_due_time, CURDATE()) BETWEEN 0 AND 7 THEN '是'",
            "    ELSE if_warranty_time",
            "  END,",
            "  if_workday = CASE",
            "    WHEN end_time IS NOT NULL AND if_workday IS NULL AND DATEDIFF(end_time, CURDATE()) BETWEEN 0 AND 3 THEN '是'",
            "    ELSE if_workday",
            "  END,",
            "  if_fuwu_service = CASE",
            "    WHEN financial_classification = '服务' AND if_fuwu_service IS NULL AND end_time IS NOT NULL AND DATEDIFF(end_time, CURDATE()) BETWEEN 0 AND 30 THEN '是'",
            "    ELSE if_fuwu_service",
            "  END",
            "WHERE deleted = 0",
            "  AND (",
            "    (warranty_due_time IS NOT NULL AND DATEDIFF(warranty_due_time, CURDATE()) BETWEEN 0 AND 7)",
            "    OR",
            "    (end_time IS NOT NULL AND DATEDIFF(end_time, CURDATE()) BETWEEN 0 AND 3)",
            "    OR",
            "    (financial_classification = '服务' AND end_time IS NOT NULL AND DATEDIFF(end_time, CURDATE()) BETWEEN 0 AND 30)",
            "  )",
            "</script>"})
    int updateContractExpiryStatus();

    /**
     * 获取当前登录用户的角色信息
     *
     * @param userId 用户ID
     * @return 角色信息列表，包含roleId和roleName
     */
    @Select("SELECT r.id as roleId, r.name as roleName " +
            "FROM system_role r " +
            "INNER JOIN system_user_role ur ON r.id = ur.role_id " +
            "WHERE ur.user_id = #{userId} " +
            "AND r.deleted = 0 " +
            "AND ur.deleted = 0")
    List<Map<String, Object>> selectCurrentUserRoles(@Param("userId") Long userId);

    /**
     * 查询合同项目分类列表（延期项目、进行中项目、验收项目）
     * 延期项目：当前时间大于计划完成时间，并且未验收
     * 进行中项目：当前时间小于计划完成时间，并且未验收
     * 验收项目：已验收
     *
     * @return 合同项目信息
     */
    @Select("SELECT " +
            "  c.id AS contract_id, " +
            "  c.contract_name, " +
            "  c.project_id, " +
            "  c.project_name, " +
            "  c.end_time, " +
            "  pg.project_grop_name, " +
            "  pg.project_manager, " +
            "  p.if_accepted, " +
            "  p.actual_acceptance_time " +
            "FROM projectmanage_contract c " +
            "LEFT JOIN projectmanage_project p ON c.project_id = p.id " +
            "LEFT JOIN projectmanage_project_grop pg ON c.project_group_id = pg.id " +
            "WHERE c.payment_relationship = '收' " +
            "  AND c.deleted = 0 " +
            "  AND p.deleted = 0 " +
            "ORDER BY c.end_time DESC")
    List<Map<String, Object>> selectContractsByCategory();

    /**
     * 一次性获取项目组的所有统计数据
     * @param projectGroupIds 项目组ID列表
     * @return 项目组统计数据列表
     */
    @Select({
        "<script>",
        "SELECT ",
        "    pg.id as project_group_id, ",
        "    pg.project_grop_number, ",
        "    (SELECT COALESCE(SUM(detail_amount), 0) FROM projectmanage_fee_detail WHERE group_code = pg.project_grop_number AND yw_type = '收款' AND deleted = 0) as received_amount, ",
        "    (SELECT COALESCE(SUM(detail_amount), 0) FROM projectmanage_fee_detail WHERE group_code = pg.project_grop_number AND yw_type = '付款' AND deleted = 0) as paid_amount, ",
        "    (SELECT COALESCE(SUM(detail_amount), 0) FROM projectmanage_fee_detail WHERE group_code = pg.project_grop_number AND yw_type = '开票' AND (is_red != 1 OR is_red IS NULL) AND deleted = 0) as inv_amount, ",
        "    (SELECT COALESCE(SUM(detail_amount), 0) FROM projectmanage_fee_detail WHERE group_code = pg.project_grop_number AND yw_type = '收票' AND deleted = 0) as inv_pay_amount, ",
        "    MIN(c.contract_year) as earliest_contract_year, ",
        "    (SELECT work_day FROM projectmanage_contract WHERE project_group_id = pg.id AND contract_year = MIN(c.contract_year) AND deleted = 0 ORDER BY create_time ASC LIMIT 1) as work_day, ",
        "    (SELECT  COUNT(DISTINCT name) FROM projectmanage_project_person WHERE project_group_id = pg.id AND deleted = 0 AND time LIKE CONCAT(DATE_FORMAT(CURDATE(), '%Y-%m'), '%')) as person_count, ",

        "    (SELECT COUNT(*) FROM projectmanage_project WHERE project_group_id = pg.id AND total_amount > 50000 AND deleted = 0) as over_five_wan_count, ",
        "    (SELECT COUNT(*) FROM projectmanage_weekly WHERE project_group_id = pg.id AND deleted = 0) as weekly_count, ",
        "    (SELECT COUNT(*) FROM projectmanage_project WHERE project_group_id = pg.id AND pay_reci_relation = '收' AND (if_received_fully IS NULL OR if_received_fully != '是') AND total_amount > 0 AND deleted = 0) as unfinished_receive, ",
        "    (SELECT COUNT(*) FROM projectmanage_project WHERE project_group_id = pg.id AND total_amount > 0 AND deleted = 0) as total_projects, ",
        "    (SELECT COUNT(*) FROM projectmanage_project WHERE project_group_id = pg.id AND pay_reci_relation = '付' AND (if_paid_fully IS NULL OR if_paid_fully != '是') AND total_amount > 0 AND deleted = 0) as unfinished_pay, ",
        "    (SELECT managing_department FROM projectmanage_project WHERE project_group_id = pg.id AND deleted = 0 GROUP BY managing_department HAVING COUNT(DISTINCT managing_department) = 1 LIMIT 1) as department, ",
        "    (SELECT flow_id FROM projectmanage_project_manager_approval WHERE project_grop_id = pg.id AND deleted = 0 ORDER BY create_time DESC LIMIT 1) as flow_id, ",
        "    (SELECT status FROM projectmanage_project_manager_approval WHERE project_grop_id = pg.id AND deleted = 0 ORDER BY create_time DESC LIMIT 1) as manager_status, ",
        "    (SELECT GROUP_CONCAT(DISTINCT c.handler) FROM projectmanage_contract c WHERE c.project_group_id = pg.id AND c.deleted = 0) as handler, ",
        "    (SELECT CASE ",
        "        WHEN COUNT(*) = 0 THEN '无项目' ",
        "        WHEN COUNT(*) = SUM(CASE WHEN if_accepted = '是' AND pay_reci_relation = '收' THEN 1 ELSE 0 END) THEN '已验收' ",
        "        ELSE '未验收' ",
        "    END FROM projectmanage_project WHERE project_group_id = pg.id AND pay_reci_relation = '收' AND deleted = 0) as reci_acceptance_status, ",
        "    (SELECT CASE ",
        "        WHEN COUNT(*) = 0 THEN '无项目' ",
        "        WHEN COUNT(*) = SUM(CASE WHEN if_accepted = '是' AND pay_reci_relation = '付' THEN 1 ELSE 0 END) THEN '已验收' ",
        "        ELSE '未验收' ",
        "    END FROM projectmanage_project WHERE project_group_id = pg.id AND pay_reci_relation = '付' AND deleted = 0) as pay_acceptance_status ",
        "FROM projectmanage_project_grop pg ",
        "LEFT JOIN projectmanage_contract c ON pg.id = c.project_group_id AND c.deleted = 0 ",
        "WHERE pg.deleted = 0 ",
        "<if test='projectGroupIds != null and projectGroupIds.size() > 0'>",
        "    AND pg.id IN ",
        "    <foreach collection='projectGroupIds' item='id' open='(' separator=',' close=')'>",
        "        #{id}",
        "    </foreach>",
        "</if>",
        "GROUP BY pg.id, pg.project_grop_number",
        "</script>"
    })
    List<Map<String, Object>> selectProjectGroupStats(@Param("projectGroupIds") List<Long> projectGroupIds);


    @Select("SELECT MAX(weeks) FROM projectmanage_weekly WHERE project_id = #{projectId} AND deleted = 0")
    Integer getMaxWeeksByProjectId(@Param("projectId") String projectId);

    @Select("SELECT MAX(weeks) FROM projectmanage_weekly WHERE project_id = #{projectId} AND system_id = #{systemId} AND deleted = 0")
    Integer getMaxWeeksByProjectIdAndSystemId(@Param("projectId") String projectId, @Param("systemId") String systemId);

    @Select("SELECT report_time_start FROM projectmanage_weekly WHERE project_id = #{projectId} AND deleted = 0 ORDER BY weeks DESC LIMIT 1")
    LocalDateTime getLatestReportTimeStartByProjectId(@Param("projectId") String projectId);

    @Select("SELECT report_time_start FROM projectmanage_weekly WHERE project_id = #{projectId} AND system_id = #{systemId} AND deleted = 0 ORDER BY weeks DESC LIMIT 1")
    LocalDateTime getLatestReportTimeStartByProjectIdAndSystemId(@Param("projectId") String projectId, @Param("systemId") String systemId);

    @Select("SELECT id FROM projectmanage_weekly WHERE project_id = #{projectId} AND weeks = #{weeks} AND deleted = 0")
    Long getWeeklyIdByProjectIdAndWeeks(@Param("projectId") String projectId, @Param("weeks") Integer weeks);

    @Select("SELECT id FROM projectmanage_weekly WHERE project_id = #{projectId} AND system_id = #{systemId} AND weeks = #{weeks} AND deleted = 0")
    Long getWeeklyIdByProjectIdSystemIdAndWeeks(@Param("projectId") String projectId, @Param("systemId") String systemId, @Param("weeks") Integer weeks);

    @Select("SELECT status FROM projectmanage_project_material_procurement " +
            "WHERE project_id = #{projectId} AND deleted = 0 " +
            "ORDER BY create_time DESC LIMIT 1")
    String selectMaterialApprovalStatusByProjectId(@Param("projectId") Long projectId);

    @Select("SELECT flow_id FROM projectmanage_project_material_procurement " +
            "WHERE project_id = #{projectId} AND deleted = 0 " +
            "ORDER BY create_time DESC LIMIT 1")
    String selectMaterialFlowIdByProjectId(@Param("projectId") Long projectId);

    @Select("SELECT fd_templata_id FROM projectmanage_project_material_procurement " +
            "WHERE project_id = #{projectId} AND deleted = 0 " +
            "ORDER BY create_time DESC LIMIT 1")
    String selectFdidByProjectId(@Param("projectId") Long projectId);
}
