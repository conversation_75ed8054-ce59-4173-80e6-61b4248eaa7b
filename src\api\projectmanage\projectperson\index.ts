import request from '@/config/axios'

// 项目人员 VO
export interface ProjectPersonVO {
  id?: number // ID
  projectGroupId: number // 项目组id
  projectGroupCode: string // 项目组编号
  projectGroupName: string // 项目组名称
  projectId: number // 项目id
  projectCode: string // 项目编号
  projectName: string // 项目名称
  name: string // 姓名
  department: string // 部门
  occupation: string // 岗位
  status: string // 状态
  systemId: number // 系统id
  systemName: string // 系统名称
  percentage: string
  time: string
  managerConfirm?: string // 经理是否确认
  rejectReason?: string // 驳回原因
  managerRejectTime?: string // 经理驳回时间
  managerOperateTime?: string // 经理操作时间
  remark?: string // 备注
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
}

// 项目人员 API
export const ProjectPersonApi = {
  // 查询项目人员分页
  getProjectPersonPage: async (params: any) => {
    return await request.get({ url: `/projectmanage/project/project-person/page`, params })
  },

  // 查询项目人员详情
  getProjectPerson: async (id: number) => {
    return await request.get({ url: `/projectmanage/project/project-person/get?id=` + id })
  },

  // 新增项目人员
  createProjectPerson: async (data: ProjectPersonVO) => {
    return await request.post({ url: `/projectmanage/project/project-person/create`, data })
  },

  // 修改项目人员
  updateProjectPerson: async (data: ProjectPersonVO) => {
    return await request.put({ url: `/projectmanage/project/project-person/update`, data })
  },

  // 删除项目人员
  deleteProjectPerson: async (id: number) => {
    return await request.delete({ url: `/projectmanage/project/project-person/delete?id=` + id })
  },

  // 导出项目人员 Excel
  exportProjectPerson: async (params) => {
    return await request.download({ url: `/projectmanage/project/project-person/export-excel`, params })
  },

  // 根据项目组编号获取项目人员列表
  getProjectPersonListByProjectGroupCode: async (projectGroupCode: string) => {
    return await request.get({ url: `/projectmanage/project/project-person/list-by-project-group-code?projectGroupCode=` + projectGroupCode })
  },

  // 根据项目编号获取项目人员列表
  getProjectPersonListByProjectCode: async (projectCode: string) => {
    return await request.get({ url: `/projectmanage/project/project-person/list-by-project-code?projectCode=` + projectCode })
  },

  // 根据项目Id获取项目人员列表
  getProjectPersonListByProjectId: async (projectId: string) => {
    return await request.get({ url: `/projectmanage/project/project-person/list-by-project-id?projectId=` + projectId })
  },

  // 根据系统id获取项目人员列表
  getProjectPersonListBySystemId: async (systemId: string) => {
    return await request.get({ url: `/projectmanage/project/project-person/list-by-system-id?systemId=` + systemId })
  },

  // 根据项目组id获取项目人员列表
  getProjectPersonListByProjectGroupId: async (projectGroupId: string) => {
    return await request.get({ url: `/projectmanage/project/project-person/list-by-project-group-id?projectGroupId=` + projectGroupId })
  },

  // 根据项目ID和项目组ID获取项目人员列表记录列表
  getPersonListByProject: async (projectId: string, projectGroupId: string) => {
    return request.get({
      url: '/projectmanage/project/project-person/list-by-project',
      params: { projectId, projectGroupId }
    })
  },
}
