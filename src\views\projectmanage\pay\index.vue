<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="付款单号" prop="payCode">
        <el-input
          v-model="queryParams.payCode"
          placeholder="请输入付款单号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="付款方式" prop="payMethod">
        <el-input
          v-model="queryParams.payMethod"
          placeholder="请输入付款方式"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="付款金额" prop="payPrice">
        <el-input
          v-model="queryParams.payPrice"
          placeholder="请输入付款金额"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
<!--      <el-form-item label="客户id" prop="customerId">-->
<!--        <el-input-->
<!--          v-model="queryParams.customerId"-->
<!--          placeholder="请输入客户id"-->
<!--          clearable-->
<!--          @keyup.enter="handleQuery"-->
<!--          class="!w-240px"-->
<!--        />-->
<!--      </el-form-item>-->
      <el-form-item label="付款时间" prop="payDate">
        <el-date-picker
          v-model="queryParams.payDate"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item label="付款人" prop="payName">
        <el-input
          v-model="queryParams.payName"
          placeholder="请输入付款人"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="是否复核" prop="isRev">
        <el-input
          v-model="queryParams.isRev"
          placeholder="请输入是否复核"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="复核人" prop="revName">
        <el-input
          v-model="queryParams.revName"
          placeholder="请输入复核人"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="复核时间" prop="revTime">
        <el-date-picker
          v-model="queryParams.revTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item label="是否付银" prop="isGetmoney">
        <el-input
          v-model="queryParams.isGetmoney"
          placeholder="请输入是否付银"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="付银人" prop="getmoneyName">
        <el-input
          v-model="queryParams.getmoneyName"
          placeholder="请输入付银人"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="付银时间" prop="getmoneyTime">
        <el-date-picker
          v-model="queryParams.getmoneyTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item label="是否达账" prop="isArrive">
        <el-input
          v-model="queryParams.isArrive"
          placeholder="请输入是否达账"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
<!--      <el-form-item label="付款达账人" prop="arriveName">-->
<!--        <el-input-->
<!--          v-model="queryParams.arriveName"-->
<!--          placeholder="请输入付款达账人"-->
<!--          clearable-->
<!--          @keyup.enter="handleQuery"-->
<!--          class="!w-240px"-->
<!--        />-->
<!--      </el-form-item>-->
      <el-form-item label="付款时间" prop="arriveTime">
        <el-date-picker
          v-model="queryParams.arriveTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="queryParams.remark"
          placeholder="请输入备注"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px"/>
          搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="mr-5px"/>
          重置
        </el-button>
<!--        <el-button-->
<!--          type="primary"-->
<!--          plain-->
<!--          @click="openForm('create')"-->
<!--          v-hasPermi="['projectmanage:pay:create']"-->
<!--        >-->
<!--          <Icon icon="ep:plus" class="mr-5px"/>-->
<!--          新增-->
<!--        </el-button>-->
<!--        <el-button-->
<!--          type="success"-->
<!--          plain-->
<!--          @click="handleExport"-->
<!--          :loading="exportLoading"-->
<!--          v-hasPermi="['projectmanage:pay:export']"-->
<!--        >-->
<!--          <Icon icon="ep:download" class="mr-5px"/>-->
<!--          导出-->
<!--        </el-button>-->
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-button
      type="primary"
      plain
      @click="addRecBut()"
    >
      新增付款
    </el-button>

    <el-button
      type="primary"
      plain
      @click="generateVcrBut"
      :disabled="isGenerateVcrDisabled"
    >
      生成付款凭证
    </el-button>

    <el-table
      v-loading="loading"
      :data="list"
      :stripe="true"
      :show-overflow-tooltip="true"
      @row-click="recClick"
      @selection-change="handleMainTableSelectionChange"
      highlight-current-row
    >
      <el-table-column type="selection" width="55"/>
      <el-table-column label="付款单号" align="center" prop="payCode"/>
      <el-table-column label="付款方式" align="center" prop="payMethod"/>
      <el-table-column label="付款金额" align="center" prop="payPrice">
        <template #default="scope">
          {{ formatCurrency(scope.row.payPrice) }}
        </template>
      </el-table-column>
      <el-table-column label="客户" align="center" prop="customerName" width="250"/>
      <el-table-column label="付款时间" align="center" prop="payDate" width="180px"/>
      <el-table-column label="付款人" align="center" prop="payName"/>
      <!--      <el-table-column label="是否复核" align="center" prop="isRev"/>-->
      <!--      <el-table-column label="复核人" align="center" prop="revName"/>-->
      <!--      <el-table-column label="复核时间" align="center" prop="revTime"/>-->
      <!--      <el-table-column label="是否付银" align="center" prop="isGetmoney"/>-->
      <!--      <el-table-column label="付银人" align="center" prop="getmoneyName"/>-->
      <!--      <el-table-column label="付银时间" align="center" prop="getmoneyTime"/>-->
      <!--      <el-table-column label="是否达账" align="center" prop="isArrive"/>-->
      <!--      <el-table-column label="付款达账人" align="center" prop="arriveName"/>-->
      <!--      <el-table-column label="付款时间" align="center" prop="arriveTime"/>-->
      <!-- <el-table-column label="备注" align="center" prop="remark"/> -->
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
<!--          <el-button-->
<!--            link-->
<!--            type="primary"-->
<!--            @click="openForm('update', scope.row.id)"-->
<!--            v-hasPermi="['projectmanage:pay:update']"-->
<!--          >-->
<!--            编辑-->
<!--          </el-button>-->
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['projectmanage:pay:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!--    一个弹窗-->
    <el-dialog
      title="新增付款"
      v-model="showAddRec"
      width="70%"
      @close="addRecCancelBut"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
<el-divider content-position="left">银行流水</el-divider>
      <ContentWrap>
        <el-button
          type="primary"
          @click="filterMatchingBankWater"
          :disabled="!feeList.length"
          class="mb-10px"
        >
          <Icon icon="ep:filter" class="mr-5px"/>
          筛选匹配银行流水
        </el-button>

        <el-table
v-loading="bankloading" :data="banklist" :stripe="true" :show-overflow-tooltip="true"
          :row-class-name="(row) => row.matched ? 'matched-row' : ''">
          <el-table-column label="交易时间" align="center" prop="biztime" />
          <el-table-column label="对方户名" align="center" prop="oppunit" />
          <el-table-column label="对方账号" align="center" prop="oppbanknumber" />
          <el-table-column label="摘要" align="center" prop="description" />
          <el-table-column label="付款金额" align="center" prop="debitamount">
            <template #default="scope">
              {{ formatCurrency(scope.row.debitamount) }}
            </template>
          </el-table-column>
          <el-table-column label="币别" align="center" prop="currencyName" width="80" />
          <el-table-column label="资金组织" align="center" prop="companyName" />
          <el-table-column label="银行账户名称" align="center" prop="accountbankBankName" />
          <el-table-column label="银行账户" align="center" prop="accountbankBankaccountnumber" />
        </el-table>
        <!-- 分页 -->
        <Pagination
          :total="banktotal"
          v-model:page="bankqueryParams.pageNo"
          v-model:limit="bankqueryParams.pageSize"
          @pagination="getBankList"
        />
      </ContentWrap>

      <ContentWrap>
        <el-form
          class="-mb-15px"
          label-width="68px"
          ref="feeFormRef"
          :inline="true"
        >
          <el-form-item label="收款方" prop="payer">
            <el-input
              v-model="fee_queryParams.payee"
              placeholder="请输入收款方"
              clearable
              @keyup.enter="queryFee"
              class="!w-240px "
            />
          </el-form-item>
          <el-form-item label="合同名称" prop="contractName">
            <el-input
              v-model="fee_queryParams.contractName"
              placeholder="请输入合同名称"
              clearable
              @keyup.enter="queryFee"
              class="!w-240px "
            />
          </el-form-item>
          <el-button type="primary" @click="queryFee" class="mb-15px">搜索
          </el-button>
        </el-form>
      </ContentWrap>
      <el-table
        v-loading="loading"
        :data="feeList"
        :stripe="true"
        :show-overflow-tooltip="true"
        style="width: 100%;margin-top: 20px"
        :default-sort="{ prop: 'projectCode', order: 'ascending' }"
        highlight-current-row
        @select="handleSelectionChangeFee"
      >
        <el-table-column type="selection" width="50" align="center"/>
        <el-table-column
          type="index"
          label="序号"
          width="60"
          align="center"
        />
        <!--      <el-table-column label="合同编号" align="center" prop="contractCode" />-->
<!--        <el-table-column label="状态" align="center" width="100" prop="status"/>-->

        <!--      <el-table-column label="合同名称" align="center" prop="contractName"/>-->
        <el-table-column label="收款方" align="center" prop="payee" width="250"/>

        <el-table-column label="合同名称" width="160" align="center">
        <template #default="scope">
          <el-button @click="openDrawer(scope.row)" type="text">{{ scope.row.contractName }}
          </el-button>
        </template>
        </el-table-column>
        <!--      <el-table-column label="付付关系" align="center" prop="paymentRelation" />-->

        <el-table-column label="付付" align="center" width="70">
          <template #default="{ row }">
            <el-tag :type="row.paymentRelation === '付' ? 'success' : 'danger'" size="large">
              {{ row.paymentRelation }}
            </el-tag>
          </template>
        </el-table-column>
        <!--        <el-table-column label="方式" align="center" prop="method"/>-->
        <el-table-column label="类型" align="center" prop="type">
          <template #default="scope">
            <span class="highlight-text">{{ scope.row.type }}</span>
          </template>
        </el-table-column>

        <el-table-column label="占比" align="center" prop="proportion">
          <template #default="scope">
            <span class="green-text">{{ scope.row.proportion }}%</span>
          </template>
        </el-table-column>
        <!--        <el-table-column label="币种" align="center" prop="currency"/>-->
        <el-table-column label="金额" align="center" prop="amount">
          <template #default="scope">
            <span class="highlight-text">{{ formatCurrency(scope.row.amount) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="已付" align="center" prop="alreadyAmount">
          <template #default="scope">
            {{ formatCurrency(scope.row.alreadyAmount) }}
          </template>
        </el-table-column>
        <el-table-column label="可付" align="center" prop="remainAmount">
          <template #default="scope">
            {{ formatCurrency(scope.row.remainAmount) }}
          </template>
        </el-table-column>

        <el-table-column label="本次付" width="150">
          <template #default="scope">
            <el-input
:disabled="thisTimeAmountAbled(scope.row)" type="number"
                      v-model="scope.row.thisTimeAmount"
                      @input="thisTimeAmountChange(scope.row)" placeholder="请输入付款金额"/>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div style="display: flex; justify-content: flex-end; margin-top: 15px;">
        <Pagination
          :total="feeTotal"
          v-model:page="fee_queryParams.pageNo"
          v-model:limit="fee_queryParams.pageSize"
          @pagination="handleFeePagination"
        />
      </div>
      <!-- 调试信息 -->
      <!-- <div v-if="feeTotal > 0" style="text-align: center; margin: 10px 0; color: #909399;">
        总记录: {{ feeTotal }} | 当前页: {{ fee_queryParams.pageNo }} | 每页显示: {{ fee_queryParams.pageSize }}
      </div> -->
      <el-form style="margin-top: 20px" label-width="130px">
        <el-row>
          <el-col :span="12" style="text-align: center">
            <el-card shadow="hover" :span="12">
          <span style="color: red;font-size: xx-large">可付款金额：{{
              money_format(canRecAmount, 2, '.', ',')
            }}</span>
            </el-card>
          </el-col>
          <el-col :span="12" style="text-align: center">
            <el-card shadow="hover" :span="12">
                <span
                  style="color: green;font-size: xx-large">本次付款金额：{{
                    money_format(actRecAmount, 2, '.', ',')
                  }}</span>
            </el-card>
          </el-col>
        </el-row>
      </el-form>
      <!--      支付方式-->
      <el-col :span="8">
        <el-form-item label="方式" prop="method">
          <el-select v-model="addRec.payMethod" placeholder="请选择方式" class="w-full">
            <el-option
              v-for="dict in getStrDictOptions(DICT_TYPE.FEE_METHOD)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </el-col>




      <template #footer>
        <el-button type="primary" @click="addRecConfrimBut" :disabled="isConfirmButtonDisabled">确 定</el-button>
        <el-button @click="addRecCancelBut">取 消</el-button>
      </template>
    </el-dialog>

    <el-drawer
      title="合同详情"
      v-model="drawerVisible"
      :direction="rtl"
      size="45%"
      class="contract-drawer"
    >
      <div class="drawer-content">
        <!-- 税率和金额信息 -->
        <div class="section-title">税率及金额信息</div>
        <el-row>
          <el-col :span="24">
            <el-form-item prop="rate">
              <div
v-for="(item, index) in one.ratesAndAmounts" :key="index"
                   class="rate-item">
                <el-row :gutter="20">
                  <el-col :span="4">
                    <el-form-item label="税率">
                      <el-input readonly v-model="item.rate" placeholder="请选择税率"/>
                    </el-form-item>
                  </el-col>
                  <el-col :span="7">
                    <el-form-item label="含税金额">
                      <el-input readonly v-model="item.amount" placeholder="含税金额"/>
                    </el-form-item>
                  </el-col>
                  <el-col :span="7">
                    <el-form-item label="不含税金额">
                      <el-input
                        :value="(item.amount/(1+item.rate/100)).toFixed(2)"
                        placeholder="不含税金额" readonly/>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="税额">
                      <el-input
readonly
                                :value="(item.amount/(1+item.rate/100)*(item.rate/100)).toFixed(2)"
                                placeholder="税额"/>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 总金额信息 -->
        <div class="section-title">总金额信息</div>
        <el-row justify="center" class="total-amount-row">
          <el-col :span="6">
            <el-form-item label="含税总金额" prop="amount">
              <div class="amount-value">
                {{ money_format(one.amount, 2, '.', ',') }}
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="不含税总金额" prop="amount">
              <div class="amount-value">
                {{ money_format(getNoTaxPrice, 2, '.', ',') }}
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="总税额" prop="amount">
              <div class="amount-value">
                {{ money_format(getTaxPrice, 2, '.', ',') }}
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 文件列表 -->
        <div class="section-title">相关文件</div>
        <el-row
v-for="group in groupFileList" :key="group[0].fileBusinessTypeDetail"
                class="file-group">
          <el-col :span="24">
            <div class="file-group-title">
              <span>{{ group[0].fileBusinessTypeDetail }}</span>
            </div>
            <UploadFile
              :modelValue="collectedUrls(group)"
              :businessId="one.id||0"
              :fileBusinessType="`合同`"
              :fileBusinessTypeDetail="group[0].fileBusinessTypeDetail"
              :url2Name="collectUrl2NameMap(group)"
              :businessFileList="group"
            />
          </el-col>
        </el-row>
      </div>
    </el-drawer>


    <el-dialog title="收款凭证" v-model="show_addVcr" :close-on-click-modal="false" width="1200px">
    <el-form style="margin-top: 20px" label-width="100px">
      <el-row>
        <el-col :span="6">
          <el-form-item label="凭证类型">
            <el-input
              v-model="VcrList.vcrClass"
              disabled
              style="width: 250px"
              placeholder="收款"
            />
          </el-form-item>
        </el-col>

        <!--        <el-col :span="3">-->
        <!--          <el-form-item label="预收金额">-->
        <!--            <el-input v-model="ysje" @blur="hqpzsj" label="预收金额"></el-input>-->
        <!--          </el-form-item>-->
        <!--        </el-col>-->
        <!--        <el-col :span="3">-->
        <!--          <el-form-item>-->
        <!--            <el-button type="primary" @click="qbys">全部</el-button>-->
        <!--          </el-form-item>-->
        <!--        </el-col>-->

        <!--        <el-col :span="6">-->
        <!--          <el-form-item label="凭证方式">-->
        <!--            <el-select filterable style="width: 250px" placeholder="请选择" @change="hqpzsj" v-model="pzfs">-->
        <!--              <el-option-->
        <!--                v-for="item in [{value:'总额',label:'总额'},{value:'净额',label:'净额'}]"-->
        <!--                :key="item.value"-->
        <!--                :label="item.label"-->
        <!--                :value="item.value">-->
        <!--              </el-option>-->
        <!--            </el-select>-->
        <!--            &lt;!&ndash;                  <el-button type="primary" @click="hqpzsj">获取凭证数据</el-button>&ndash;&gt;-->
        <!--          </el-form-item>-->
        <!--        </el-col>-->

        <el-col :span="24">
          <!--最多10个汉字-->
          <el-form-item label="标准摘要">
            <el-input type="textarea" v-model="VcrList.vcrStdop" placeholder="标准摘要"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <!--最多10个汉字-->
          <el-form-item label="附件张数">
            <el-input-number v-model="VcrList.fjzs" label="附件张数"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="财务经办人">
            <el-input style="width: 250px" v-model="VcrList.accOman" label="财务经办人"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="凭证制作日期">
            <el-date-picker
              type="date" value-format="YYYY-MM-DD" v-model="VcrList.vmakedate"
              style="width: 250px"
              placeholder="选择日期"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="借贷方式">
            <el-input
              v-model="VcrList.jdfs"
              disabled
              style="width: 250px"
              placeholder="借"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="凭证发生额">
            <el-input
              v-model="VcrList.vcrMny" style="width: 250px" readonly
              label="凭证发生额"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否内部">
            <el-input
              v-model="VcrList.selfip"
              disabled
              style="width: 250px"
              placeholder="否"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-col :span="24" style="text-align: right">
        <el-button type="primary" @click="xzfl"><el-icon><Plus/></el-icon>&nbsp;&nbsp;新增分录
        </el-button>
      </el-col>
      <el-col :span="24">
        <el-divider content-position="left">分录</el-divider>
      </el-col>
      <el-col :span="24">
        <el-table :data="VcrDetailList" ref="FLmultipleTable" height="350px" border>
          <el-table-column label="分录号" width="80px" prop="flodr" align="center"/>
          <el-table-column prop="zy" label="摘要" align="center" width="320px"/>
          <el-table-column label="借贷方向" width="90px" prop="jd" align="center"/>
          <el-table-column prop="rmb" label="人民币" align="center" width="220px"/>
          <el-table-column label="科目内码" width="120px" prop="kmnm" align="center"/>
          <el-table-column label="对应科目名称" prop="dykm" align="center"/>
          <el-table-column label="操作" align="center" width="120">
            <template #default="scope">
              <el-button
                type="danger"
                link
                @click="deleteVcrDetailLocal(scope.$index)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
      <el-col :span="24">
                <span style="float: right;color: red;margin-top: 10px">总贷 - 总借:{{
                    money_format2(daiJianJie, 3, '.', ',')
                  }}</span>
      </el-col>
      <el-col :span="24">
        <el-divider content-position="left">核算维度</el-divider>
      </el-col>
      <el-col :span="24">
        <el-table
          :data="ZFVcrFzzList" ref="ZFZmultipleTable" border style="margin-top: 10px"
          height="350px">
          <el-table-column label="分录号" prop="flodr" align="center"/>
          <el-table-column label="客户" prop="clientName" align="center"/>
          <el-table-column label="货类" prop="cargoKind" align="center"/>
          <el-table-column label="业务主体" prop="deptName" align="center"/>
          <el-table-column label="银行账户" prop="bankCode" align="center"/>
          <el-table-column label="银行名称" prop="bankName" align="center"/>
          <el-table-column label="供应商" prop="supplyName" align="center"/>
          <el-table-column label="业务明细" prop="businessName" align="center"/>
          <el-table-column label="属地" prop="apanageName" align="center"/>
          <el-table-column label="税率" prop="taxrateName" align="center"/>
        </el-table>
      </el-col>
    </el-form>
    <template #footer>
      <el-button type="success" @click="pz_confrimPzlx">保存</el-button>
      <el-button @click="handleCancelVoucher">取 消</el-button>
    </template>
  </el-dialog>




  <el-dialog
    v-model="showAddEntryDialog"
    title="新增分录"
    width="50%"
  >
    <el-form ref="entryFormRef" :model="entryForm" label-width="100px">
      <el-form-item label="科目内码" prop="kmnm">
        <el-select v-model="entryForm.kmnm" placeholder="请选择科目" @change="handleKmnmChange">
          <el-option
            v-for="item in accountingEntries"
            :key="item.Kmnm"
            :label="`${item.Kmnm}_${item.Kmname}`"
            :value="item.Kmnm"
          />
        </el-select>
      </el-form-item>
      <el-row>
        <el-col :span="21">
          <el-form-item label="人民币" prop="rmb">
            <el-input v-model="entryForm.rmb" type="number"/>
          </el-form-item>
        </el-col>
        <el-col :span="3">
          <el-button type="warning" @click="addQuickly" style="margin-left: 10px;height: 30px;width: 90%;"><el-icon><Money/></el-icon>&nbsp;快速平账</el-button>
        </el-col>
      </el-row>
      <!-- 动态显示核算维度输入框 -->
      <template v-if="getRequiredHswd.includes('客户')">
        <el-form-item label="客户">
          <el-input v-model="entryForm.clientName"/>
        </el-form-item>
      </template>

      <template v-if="getRequiredHswd.includes('业务明细')">
        <el-form-item label="业务明细">
          <el-select v-model="entryForm.businessCode" clearable placeholder="请选择业务明细" @change="handleBusinessChange">
            <el-option v-for="dict in getStrDictOptions('ywmx')" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
      </template>

      <template v-if="getRequiredHswd.includes('业务主体')">
        <el-form-item label="业务主体">
          <el-select v-model="entryForm.deptName" placeholder="请选择业务主体" clearable @change="handleDeptChange">
            <el-option
              v-for="dict in getStrDictOptions('ywzt')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </template>

      <template v-if="getRequiredHswd.includes('银行账户')">
        <el-form-item label="银行账户">
          <el-select v-model="entryForm.bankCode" clearable placeholder="请选择银行账户" @change="handleBankChange">
            <el-option v-for="dict in getStrDictOptions('yhzh')" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
      </template>

      <template v-if="getRequiredHswd.includes('属地')">
        <el-form-item label="属地">
          <el-select v-model="entryForm.apanageCode" clearable placeholder="请选择属地" @change="handleApanageChange">
            <el-option v-for="dict in getStrDictOptions('sd')" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
      </template>

      <template v-if="getRequiredHswd.includes('税率')">
        <el-form-item label="税率">
          <el-select v-model="entryForm.taxrateCode" clearable placeholder="请选择税率" @change="handleTaxrateChange">
            <el-option v-for="dict in getStrDictOptions('jdsl')" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
      </template>
    </el-form>
    <template #footer>
      <el-button @click="showAddEntryDialog = false">取消</el-button>
      <el-button type="primary" @click="confirmAddEntry">确定</el-button>
    </template>
  </el-dialog>


  </ContentWrap>

  <ContentWrap>
    <el-tabs model-value="业务信息">
      <!--      name="invDetail"-->
      <el-tab-pane label="业务信息" name="业务信息">
          <el-table  :data="feeDetailList" :stripe="true" :show-overflow-tooltip="true">
<!--            <el-table-column label="id" align="center" prop="id" />-->
<!--            <el-table-column label="项目组id" align="center" prop="groupId" />-->
            <el-table-column label="项目组代码" align="center" prop="groupCode" />
<!--            <el-table-column label="项目组名字" align="center" prop="groupName" />-->
<!--            <el-table-column label="项目id" align="center" prop="projectId" />-->
<!--            <el-table-column label="项目代码" align="center" prop="projectCode" />-->
<!--            <el-table-column label="项目名" align="center" prop="projectName" />-->

            <el-table-column label="项目名" width="200" align="center" fixed="left">
              <template #default="scope">
                <router-link :to="'/project/' + scope.row.projectId">
                  <el-button link type="primary">{{ scope.row.projectName }}</el-button>
                </router-link>
              </template>
            </el-table-column>

<!--            <el-table-column label="合同id" align="center" prop="contractId" />-->
            <el-table-column label="合同代码" align="center" prop="contractCode" />
<!--            <el-table-column label="合同名" align="center" prop="contractName" />-->
            <el-table-column label="合同名" width="160" align="center">
              <template #default="scope">
                <el-button @click="openDrawer(scope.row)" type="text">{{ scope.row.contractName }}
                </el-button>
              </template>
            </el-table-column>
<!--            <el-table-column label="feeId" align="center" prop="feeId" />-->
            <el-table-column label="付款方" align="center" prop="payer" />
<!--            <el-table-column label="付款方" align="center" prop="payee" />-->
<!--            <el-table-column label="支付方式" align="center" prop="method" />-->
<!--            <el-table-column label="付付关系" align="center" prop="paymentRelation" />-->
            <el-table-column label="类型" align="center" prop="type" />

<!--            <el-table-column label="币种" align="center" prop="currency" />-->
<!--            <el-table-column-->
<!--              label="发生时间"-->
<!--              align="center"-->
<!--              prop="occurrenceDate"-->
<!--              :formatter="dateFormatter"-->
<!--              width="180px"-->
<!--            />-->
<!--            <el-table-column label="占比" align="center" prop="proportion" />-->
            <el-table-column label="备注" align="center" prop="remarks" />
            <el-table-column
              label="创建时间"
              align="center"
              prop="createTime"
              :formatter="dateFormatter"
              width="180px"
            />
<!--            <el-table-column label="业务代码" align="center" prop="ywCode" />-->
<!--            <el-table-column label="业务类型" align="center" prop="ywType" />-->
            <el-table-column label="总金额" align="center" prop="allAmount">
              <template #default="scope">
                {{ formatCurrency(scope.row.allAmount) }}
              </template>
            </el-table-column>
            <el-table-column label="付款金额" align="center" prop="detailAmount">
              <template #default="scope">
                {{ formatCurrency(scope.row.detailAmount) }}
              </template>
            </el-table-column>
            <el-table-column label="占比" align="center" prop="proportion" />
          </el-table>
      </el-tab-pane>

      <el-tab-pane label="凭证数据" name="凭证数据">
        <template v-if="VcrList.vcrStdop">
          <!-- 凭证主表 -->
          <el-table :data="[VcrList]" :stripe="true" :show-overflow-tooltip="true">
            <el-table-column label="凭证流水号" prop="vcrSeq" align="center"/>
            <el-table-column label="凭证类型" prop="vcrClass" align="center"/>
            <el-table-column label="标准摘要" prop="vcrStdop" align="center"/>
            <el-table-column label="附件张数" prop="fjzs" align="center"/>
            <el-table-column label="财务经办人" prop="accOman" align="center"/>
            <!-- <el-table-column label="凭证制作日期" prop="vmakedate" align="center"/> -->
             <!-- 凭证制作日期临时用vcrDate -->
            <el-table-column label="凭证制作日期" align="center">
              <template #default="scope">
                {{ dayjs(scope.row.vmakedate).format('YYYY-MM-DD') }}
              </template>
            </el-table-column>
            <el-table-column label="借贷方式" prop="jdfs" align="center"/>
            <el-table-column label="凭证发生额" prop="vcrMny" align="center"/>
            <el-table-column label="是否内部" prop="selfip" align="center"/>
            <el-table-column label="金蝶结果" prop="kingdeeMessage" align="center"/>
            <!-- 添加操作列 -->
            <el-table-column label="操作" align="center" width="200">
              <template #default>
                <el-button link type="success" @click="handleEditVcrData">
                  编辑凭证
                </el-button>
                <el-button link type="danger" @click="handleDeleteVcrData">
                  删除凭证
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-col :span="24">
            <el-divider content-position="left">分录</el-divider>
          </el-col>
          <!-- 分录表 -->
          <el-table :data="VcrDetailList" :stripe="true" :show-overflow-tooltip="true" class="mt-4">
            <el-table-column label="凭证流水号" prop="vcrSeq" align="center"/>
            <el-table-column label="分录号" prop="flodr" align="center" width="80"/>
            <el-table-column label="摘要" prop="zy" align="center"/>
            <el-table-column label="借贷方向" prop="jd" align="center" width="100"/>
            <el-table-column label="人民币" prop="rmb" align="center"/>
            <el-table-column label="科目内码" prop="kmnm" align="center"/>
            <el-table-column label="对应科目名称" prop="dykm" align="center"/>
          </el-table>

          <el-col :span="24">
            <el-divider content-position="left">核算维度</el-divider>
          </el-col>
          <!-- 核算维度表 -->
          <el-table :data="ZFVcrFzzList" :stripe="true" :show-overflow-tooltip="true" class="mt-4">
            <el-table-column label="分录号" prop="flodr" align="center"/>
            <el-table-column label="客户" prop="clientName" align="center"/>
            <el-table-column label="货类" prop="cargoKind" align="center"/>
            <el-table-column label="业务主体" prop="deptName" align="center"/>
            <el-table-column label="银行账户" prop="bankCode" align="center"/>
            <el-table-column label="银行名称" prop="bankName" align="center"/>
            <el-table-column label="供应商" prop="supplyName" align="center"/>
            <el-table-column label="业务明细" prop="businessName" align="center"/>
            <el-table-column label="属地" prop="apanageName" align="center"/>
            <el-table-column label="税率" prop="taxrateName" align="center"/>
          </el-table>
        </template>
        <template v-else>
          <el-empty description="暂无凭证数据" />
        </template>
      </el-tab-pane>

    </el-tabs>
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <PayForm ref="formRef" @success="getList"/>
</template>

<script setup lang="ts">
import {dateFormatter} from '@/utils/formatTime'
import download from '@/utils/download'
import {PayApi, PayVO} from '@/api/projectmanage/pay'
import PayForm from './PayForm.vue'
import {FeeApi} from "@/api/projectmanage/fee";
import {accAdd, Subtr} from "@/utils/add_sub";
import {DICT_TYPE, getStrDictOptions} from "@/utils/dict";
import {FeeDetailApi} from "@/api/projectmanage/feedetail";
import {CustomerApi, CustomerVO} from '@/api/projectmanage/customer'

import {useUserStore} from '@/store/modules/user'
import { accountingEntries } from '@/api/projectmanage/pay/accountingEntries'
import { BankWaterApi } from '@/api/projectmanage/bankwater'
import dayjs from 'dayjs';
import {Money, Plus} from "@element-plus/icons-vue";
import {ContractApi} from "@/api/projectmanage/contract";
import {reactive, ref, computed} from "vue";
import {BusinessFileTypeApi} from "@/api/infraInfo/bussinessFileType";
import UploadFile from "../../../components/UploadFile/src/UploadFile.vue";


const bankloading = ref(false) // 银行流水的加载中
const banklist = ref([]) // 银行流水的数据
const banktotal = ref(0) // 银行流水的总页数
const bankqueryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  oppUnit: undefined,
  oppBank: undefined,
  oppBankNumber: undefined,
  accountBankName: undefined,
  companyName: undefined,
  accountBankNumber: undefined,
  description: undefined,
  detailId: undefined,
  debitAmount: undefined,
  bizType: undefined,
  sortNo: undefined,
  currencyName: undefined,
  bizDate: [],
  transBalance: undefined,
  receiptNo: undefined,
  creditAmount: undefined,
  billNo: undefined,
  bizTime: [],
  transferCharge: undefined,
  accountBankOpenOrgName: undefined,
  receredType: undefined,
  ywCode: undefined,
  companyCode: undefined,
  isBind: undefined,
  isYw: undefined,
  createTime: [],
})
// 类型定义
interface VcrDetailItem {
  flodr: number
  zy: string
  jd: string
  rmb: string
  kmnm: number
  dykm: string

}

interface VcrFzzItem {
  flodr: number
  clientName?: string
  cargoKind?: string
  deptName?: string
  bankCode?: string
  bankName?: string
  supplyName?: string
  businessName?: string
  apanageName?: string
  taxrateName?: string
}

interface FeeFormItem {
  payer: string
  [key: string]: any
}

interface VcrListItem {
  vcrClass: string
  vcrStdop: string
  fjzs: number
  accOman: string
  vmakedate: string
  jdfs: string
  vcrMny: string
  selfip: string,
  kingdeeMessage?: string
  kingdeeCode?: string
  [key: string]: any
}

const getRequiredHswd = ref<string[]>([])
const showAddEntryDialog = ref(false)

const show_addVcr = ref(false)
const VcrList = ref<VcrListItem>({
  vcrClass: '付款',
  vcrStdop: '',
  fjzs: 1,
  accOman: '',
  vmakedate: dayjs().format('YYYY-MM-DD'),
  jdfs: '借',
  vcrMny: '',
  selfip: '否'
})
const VcrDetailList = ref<VcrDetailItem[]>([])
const daiJianJie = ref(0)
const ZFVcrFzzList = ref<VcrFzzItem[]>([])

/** 付款 列表 */
defineOptions({name: 'Pay'})
const mainTableSelection = ref<PayVO[]>([]) // 主表选中的数据
const message = useMessage() // 消息弹窗
const {t} = useI18n() // 国际化
const showAddRec = ref(false) // 新增付款弹窗
const loading = ref(true) // 列表的加载中
const list = ref<PayVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const feeList = ref([]) // 付款列表
const feeList2 = ref([])
const feeTotal = ref(0) // 付款列表的总数
const feeDetailList = ref([]) // 付款详情列表
const addRec = ref({
  id: undefined,
  payCode: undefined,
  payMethod: undefined,
  payPrice: undefined,
  customerId: undefined,
  payDate: undefined,
  payName: undefined,
  isRev: undefined,
  revName: undefined,
  revTime: undefined,
  isGetmoney: undefined,
  getmoneyName: undefined,
  getmoneyTime: undefined,
  isArrive: undefined,
  arriveName: undefined,
  arriveTime: undefined,
  remark: undefined,
  fees: []
}) // 新增付款
const fee_queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  projectCode: undefined,
  projectGroupCode: undefined,
  projectName: undefined,
  contractId: undefined,
  contractCode: undefined,
  contractName: undefined,
  paymentRelation: '付',
  method: undefined,
  type: undefined,
  amount: undefined,
  currency: undefined,
  occurrenceDate: [],
  proportion: undefined,
  payer: undefined,
  payee: undefined,
  remarks: undefined,
  createTime: [],
  projectId: undefined,
  isInv: '',
  invDate: undefined,
  invCode: undefined,
  invEr: undefined,
  isToincome: '',
  toincomeDate: undefined,
  toincomeEr: undefined,
  isRec: '',
  recDate: undefined,
  recEr: undefined,
  isInvPay: undefined,
  invDatePay: undefined,
  invCodePay: undefined,
  invErPay: undefined,
  isToincomePay: undefined,
  toincomeDatePay: undefined,
  toincomeErPay: undefined,
  isRecPay: '否',
  recDatePay: undefined,
  recErPay: undefined,
  status: undefined,
  invType: undefined,
  buyer: undefined,
  buyerAddr: undefined,
  buyerBank: undefined,
  buyerBankNo: undefined,
  buyTaxNo: undefined,
  taxRate: undefined,
  shoukuanren: undefined,
  fuheren: undefined,
  kaipiaoren: undefined,
  invRemark: undefined,
  invNo: undefined,
  createInvDate: undefined,
  price: undefined,
  noTaxPrice: undefined,
  tax: undefined,
  reTxt: undefined,
  isRed: undefined,
  specialInvType: undefined,
  invWaterId: undefined,
  redReTxt: undefined,
  pdfUrl: undefined,
  odfUrl: undefined,
  email: undefined,
  tel: undefined,
  redPdf: undefined,
  redOdf: undefined,
  redInvNo: undefined,
  redInfoNo: undefined,
  redInfoId: undefined,
  redInvDate: undefined,
  isPerson: undefined,
  bankShow: undefined,
  ywName: '付款'
})
const selectFeeList = ref([]) // 选中的付款列表
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  payCode: undefined,
  payMethod: undefined,
  payPrice: undefined,
  customerId: undefined,
  payDate: [],
  payName: undefined,
  isRev: undefined,
  revName: undefined,
  revTime: [],
  isGetmoney: undefined,
  getmoneyName: undefined,
  getmoneyTime: [],
  isArrive: undefined,
  arriveName: undefined,
  arriveTime: [],
  remark: undefined,
  createTime: [],
  projectName: undefined,
  projectId: undefined,
  projectCode: undefined,
  contractId: undefined,
  contractCode: undefined,
  contractName: undefined,
  projectGroupCode: undefined,
})

// 处理科目内码变化
const handleKmnmChange = (value) => {
  const selectedEntry = accountingEntries.find(item => item.Kmnm === Number(value));
  if (selectedEntry) {
    // 根据hswd字段拆分需要显示的核算维度
    getRequiredHswd.value = selectedEntry.hswd.split('+');
  } else {
    getRequiredHswd.value = [];
  }
};

const entryForm = reactive({
  kmnm: '',
  rmb: '',
  clientCode: '',    // 客户代码
  clientName: '',    // 客户名称
  businessCode: '',  // 业务明细代码
  businessName: '',  // 业务明细名称
  deptCode: '',     // 业务主体代码
  deptName: '',     // 业务主体名称
  bankCode: '',     // 银行账户代码
  bankName: '',     // 银行账户名称
  supplyCode: '',   // 供应商代码
  supplyName: '',   // 供应商名称
  apanageCode: '',  // 属地代码
  apanageName: '',  // 属地名称
  taxrateCode: '',  // 税率代码
  taxrateName: ''   // 税率名称
})

const queryParams_feeDetail = reactive({
  pageNo: 1,
  pageSize: 1000,
  ywCode: '',
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const drawerVisible = ref(false);
const contract = ref([]);
const groupFileList = ref([])
const fileList = ref([])

const one = ref({
  id: undefined,
  contractType: undefined,
  handler: undefined,
  executingDeparment: undefined,
  businessType: undefined,
  startTime: undefined,
  endTime: undefined,
  advanceAmount: undefined,
  status: undefined,
  approvalStatus: undefined,
  projectId: undefined,
  projectName: undefined,
  projectGroupId: undefined,
  projectGroupCode: undefined,
  projectGroupName: undefined,
  contractCode: undefined,
  contractName: undefined,
  jiafangId: undefined,
  jiafangName: undefined,
  workDay: undefined,
  yifangId: undefined,
  yifangName: undefined,
  jiafangSignatory: undefined,
  yifangSignatory: undefined,
  signingTime: undefined,
  content: undefined,
  amount: undefined,
  currency: undefined,
  paymentRelationship: undefined,
  contractYear: undefined,
  isAdvancePayment: undefined,
  advanceCurrency: undefined,
  projectCode: undefined,
  remark: undefined,
  financialClassification: undefined,
  businessContractCode: undefined,
  sourceMethods: undefined,
  warrantyService: undefined,
  isWarranty: undefined,
  warrantyAmount: undefined,
  rate: undefined,
  ratesAndAmounts: [] as { rate: number | undefined, amount: number | undefined }[],
  paymentType: undefined,
  paymentDescription: undefined,
  taxRate: undefined,
  taxAmount: undefined,
  taxExclusiveAmount: undefined,
  mainOrPatch:undefined,
  mainId:undefined,
  patchId:undefined
})

// 查询银行流水
const getBankList = async () => {
  bankloading.value = true
  try {
    // 使用新的API获取付款金额不为0的记录
    const data = await BankWaterApi.getFilteredBankWaterPage(bankqueryParams, 'debit')
    banklist.value = data.list
    banktotal.value = data.total
  } finally {
    bankloading.value = false
  }
}


/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await PayApi.getPayPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

const queryFee = async () => {
  loading.value = true
  try{
    const data = await FeeApi.getFeePage_yw(fee_queryParams)
    feeList.value = data.list
    feeTotal.value = data.total
  }catch (e) {
    console.log("查询失败:", e);
  }finally {
    loading.value = false;
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

// const recClick = async (row) => {
//   console.log(row)
//   queryParams_feeDetail.ywCode = row.payCode
//   const date = await FeeDetailApi.getFeeDetailPage(queryParams_feeDetail)
//   console.log(date)
//   feeDetailList.value = date.list
// }

const money_format = (number, decimals, dec_point, thousands_sep) => {
  /*
    * 参数说明：
    * number：要格式化的数字
    * decimals：保留几位小数
    * dec_point：小数点符号
    * thousands_sep：千分位符号
    * */
  number = (number + '').replace(/[^0-9+-Ee.]/g, '')
  let n = !isFinite(+number) ? 0 : +number,

    prec = !isFinite(+decimals) ? 0 : Math.abs(decimals),
    sep = (typeof thousands_sep === 'undefined') ? ',' : thousands_sep,
    dec = (typeof dec_point === 'undefined') ? '.' : dec_point,
    s = '',
    toFixedFix = function (n, prec) {
      var k = Math.pow(10, prec)
      return '' + Math.floor(n * k) / k
    }
  s = (prec ? toFixedFix(n, prec) : '' + Math.floor(n)).split('.')
  var re = /(-?\d+)(\d{3})/
  while (re.test(s[0])) {
    s[0] = s[0].replace(re, '$1' + sep + '$2')
  }

  if ((s[1] || '').length < prec) {
    s[1] = s[1] || ''
    s[1] += new Array(prec - s[1].length + 1).join('0')
  }
  return s.join(dec)
  // 使用案例
  //   number_format(1234567.089, 2, ".", ",");//1,234,567.08
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}


const handleSelectionChangeFee = async (rows, row) => {
  // 判断是勾选还是取消
  let selected = rows.length && rows.indexOf(row) !== -1
  selectFeeList.value = rows

  // 重置银行流水匹配状态
  bankWaterMatched.value = false
  matchedBankIds.value = []

  if (selected) {
    row.thisTimeAmount = row.remainAmount
    const data = await CustomerApi.getunit({name: row.payer})
    if(data && data.length > 0 && data[0].isInternalUnit === "是"){
      addRec.value.payMethod = "数字人民币"
    }else{
      addRec.value.payMethod = "银行转账"
    }

    // 尝试匹配银行流水
    if (row.payee && row.amount) {
      try {
        // 直接调用loadMatchedBankWaters加载匹配记录
        await loadMatchedBankWaters(row.payee, row.amount)

        if (bankWaterMatched.value) {
          message.success(`已找到匹配的银行流水记录 ${banklist.value.length} 条`)
        } else {
          message.warning('未找到匹配的银行流水记录')
        }
      } catch (err) {
        console.error('匹配银行流水失败:', err)
        message.error('匹配银行流水失败')
      }
    } else {
      // 清空银行流水列表
      banklist.value = []
      banktotal.value = 0
    }
  } else {
    row.thisTimeAmount = 0
    addRec.value.payMethod = undefined
    // 清空银行流水列表
    banklist.value = []
    banktotal.value = 0
  }
}

// 金额显示规范
const formatCurrency = (amount: number | undefined): string => {
  if (amount === undefined) {
    return '¥ 0.00'
  }
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount)
}

const confirmAddEntry = () => {
  if (!entryForm.kmnm || !entryForm.rmb) {
    message.error('请填写完整信息');
    return;
  }

  // 获取选中的科目信息
  const selectedEntry = accountingEntries.find(item => item.Kmnm === Number(entryForm.kmnm));
  if (!selectedEntry) {
    message.error('未找到对应的科目信息');
    return;
  }

  // 构建分录对象
  const newEntry = {
    flodr: VcrDetailList.value.length + 1,
    zy: VcrList.value.vcrStdop,
    jd: selectedEntry.Jd,
    rmb: entryForm.rmb,
    kmnm: selectedEntry.Kmnm,
    dykm: selectedEntry.Kmname
  };

  // 添加到分录列表
  VcrDetailList.value.push(newEntry);

  // 创建核算维度条目
  if (getRequiredHswd.value.length > 0) {
    const newDimension: any = {
      flodr: newEntry.flodr,
      clientCode: entryForm.clientCode || '',
      clientName: entryForm.clientName || '',
      businessCode: entryForm.businessCode || '',
      businessName: entryForm.businessName || '',
      deptCode: entryForm.deptCode || '',
      deptName: entryForm.deptName || '',
      bankCode: entryForm.bankCode || '',
      bankName: entryForm.bankName || '',
      supplyCode: entryForm.supplyCode || '',
      supplyName: entryForm.supplyName || '',
      apanageCode: entryForm.apanageCode || '',
      apanageName: entryForm.apanageName || '',
      taxrateCode: entryForm.taxrateCode || '',
      taxrateName: entryForm.taxrateName || ''
    };
    (ZFVcrFzzList.value as any[]).push(newDimension);
  }

  // 计算借贷差额
  daiJianJie.value = VcrDetailList.value.reduce((acc, curr) => {
    if (curr.jd === '借') {
      return Subtr(acc,Number(curr.rmb))
    } else {
      // return acc + Number(curr.rmb);
      return accAdd(acc, Number(curr.rmb))
    }
  }, 0);

  showAddEntryDialog.value = false;
  message.success('添加分录成功');
};

const addRecConfrimBut = async () => {
  const payerSet = new Set(selectFeeList.value.map(row => row.payer));
  if (payerSet.size > 1) {
    message.error(t('付款方不一致，请选择同一收款方'));
    return;
  }
  if (addRec.value.payMethod === undefined) {
    message.error(t('请选择支付方式'));
    return;
  }
  addRec.value.fees = selectFeeList.value
  try {
    await PayApi.createPay_yw(addRec.value)
    message.success(t('common.addSuccess'))
    showAddRec.value = false
    getList()
  } catch {
  }
}

const addRecCancelBut = () => {
  showAddRec.value = false
  selectFeeList.value = []
}

const thisTimeAmountAbled = (row) => {
  // 如果当前行是勾选状态，可以输入
  return selectFeeList.value.indexOf(row) === -1
}

const thisTimeAmountChange = (row) => {
  // 本次付比可付大
  if (row.thisTimeAmount > row.remainAmount) {
    message.error('本次付款金额不能大于可付款金额')
    row.thisTimeAmount = row.remainAmount
  }
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await PayApi.deletePay_yw(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {
  }
}

// 新增付款按钮
const addRecBut = async () => {
  // 打开一个弹窗
  showAddRec.value = true
  // 重置分页状态
  fee_queryParams.pageNo = 1
  let date = await FeeApi.getFeePage_yw(fee_queryParams)
  console.log(date)
  feeList.value = date.list
  feeTotal.value = date.total
  console.log("feeTotal设置为:", feeTotal.value)
  await getBankList()
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await PayApi.exportPay(queryParams)
    download.excel(data, '付款.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}


// 计算canRecAmount
const canRecAmount = computed(() => {
  let sum = 0
  selectFeeList.value.forEach((item) => {
    sum = accAdd(sum, item.remainAmount)
  })
  return sum
})

// 计算actRecAmount
const actRecAmount = computed(() => {
  let sum = 0
  selectFeeList.value.forEach((item) => {
    sum = accAdd(sum, item.thisTimeAmount)
  })
  return sum
})

/** 初始化 **/
onMounted(() => {
  getList()
})

// 存储当前单击选中的行数据
const currentRow = ref()
const recClick = async (row) => {
  console.log(row)
  currentRow.value = row
  queryParams_feeDetail.ywCode = row.payCode
  try {
    // 获取费用明细数据
    const date = await FeeDetailApi.getFeeDetailPage(queryParams_feeDetail)
    console.log(date)
    feeDetailList.value = date.list
    // 清空之前的凭证数据
    VcrList.value = {
      vcrClass: '',
      selfip: '',
      vcrStdop: '',
      fjzs: 0,
      accOman: '',
      vmakedate: '',
      jdfs: '',
      vcrMny: 0
    }
    VcrDetailList.value = []
    ZFVcrFzzList.value = []
    daiJianJie.value = 0

    // 获取凭证分录列表
    const detailRes = await PayApi.getVcrDetailList(row.payCode)
    if (detailRes) {
      VcrDetailList.value = detailRes
    }

    // 获取核算维度列表
    const fzzRes = await PayApi.getVerFzzList(row.payCode)
    if (fzzRes) {
      ZFVcrFzzList.value = fzzRes
    }

    // 获取凭证主表数据
    const vcrRes = await PayApi.getVcrListByItemCode(row.payCode)
    if (vcrRes) {
      VcrList.value = {
        ...vcrRes,
        vcrClass: vcrRes.vcrClass || '转账',
        selfip: vcrRes.selfip ? '是' : '否',
        kingdeeMessage: vcrRes.kdNote
      }
    }

    // 计算借贷差额
    daiJianJie.value = VcrDetailList.value.reduce((acc, curr) => {
      if (curr.jd === '借') {
        return Subtr(acc, Number(curr.rmb))
      } else {
        return accAdd(acc, Number(curr.rmb))
      }
    }, 0)
  } catch (error) {
    console.error('获取凭证数据失败:', error)
    message.error('获取凭证数据失败')
  }
}

const generateVcrBut = async () => {
  try {
    // 检查是否选择了记录
    if (!mainTableSelection.value || mainTableSelection.value.length === 0) {
      message.error('请选择要生成凭证的记录')
      return
    }

    const selectedRow = mainTableSelection.value[0]

    // 初始化凭证数据
    VcrList.value = {
      vcrClass: '收款',
      vcrStdop: `收${selectedRow.customerName}${selectedRow.projectName}项目费用`,
      fjzs: 1,
      accOman: useUserStore().user.nickname,
      vmakedate: dayjs().format('YYYY-MM-DD'),
      jdfs: '借',
      vcrMny: selectedRow.payPrice?.toString() || '0',
      selfip: '否'
    }

    // 获取凭证分录列表
    try {
      const detailRes = await PayApi.getVcrDetailList(selectedRow.payCode)
      if (detailRes) {
        VcrDetailList.value = detailRes
      }
    } catch (error) {
      console.error('获取分录列表失败:', error)
    }

    // 获取核算维度列表
    try {
      const fzzRes = await PayApi.getVerFzzList(selectedRow.payCode)
      if (fzzRes) {
        ZFVcrFzzList.value = fzzRes
      }
    } catch (error) {
      console.error('获取核算维度列表失败:', error)
    }

    // 更新查询参数并获取费用明细
    queryParams_feeDetail.ywCode = selectedRow.payCode
    try {
      const feeDetailRes = await FeeDetailApi.getFeeDetailPage(queryParams_feeDetail)
      feeDetailList.value = feeDetailRes.list
    } catch (error) {
      console.error('获取费用明细失败:', error)
    }

    // 计算借贷差额
    daiJianJie.value = VcrDetailList.value.reduce((acc, curr) => {
      const amount = Number(curr.rmb) || 0
      return curr.jd === '借' ? Subtr(acc, amount) : accAdd(acc, amount)
    }, 0)

    // 显示凭证弹窗
    show_addVcr.value = true

  } catch (err: any) {
    message.error('生成凭证失败：' + (err.message || '未知错误'))
    console.error('生成凭证失败:', err)
  }
}

// 主表选择事件
const handleMainTableSelectionChange = (selection: PayVO[]) => {
  mainTableSelection.value = selection
}

// 新增分录按钮
const xzfl = () => {
  showAddEntryDialog.value = true;
  // 重置表单并填充客户名称
  Object.assign(entryForm, {
    kmnm: '',
    rmb: '',
    clientName: mainTableSelection.value[0]?.customerName || '', // 自动填充选中记录的客户名称
    businessCode: '',
    businessName: '',
    deptCode: '',
    deptName: '',
    bankCode: '',
    supplyName: '',
    apanageName: '',
    taxrateName: ''
  });
};

// 设置能够快速平账的按钮方法
const addQuickly = () => {
  entryForm.rmb = (daiJianJie.value > 0 ? daiJianJie.value : -daiJianJie.value).toString();
}

// 保存凭证
const pz_confrimPzlx = async () => {
  try {
    // 检查必填项
    if (!VcrList.value.vcrStdop) {
      message.error('请填写标准摘要')
      return
    }
    if (!VcrList.value.accOman) {
      message.error('请填写财务经办人')
      return
    }
    if (!VcrList.value.vmakedate) {
      message.error('请选择凭证制作日期')
      return
    }
    if (VcrDetailList.value.length === 0) {
      message.error('请添加分录')
      return
    }

    // 计算总借和总贷金额
    let totalDebit = 0  // 总借
    let totalCredit = 0 // 总贷
    VcrDetailList.value.forEach(entry => {
      const amount = Number(entry.rmb)
      if (entry.jd === '借') {
        totalDebit = accAdd(totalDebit, amount)
      } else {
        totalCredit = accAdd(totalCredit, amount)
      }
    })

    // 检查总借总贷是否相等
    if (totalDebit !== totalCredit) {
      message.error('借贷不平衡')
      return
    }

    // 检查总借和总贷是否等于凭证发生额
    const vcrMny = Number(VcrList.value.vcrMny)
    if (totalDebit !== vcrMny || totalCredit !== vcrMny) {
      message.error('总借和总贷金额必须等于凭证发生额')
      return
    }

    // 获取新的凭证流水号
    let vcrSeqRes;
    // 检查是否已有凭证流水号，如果已有则使用原流水号，否则获取新的
    if (VcrList.value.vcrSeq) {
      vcrSeqRes = VcrList.value.vcrSeq;
      // console.log('使用原有凭证流水号:', vcrSeqRes);
    } else {
      // 获取新的凭证流水号
      vcrSeqRes = await PayApi.getVcrSeq()
      // console.log(vcrSeqRes)
    }
    // 查询是否选中
    if (mainTableSelection.value.length === 0) {
      mainTableSelection.value.push(currentRow.value)
    }
    console.log(VcrList.value)
    // 构造主表保存数据
    const saveData = {
      ...VcrList.value,
      selfip: VcrList.value.selfip === '是' ? 1 : 0,  // 将"是否"转换为 1/0
      vcrSeq: vcrSeqRes,  // 使用凭证流水号
      vcrDate: dayjs().format('YYYY-MM-DD'),  // 设置当前日期作为占位符
      itemCode: mainTableSelection.value[0]?.payCode || '',  // 使用选中记录的转收入流水号
      toincomeIds: mainTableSelection.value.map(item => item.id),  // 选中的转收入记录ID列表
      vmakedate: VcrList.value.vmakedate  // 使用用户选择的凭证制作日期
    }

    // 保存凭证主表
    const saveRes = await PayApi.saveVoucher(saveData)

    // 更新付款记录的凭证信息
    const updatePromises = mainTableSelection.value.map(item => {
      return PayApi.updatePayById({
        id: item.id,
        isPz: '是',
        pzTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        pzName: VcrList.value.accOman
      })
    })
    await Promise.all(updatePromises)

      // 构造分录数据
      const detailData = {
        itemCode: mainTableSelection.value[0].payCode,
        vcrSeq: vcrSeqRes,
        vcrDetailList: VcrDetailList.value
      }

      // 保存分录
      await PayApi.batchSaveVcrDetail(detailData)
      // 保存核算维度
      if (ZFVcrFzzList.value.length > 0) {
        // 处理每个核算维度条目
        const fzzDataPromises = ZFVcrFzzList.value.map(async item => {
          let clientId = ''
          // 如果有客户名称，查询对应的金蝶编码
          if (item.clientName) {
            try {
              const customerRes = await PayApi.getCustomerByname(item.clientName)
              if (customerRes) {
                clientId = customerRes.kingdeeCode // 使用金蝶编码作为 client_id
              } else {
                clientId = 'K9000108' // 如果查询结果为空，则使用默认值 K9000108
              }
            } catch (error) {
              console.error('查询客户信息失败:', error)
              clientId = 'K9000108' // 查询失败时也使用默认值 K9000108
            }
          }
          //  else {
          //   clientId = 'K9000108' // 如果没有客户名称，也使用默认值 K9000108
          // }

          return {
            ...item,
            vcrSeq: vcrSeqRes,
            ywCode: mainTableSelection.value[0].payCode,
            itemCode: mainTableSelection.value[0].payCode, // 保持与 ywCode 一致
            clientId: clientId // 设置查询到的金蝶编码
          }
        })

        // 等待所有客户信息查询完成
        const fzzData = await Promise.all(fzzDataPromises)

        // 保存核算维度数据
        await PayApi.batchSaveVerFzz(fzzData)
      }


      message.success('保存成功')

      // 重新获取凭证数据
      const itemCode = mainTableSelection.value[0].payCode
      try {
        // 获取凭证分录列表
        const detailRes = await PayApi.getVcrDetailList(itemCode)
        if (detailRes) {
          VcrDetailList.value = detailRes
        }

        // 获取核算维度列表
        const fzzRes = await PayApi.getVerFzzList(itemCode)
        if (fzzRes) {
          ZFVcrFzzList.value = fzzRes
        }

        // 获取凭证主表数据
        const vcrRes = await PayApi.getVcrListByItemCode(itemCode)
        if (vcrRes) {
          VcrList.value = {
            ...vcrRes,
            vcrClass: vcrRes.vcrClass || '付款',
            selfip: vcrRes.selfip ? '是' : '否'
          }
        }

        // 重新计算借贷差额
        daiJianJie.value = VcrDetailList.value.reduce((acc, curr) => {
          if (curr.jd === '借') {
            return Subtr(acc, Number(curr.rmb))
          } else {
            return accAdd(acc, Number(curr.rmb))
          }
        }, 0)
      } catch (err) {
        console.error('重新获取凭证数据失败:', err)
      }

      show_addVcr.value = false
      getList()

  } catch (error: any) {
    message.error('保存失败：' + error.message)
    console.error(error)
  }
}

// 处理业务明细选择变化
const handleBusinessChange = (value: string) => {
  const selectedBusiness = getStrDictOptions('ywmx').find(dict => dict.value === value);
  if (selectedBusiness) {
    entryForm.businessCode = selectedBusiness.value;
    entryForm.businessName = selectedBusiness.label;
  } else {
    entryForm.businessCode = '';
    entryForm.businessName = '';
  }
};

// 处理业务主体选择变化
const handleDeptChange = (value: string) => {
  const selectedDept = getStrDictOptions('ywzt').find(dict => dict.value === value);
  if (selectedDept) {
    entryForm.deptCode = selectedDept.value;
    entryForm.deptName = selectedDept.label;
  } else {
    entryForm.deptCode = '';
    entryForm.deptName = '';
  }
};



// 处理银行账户选择变化
const handleBankChange = (value: string) => {
  const selected = getStrDictOptions('yhzh').find(dict => dict.value === value)
  if (selected) {
    entryForm.bankCode = selected.value
    entryForm.bankName = selected.label
  }
}

// 处理供应商选择变化
const handleSupplyChange = (value: string) => {
  const selected = getStrDictOptions('gys').find(dict => dict.value === value)
  if (selected) {
    entryForm.supplyCode = selected.value
    entryForm.supplyName = selected.label
  }
}

// 处理属地选择变化
const handleApanageChange = (value: string) => {
  const selected = getStrDictOptions('sd').find(dict => dict.value === value)
  if (selected) {
    entryForm.apanageCode = selected.value
    entryForm.apanageName = selected.label
  }
}

// 处理税率选择变化
const handleTaxrateChange = (value: string) => {
  const selected = getStrDictOptions('jdsl').find(dict => dict.value === value)
  if (selected) {
    entryForm.taxrateCode = selected.value
    entryForm.taxrateName = selected.label
  }
}

// 添加删除分录的处理函数
const handleDeleteEntry = async (index: number) => {
  try {
    const entry = VcrDetailList.value[index];
    const deletedFlodr = entry.flodr;

    // 删除凭证分录
    await PayApi.deleteVcrDetail(entry.id);

    // 删除对应的核算维度记录
    const fzzEntry = ZFVcrFzzList.value.find(item => item.flodr === deletedFlodr);
    if (fzzEntry && fzzEntry.id) {
      await PayApi.deleteVerFzz(fzzEntry.id);
    }

    // 更新后端分录号
    const itemCode = mainTableSelection.value[0].payCode;
    await PayApi.updateFlodrNumbers(itemCode);
    await PayApi.updateFzzFlodrNumbers(itemCode);

    // 重新获取分录列表和核算维度列表
    const [detailRes, fzzRes] = await Promise.all([
      PayApi.getVcrDetailList(itemCode),
      PayApi.getVerFzzList(itemCode)
    ]);

    if (detailRes) {
      VcrDetailList.value = detailRes;
    }
    if (fzzRes) {
      ZFVcrFzzList.value = fzzRes;
    }

    // 重新计算借贷差额
    daiJianJie.value = VcrDetailList.value.reduce((acc, curr) => {
      if (curr.jd === '借') {
        return Subtr(acc, Number(curr.rmb));
      } else {
        return accAdd(acc, Number(curr.rmb));
      }
    }, 0);

    message.success('删除分录成功');
  } catch (error: any) {
    message.error('删除分录失败：' + (error.message || '未知错误'));
    console.error('删除分录失败:', error);
  }
};

// 格式化金额（带千分位）
const money_format2 = (number: number, decimals: number, dec_point: string, thousands_sep: string) => {
  return money_format(number, decimals, dec_point, thousands_sep)
}

// 计算是否禁用生成转收入凭证按钮
const isGenerateVcrDisabled = computed(() => {
  return mainTableSelection.value.some(item => item.isPz === '是')
})


/**
 * 本地删除凭证分录（不与后端交互）
 * @param index 要删除的分录索引
 */
 const deleteVcrDetailLocal = (index: number) => {
  try {
    // 获取要删除的分录
    const entry = VcrDetailList.value[index];
    const deletedFlodr = entry.flodr;

    // 1. 从分录列表中删除该条记录
    VcrDetailList.value.splice(index, 1);

    // 2. 删除对应的核算维度记录
    ZFVcrFzzList.value = ZFVcrFzzList.value.filter(
      dimension => dimension.flodr !== deletedFlodr
    );

    // 3. 重新编号分录
    VcrDetailList.value.forEach((entry, idx) => {
      entry.flodr = idx + 1;
    });

    // 4. 更新核算维度记录的分录号
    ZFVcrFzzList.value.forEach(dimension => {
      const originalFlodr = dimension.flodr;
      // 如果原始分录号大于被删除的分录号，则减1
      if (originalFlodr > deletedFlodr) {
        dimension.flodr = originalFlodr - 1;
      }
    });

    // 5. 重新计算借贷差额
    daiJianJie.value = VcrDetailList.value.reduce((acc, curr) => {
      if (curr.jd === '借') {
        return Subtr(acc, Number(curr.rmb));
      } else {
        return accAdd(acc, Number(curr.rmb));
      }
    }, 0);

    // 6. 提示用户
    message.success('已删除分录');
  } catch (error: any) {
    message.error('删除分录失败：' + (error.message || '未知错误'));
    console.error('删除分录失败:', error);
  }
}

// 在 script setup 部分添加删除方法
const handleDeleteVcrData = async () => {
  try {
    // 检查是否有凭证数据
    if (!VcrList.value || !VcrList.value.vcrStdop) {
      message.error('没有可删除的凭证数据')
      return
    }

    // 显示警告确认框
    await message.confirm(
      '确定要删除该凭证数据吗？此操作将永久删除所有相关的凭证记录。',
      '警告',
      {
        type: 'warning',
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        confirmButtonClass: 'el-button--danger'
      }
    )

    // 从当前查看的凭证数据中获取 itemCode
    const itemCode = VcrList.value.itemCode

    // 调用删除接口
    await PayApi.physicalDeleteVcrList(itemCode)

    // 更新转收入记录的凭证状态
    const currentRecord = list.value.find(item => item.payCode === itemCode)
    if (currentRecord) {
      await PayApi.updatePayById({
        id: currentRecord.id, // 使用当前记录的 id
        isPz: '否', // 将凭证状态改为"否"
        pzTime: '', // 清空凭证时间
        pzName: ''  // 清空凭证制作人
      })
    }

    message.success('凭证数据删除成功')

    // 清空凭证数据
    VcrList.value = {
      vcrClass: '',
      selfip: '',
      vcrStdop: '',
      fjzs: 0,
      accOman: '',
      vmakedate: '',
      jdfs: '',
      vcrMny: ''
    }
    VcrDetailList.value = []
    ZFVcrFzzList.value = []
    daiJianJie.value = 0

    // 刷新列表
    await getList()
  } catch (error) {
    if (error !== 'cancel') {
      message.error('删除失败：' + (error as Error).message)
    }
  }
}
// 编辑转收入凭证的数据
const handleEditVcrData = async () => {
  // 打开凭证弹窗
  show_addVcr.value = true
  // 不再强制设置日期为今天
}

// 添加resetVoucherData函数和handleCancelVoucher函数
const resetVoucherData = () => {
  // 重置凭证主表数据
  VcrList.value = {
    vcrClass: '付款',
    vcrStdop: '',
    fjzs: 1,
    accOman: '',
    vmakedate: dayjs().format('YYYY-MM-DD'),
    jdfs: '借',
    vcrMny: '',
    selfip: '否'
  }

  // 清空凭证分录
  VcrDetailList.value = []

  // 清空核算维度
  ZFVcrFzzList.value = []

  // 重置借贷差额
  daiJianJie.value = 0
}

// 处理取消按钮点击
const handleCancelVoucher = () => {
  resetVoucherData()
  show_addVcr.value = false
}

// 处理费用列表分页事件
const handleFeePagination = (params) => {
  fee_queryParams.pageNo = params.page
  fee_queryParams.pageSize = params.limit
  queryFee()
}

const openDrawer = async (row) => {
  // 去请求合同信息,
  let data = await ContractApi.getContract(row.contractId)
  if (!data){
    message.error('合同不存在或已被删除')
    return
  }
  console.log("222222")
  console.log(row.contractId)
  console.log(data)
  one.value = reactive(data)
  feeList2.value = data.fees || [];
  console.log(one)
  await getGroupFileList()
  drawerVisible.value = true
}

const getNoTaxPrice = computed(() => {
  let sum = 0;
  for (let i = 0; i < one.value.ratesAndAmounts.length; i++) {
    const item = one.value.ratesAndAmounts[i]
    sum += parseFloat((item.amount / (1 + item.rate / 100)).toFixed(2))
  }
  return sum.toFixed(2);
})

const getTaxPrice = computed(() => {
  let sum = 0;
  for (let i = 0; i < one.value.ratesAndAmounts.length; i++) {
    const item = one.value.ratesAndAmounts[i]
    sum += parseFloat(((item.amount / (1 + item.rate / 100) * (item.rate / 100)).toFixed(2)))
  }
  return sum.toFixed(2);
})

const getGroupFileList = async () => {

  let queryParams = {
    fileBusinessType: `合同`,
    businessId: one.value.id,
  }
  try {
    const data = await BusinessFileTypeApi.businessFileList(queryParams)
    fileList.value = data
    // fileList是一个对象数组，按照fileBusinessTypeDetail进行分组，形成一个二维数组groupFileList
    let group = {}
    for (let i = 0; i < fileList.value.length; i++) {
      let item = fileList.value[i]
      if (!group[item.fileBusinessTypeDetail]) {
        group[item.fileBusinessTypeDetail] = []
      }
      group[item.fileBusinessTypeDetail].push(item)
    }
    groupFileList.value = Object.values(group)
  } finally {

  }
};

const collectedUrls = (group) => {
  // 创建一个计算属性，返回当前 group 对象中所有 url 的数组
  return group.map(item => item.url);
};

const collectUrl2NameMap = (group) => {
  // 创建一个计算属性，返回当前 group 对象中所有 url 的数组
  const url2NameMap = {};
  group.forEach(item => {
    url2NameMap[item.url] = item.name;
  });
  console.log(url2NameMap)
  return url2NameMap;
};

// 在script setup部分添加匹配状态相关变量和函数
// 银行流水匹配状态
const bankWaterMatched = ref(false)
const matchedBankIds = ref<number[]>([])

// 添加确定按钮的禁用逻辑
const isConfirmButtonDisabled = computed(() => {
  // 当选中了付款记录但没有匹配到银行流水时禁用
  return selectFeeList.value.length > 0 && !bankWaterMatched.value
})

// 修改加载匹配银行流水的方法
const loadMatchedBankWaters = async (payer, amount) => {
  bankloading.value = true
  try {
    if (!payer || !amount) {
      // 如果没有提供付款方或金额，清空列表
      banklist.value = []
      banktotal.value = 0
      return
    }

    // 直接调用匹配API获取精确匹配的记录
    const matchedRecords = await BankWaterApi.matchBankWater(payer, Number(amount))
    if (matchedRecords && matchedRecords.length > 0) {
      // 只显示匹配的记录
      banklist.value = matchedRecords
      banktotal.value = matchedRecords.length

      // 标记所有记录为匹配
      banklist.value.forEach(item => {
        item.matched = true
      })

      // 更新匹配状态和ID列表
      bankWaterMatched.value = true
      matchedBankIds.value = matchedRecords.map(item => item.id)
    } else {
      // 没有匹配记录时清空列表
      banklist.value = []
      banktotal.value = 0
      bankWaterMatched.value = false
      matchedBankIds.value = []
    }
  } catch (err) {
    console.error('加载匹配银行流水失败:', err)
    banklist.value = []
    banktotal.value = 0
  } finally {
    bankloading.value = false
  }
}

// 筛选匹配银行流水函数
const filterMatchingBankWater = async () => {
  console.log('筛选匹配银行流水');

  if (!feeList.value || !feeList.value.length) {
    message.warning('请先导入付款记录');
    return;
  }

  bankloading.value = true;
  try {
    // 收集所有记录的付款方和金额信息
    const matchRequests = feeList.value.map(item => ({
      payer: item.payee, // 注意：付款管理中是收款方(payee)而非付款方(payer)
      amount: Number(item.thisTimeAmount || item.amount)
    })).filter(item => item.payer && item.amount > 0);

    if (matchRequests.length === 0) {
      message.warning('没有有效的匹配条件，请确保记录包含收款方和金额');
      bankloading.value = false;
      return;
    }

    // 调用批量匹配API
    const matchedRecords = await BankWaterApi.batchMatchBankWater(matchRequests);

    if (matchedRecords && matchedRecords.length > 0) {
      // 更新银行流水列表
      banklist.value = matchedRecords;
      banktotal.value = matchedRecords.length;

      // 标记所有记录为已匹配
      banklist.value.forEach(item => {
        item.matched = true;
      });

      // 更新匹配状态和ID列表
      bankWaterMatched.value = true;
      matchedBankIds.value = matchedRecords.map(item => item.id);

      message.success(`已找到 ${matchedRecords.length} 条匹配的银行流水记录`);
    } else {
      // 没有匹配记录时显示提示
      banklist.value = [];
      banktotal.value = 0;
      bankWaterMatched.value = false;
      matchedBankIds.value = [];

      message.warning('未找到匹配的银行流水记录');
    }
  } catch (err) {
    console.error('批量匹配银行流水失败:', err);
    message.error('批量匹配银行流水失败');
    banklist.value = [];
    banktotal.value = 0;
  } finally {
    bankloading.value = false;
  }
};

</script>

<style scoped>
.highlight-text {
  color: #F56C6C;
  font-weight: bold;
}

.contract-drawer {
  :deep(.el-drawer__header) {
    margin-bottom: 0;
    padding: 16px 20px;
    border-bottom: 1px solid #e4e7ed;
    font-size: 16px;
    font-weight: 500;
  }

  :deep(.el-drawer__body) {
    padding: 0;
  }
}

.drawer-content {
  padding: 20px;
}

.section-title {
  font-size: 15px;
  font-weight: 500;
  color: #303133;
  margin: 20px 0 15px;
  padding-left: 10px;
  border-left: 3px solid #409eff;
}

.rate-item {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 15px;

  :deep(.el-form-item) {
    margin-bottom: 0;
  }

  :deep(.el-input) {
    width: 100%;
  }
}

.total-amount-row {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  margin: 15px 0;

  :deep(.el-form-item) {
    margin-bottom: 0;
  }
}

.amount-value {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  text-align: center;
}

.file-group {
  margin-bottom: 20px;
}

.file-group-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 10px;
  padding-left: 10px;
}

.matched-row {
  background-color: #f0f9eb !important;
}
.matched-row td {
  font-weight: bold;
  color: #67C23A;
}
</style>
