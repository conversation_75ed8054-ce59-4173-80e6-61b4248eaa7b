package cn.iocoder.yudao.module.projectmanage.dal.dataobject.project.projectperson;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 项目人员 DO
 *
 * <AUTHOR>
 */
@TableName("projectmanage_project_person")
@KeySequence("projectmanage_project_person_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectPersonDO extends BaseDO {

    /**
     * 记录id
     */
    @TableId
    private Long id;
    /**
     * 项目组id
     */
    private Long projectGroupId;
    /**
     * 项目组编号
     */
    private String projectGroupCode;
    /**
     * 项目组名称
     */
    private String projectGroupName;
    /**
     * 项目id
     */
    private Long projectId;
    /**
     * 项目编号
     */
    private String projectCode;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 姓名
     */
    private String name;
    /**
     * 部门
     */
    private String department;
    /**
     * 岗位
     */
    private String occupation;
    /**
     * 状态
     */
    private String status;
    /**
     * 备注
     */
    private String remark;

    private  String systemName;

    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String systemId;

    private String percentage;

    private String time;

    private String managerConfirm;

    /**
     * 驳回原因
     */
    private String rejectReason;

    /**
     * 经理驳回时间
     */
    private LocalDateTime managerRejectTime;

    /**
     * 经理操作时间
     */
    private LocalDateTime managerOperateTime;

    private Integer deptId;

}