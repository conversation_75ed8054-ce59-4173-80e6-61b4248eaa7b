package cn.iocoder.yudao.module.projectmanage.service.projectmaterialprocurement;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import cn.iocoder.yudao.module.projectmanage.controller.admin.projectmaterialprocurement.vo.*;
import cn.iocoder.yudao.module.projectmanage.dal.dataobject.projectmaterialprocurement.ProjectMaterialProcurementDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.projectmanage.dal.mysql.projectmaterialprocurement.ProjectMaterialProcurementMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.projectmanage.enums.ErrorCodeConstants.*;

/**
 * 物资采购审批 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ProjectMaterialProcurementServiceImpl implements ProjectMaterialProcurementService {

    @Resource
    private ProjectMaterialProcurementMapper projectMaterialProcurementMapper;

    @Override
    public Long createProjectMaterialProcurement(ProjectMaterialProcurementSaveReqVO createReqVO) {
        // 插入
        ProjectMaterialProcurementDO projectMaterialProcurement = BeanUtils.toBean(createReqVO, ProjectMaterialProcurementDO.class);
        projectMaterialProcurementMapper.insert(projectMaterialProcurement);
        // 返回
        return projectMaterialProcurement.getId();
    }

    @Override
    public void updateProjectMaterialProcurement(ProjectMaterialProcurementSaveReqVO updateReqVO) {
        // 校验存在
        validateProjectMaterialProcurementExists(updateReqVO.getId());
        // 更新
        ProjectMaterialProcurementDO updateObj = BeanUtils.toBean(updateReqVO, ProjectMaterialProcurementDO.class);
        projectMaterialProcurementMapper.updateById(updateObj);
    }

    @Override
    public void deleteProjectMaterialProcurement(Long id) {
        // 校验存在
        validateProjectMaterialProcurementExists(id);
        // 删除
        projectMaterialProcurementMapper.deleteById(id);
    }

    private void validateProjectMaterialProcurementExists(Long id) {
        if (projectMaterialProcurementMapper.selectById(id) == null) {
            throw exception(new ErrorCode(168, id + ",ProjectMaterialProcurement不存在"));
        }
    }

    @Override
    public ProjectMaterialProcurementDO getProjectMaterialProcurement(Long id) {
        return projectMaterialProcurementMapper.selectById(id);
    }

    @Override
    public PageResult<ProjectMaterialProcurementDO> getProjectMaterialProcurementPage(ProjectMaterialProcurementPageReqVO pageReqVO) {
        return projectMaterialProcurementMapper.selectPage(pageReqVO);
    }

    @Override
    public ProjectMaterialProcurementDO getProjectByFlowId(String flowId) {
        return projectMaterialProcurementMapper.selectOne(ProjectMaterialProcurementDO::getFlowId, flowId);
    }

}