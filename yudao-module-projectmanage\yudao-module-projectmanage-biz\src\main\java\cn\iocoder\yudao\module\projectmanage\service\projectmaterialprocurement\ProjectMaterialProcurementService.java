package cn.iocoder.yudao.module.projectmanage.service.projectmaterialprocurement;

import java.util.*;
import jakarta.validation.*;
import cn.iocoder.yudao.module.projectmanage.controller.admin.projectmaterialprocurement.vo.*;
import cn.iocoder.yudao.module.projectmanage.dal.dataobject.projectmaterialprocurement.ProjectMaterialProcurementDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 物资采购审批 Service 接口
 *
 * <AUTHOR>
 */
public interface ProjectMaterialProcurementService {

    /**
     * 创建物资采购审批
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createProjectMaterialProcurement(@Valid ProjectMaterialProcurementSaveReqVO createReqVO);

    /**
     * 更新物资采购审批
     *
     * @param updateReqVO 更新信息
     */
    void updateProjectMaterialProcurement(@Valid ProjectMaterialProcurementSaveReqVO updateReqVO);

    /**
     * 删除物资采购审批
     *
     * @param id 编号
     */
    void deleteProjectMaterialProcurement(Long id);

    /**
     * 获得物资采购审批
     *
     * @param id 编号
     * @return 物资采购审批
     */
    ProjectMaterialProcurementDO getProjectMaterialProcurement(Long id);

    /**
     * 获得物资采购审批分页
     *
     * @param pageReqVO 分页查询
     * @return 物资采购审批分页
     */
    PageResult<ProjectMaterialProcurementDO> getProjectMaterialProcurementPage(ProjectMaterialProcurementPageReqVO pageReqVO);

    ProjectMaterialProcurementDO getProjectByFlowId(String flowId);
}