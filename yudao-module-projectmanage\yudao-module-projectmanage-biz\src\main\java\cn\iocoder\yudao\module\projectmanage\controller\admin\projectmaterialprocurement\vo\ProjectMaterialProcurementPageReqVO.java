package cn.iocoder.yudao.module.projectmanage.controller.admin.projectmaterialprocurement.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 物资采购审批分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProjectMaterialProcurementPageReqVO extends PageParam {

    @Schema(description = "项目id", example = "31102")
    private Long projectId;

    @Schema(description = "模版id", example = "18503")
    private String fdTemplataId;

    @Schema(description = "流程id", example = "12756")
    private String flowId;

    @Schema(description = "发起人")
    private String docCreator;

    @Schema(description = "项目名称", example = "芋艿")
    private String projectName;

    @Schema(description = "具体内容")
    private String detail;

    @Schema(description = "预算金额")
    private BigDecimal estimateMoney;

    @Schema(description = "比价邀标情况")
    private String priceComparison;

    @Schema(description = "是否列入固定资产")
    private String isAssets;

    @Schema(description = "审批状态", example = "1")
    private String status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}