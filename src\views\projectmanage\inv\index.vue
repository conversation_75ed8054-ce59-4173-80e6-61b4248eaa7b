<template>

  <el-tabs model-value="开票">
    <!--      name="invDetail"-->
    <el-tab-pane label="开票" name="开票">
      <ContentWrap>
        <!-- 搜索工作栏 -->
        <el-form
          class="-mb-15px"
          :model="queryParams"
          ref="queryFormRef"
          :inline="true"
          label-width="90px"
        >
          <el-form-item label="项目编号" prop="projectCode">
            <el-input
              v-model="queryParams.projectCode"
              placeholder="请输入项目编号"
              clearable
              @keyup.enter="handleQuery"
              class="!w-240px"
            />
          </el-form-item>
          <el-form-item label="项目名称" prop="projectName">
            <el-input
              v-model="queryParams.projectName"
              placeholder="请输入项目名称"
              clearable
              @keyup.enter="handleQuery"
              class="!w-240px"
            />
          </el-form-item>
          <el-form-item label="合同id" prop="contractId">
            <el-input
              v-model="queryParams.contractId"
              placeholder="请输入合同id"
              clearable
              @keyup.enter="handleQuery"
              class="!w-240px"
            />
          </el-form-item>
          <el-form-item label="合同编号" prop="contractCode">
            <el-input
              v-model="queryParams.contractCode"
              placeholder="请输入合同编号"
              clearable
              @keyup.enter="handleQuery"
              class="!w-240px"
            />
          </el-form-item>
          <el-form-item label="合同名称" prop="contractName">
            <el-input
              v-model="queryParams.contractName"
              placeholder="请输入合同名称"
              clearable
              @keyup.enter="handleQuery"
              class="!w-240px"
            />
          </el-form-item>
          <el-form-item label="方式" prop="method">
            <el-select
              v-model="queryParams.method"
              placeholder="请选择方式"
              clearable
              class="!w-240px"
            >
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.FEE_METHOD)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="类型" prop="type">
            <el-select
              v-model="queryParams.type"
              placeholder="请选择类型"
              clearable
              class="!w-240px"
            >
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.FEE_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="付款方" prop="payer">
            <el-input
              v-model="queryParams.payer"
              placeholder="请输入付款方"
              clearable
              @keyup.enter="handleQuery"
              class="!w-240px"
            />
          </el-form-item>
          <el-form-item>
            <el-button @click="handleQuery">搜索</el-button>
            <el-button @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </ContentWrap>
      <!-- 列表 -->
      <ContentWrap>
        <el-badge style="margin-top: 10px;" :value="totalAddInv" class="item">
          <el-button
            type="primary"
            plain
            @click="addInvBut()"
          >
            新增开票
          </el-button>
        </el-badge>
        <el-table
          v-loading="loading"
          :data="list"

          :show-overflow-tooltip="true"
          style="width: 100%;"
          @row-click="handleRowClick"
          :row-class-name="tableRowClassName"
        >
          <!--      <el-table-column type="expand">-->
          <!--        <template #default="props">-->
          <!--          <el-form label-position="left" inline>-->
          <!--            <el-form-item label="状态：">-->
          <!--              <span>{{ props.row.status }}</span>-->
          <!--            </el-form-item>-->
          <!--            <el-form-item label="合同名称：">-->
          <!--              <span>{{ props.row.contractName }}</span>-->
          <!--            </el-form-item>-->
          <!--            <el-form-item label="方式：">-->
          <!--              <span>{{ props.row.method }}</span>-->
          <!--            </el-form-item>-->
          <!--            <el-form-item label="类型：">-->
          <!--              <span>{{ props.row.type }}</span>-->
          <!--            </el-form-item>-->
          <!--            <el-form-item label="占比：">-->
          <!--              <span>{{ props.row.proportion }}</span>-->
          <!--            </el-form-item>-->
          <!--            <el-form-item label="币种：">-->
          <!--              <span>{{ props.row.currency }}</span>-->
          <!--            </el-form-item>-->
          <!--            <el-form-item label="备注：">-->
          <!--              <span>{{ props.row.remarks }}</span>-->
          <!--            </el-form-item>-->
          <!--          </el-form>-->
          <!--        </template>-->
          <!--      </el-table-column>-->
          <el-table-column label="客戶" show-overflow-tooltip prop="buyer" width="250"
                           align="center"/>
          <el-table-column label="项目名称" prop="projectName" show-overflow-tooltip width="230"
                           align="center"/>
          <el-table-column label="发票类型" prop="invType" width="80" align="center"/>

          <el-table-column label="含税金额" align="center" prop="price" width="120">
            <template #default="scope">
              {{ formatCurrency(scope.row.price) }}
            </template>
          </el-table-column>
          <!--          <el-table-column label="税率" prop="taxRate" width="60" align="center"/>-->
          <el-table-column label="不含税金额" align="center" prop="noTaxPrice" width="120">
            <template #default="scope">
              {{ formatCurrency(scope.row.noTaxPrice) }}
            </template>
          </el-table-column>

          <el-table-column label="税额" align="center" prop="tax" width="120">
            <template #default="scope">
              {{ formatCurrency(scope.row.tax) }}
            </template>
          </el-table-column>
          <el-table-column label="发票号码" prop="invNo" width="200" align="center"/>
          <!--          <el-table-column label="返回描述" prop="reTxt" width="190" align="center"/>-->
          <el-table-column label="申请人" prop="applyName" width="100" align="center"/>
<!--          <el-table-column label="开票人" prop="kaipiaoren" width="100" align="center"/>-->
          <el-table-column label="提交信息" prop="reTxt" show-overflow-tooltip width="160"
                           align="center"/>
          <!--          prop="isRed"-->
          <el-table-column label="是否冲红" show-overflow-tooltip width="100"
                           align="center">
            <template #default="scope">
              <el-tag v-if="scope.row.isRed == `是`" type="danger">是</el-tag>
              <el-tag v-else type="success">否</el-tag>
            </template>
          </el-table-column>

          <el-table-column label="开票时间" show-overflow-tooltip width="110"
                           align="center">
            <template #default="scope">
              {{ scope.row.invDate.slice(0, 4) }}-{{
                scope.row.invDate.slice(4, 6)
              }}-{{ scope.row.invDate.slice(6, 8) }}
            </template>
          </el-table-column>

          <el-table-column label="冲红信息" prop="redReTxt" show-overflow-tooltip width="180"
                           align="center"/>
          <el-table-column label="开票人" prop="kaipiaoren" width="100" align="center"/>

          <!--        -->
          <el-table-column
            fixed="right"
            label="操作"
            align="center"
            width="300"
            class-name="small-padding fixed-width"
          >
            <template #default="scope">
              <el-button
                link
                type="danger"
                v-if="scope.row.reTxt === '已在税局开票'"
                @click="handleDeleteInv(scope.row.id)"
              >
                删除
              </el-button>

              <el-button
                link
                type="primary"
                @click="downloadInv(scope.row)"
              >
                下载
              </el-button>


              <el-button
                link
                type="primary"
                @click="openPdf(scope.row)"
              >
                预览
              </el-button>

              <el-button
                link
                type="danger"
                @click="hongpiaoyulan(scope.row)"
              >
                红票
              </el-button>
              <el-button
                link
                type="danger"
                @click="chonghong(scope.row)"
              >
                冲红
              </el-button>
              <el-button
                link
                type="danger"
              >
                重推
              </el-button>
              <!--              <el-button-->
              <!--                link-->
              <!--                type="danger"-->
              <!--                @click="delInv(scope.row)"-->
              <!--              >-->
              <!--                取消-->
              <!--              </el-button>-->
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </ContentWrap>
      <ContentWrap>
        <el-tabs model-value="invDetail">
          <!--      name="invDetail"-->
          <el-tab-pane label="发票清单" name="invDetail">
            <el-table v-loading="detailLoading" :data="detailList" :stripe="true"
                      :show-overflow-tooltip="true"
                      :summary-method="summaryMethodForInvDetail"
                      show-summary>
              <!--          <el-table-column label="id" align="center" prop="id" />-->
              <!--          <el-table-column label="feeId" align="center" prop="feeId" />-->
              <el-table-column label="项目名称" align="center" prop="goodsname" width="230"/>
              <el-table-column label="规格型号" align="center" prop="standard"/>
              <el-table-column label="单位" align="center" prop="unit"/>
              <el-table-column label="数量" align="center" prop="number"/>
              <el-table-column label="单价" align="center" prop="price"/>
              <el-table-column label="金额" align="center" prop="noTaxAmount">
                <template #default="scope">
                  {{ formatCurrency(scope.row.noTaxAmount) }}
                </template>
              </el-table-column>
              <el-table-column label="税率/征收率" align="center" prop="taxrate"/>
              <el-table-column label="税额" align="center" prop="taxamount"/>
              <!--          <el-table-column label="含税总金额" align="center" prop="allAmount" />-->
              <!--          <el-table-column label="金额" align="center" prop="noTaxAmount" />-->
              <!--          <el-table-column label="数量" align="center" prop="number" />-->
              <!--          <el-table-column label="单价" align="center" prop="price" />-->
              <!--          <el-table-column label="单位" align="center" prop="unit" />-->
              <!--          <el-table-column label="含税标记(0不含税，1含税)" align="center" prop="pricekind" />-->
              <!--          <el-table-column label="商品规格" align="center" prop="standard" />-->
              <!--          <el-table-column label="税率" align="center" prop="taxrate" />-->
              <!--          <el-table-column label="税额" align="center" prop="taxamount" />-->
              <!--          <el-table-column label="税收分类编码" align="center" prop="goodstaxno" />-->
              <!--          <el-table-column label="优惠政策" align="center" prop="taxpre" />-->
              <!--          <el-table-column label="优惠政策内容(免税，不征税)" align="center" prop="taxprecon" />-->
              <!--          <el-table-column label="零税率标志" align="center" prop="zerotax" />-->
              <!--          <el-table-column label="序号" align="center" prop="seqNo" />-->
              <!--          <el-table-column label="发票行性质" align="center" prop="invoiceLineProperty" />-->
              <!--          <el-table-column label="备注" align="center" prop="remark" />-->
              <!--          <el-table-column-->
              <!--            label="创建时间"-->
              <!--            align="center"-->
              <!--            prop="createTime"-->
              <!--            :formatter="dateFormatter"-->
              <!--            width="180px"-->
              <!--          />-->
            </el-table>
            <Pagination
              :total="totalDetail"
              v-model:page="detailQueryParams.pageNo"
              v-model:limit="detailQueryParams.pageSize"
              @pagination="getDetailList"
            />
          </el-tab-pane>
          <el-tab-pane label="业务信息" name="businessDetail">


            <el-table :data="feeDetailList_kp" :stripe="true" :show-overflow-tooltip="true"
                      :summary-method="summaryMethod"
                      show-summary>
              <el-table-column label="项目组代码" align="center" prop="groupCode"/>
              <el-table-column label="项目名" width="200" align="center" fixed="left">
                <template #default="scope">
                  <router-link :to="'/project/' + scope.row.projectId">
                    <el-button link type="primary">{{ scope.row.projectName }}</el-button>
                  </router-link>
                </template>
              </el-table-column>
              <el-table-column label="合同代码" align="center" prop="contractCode"/>
              <!--              <el-table-column label="合同名" align="center" prop="contractName"/>-->
              <el-table-column label="合同名" align="center" prop="contractName">
                <template #default="scope">
                  <el-button @click="openDrawer(scope.row)" type="text">{{
                      scope.row.contractName
                    }}
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column label="付款方" align="center" prop="payer"/>
              <el-table-column label="类型" align="center" prop="type"/>
              <el-table-column
                label="创建时间"
                align="center"
                prop="createTime"
                :formatter="dateFormatter"
                width="180px"
              />
              <!--            <el-table-column label="业务代码" align="center" prop="ywCode" />-->
              <!--            <el-table-column label="业务类型" align="center" prop="ywType" />-->
              <el-table-column label="总金额" align="center" prop="allAmount"/>
              <el-table-column label="开票金额" align="center" prop="detailAmount"/>
              <el-table-column label="占比" align="center" prop="proportion"/>
            </el-table>
          </el-tab-pane>
        </el-tabs>

      </ContentWrap>
      <!-- 新增开票弹窗 -->
      <el-dialog
        title="新增开票"
        v-model="addInvShow"
        width="70%"
        :close-on-click-modal="false"
        :close-on-press-escape="false">
        <ContentWrap :title="`申请发票`">
          <el-table
            v-loading="loading"
            :data="addInvData"
            :stripe="true"
            :show-overflow-tooltip="true"
            style="width: 100%;"
            @selection-change="handleSelectionChangeAddInvData"
            ref="SelectTableInv"
          >
            <el-table-column type="selection" :selectable="checkSelectable"/>
            <!--      <el-table-column label="项目编号" align="center" prop="projectCode"/>-->
            <!--      <el-table-column label="项目组编号" align="center" prop="projectGroupCode"/>-->
            <el-table-column label="付款方" align="center" prop="customerName" width="200" fixed/>
            <el-table-column label="项目名称" align="center" prop="projectName">
              <template #default="scope">
                <el-button @click="openDrawer(scope.row)" type="text">{{ scope.row.projectName }}
                </el-button>
              </template>
            </el-table-column>
            <!--      <el-table-column label="合同id" align="center" prop="contractId"/>-->
            <!--      <el-table-column label="合同编号" align="center" prop="contractCode"/>-->
            <!--            <el-table-column label="合同名称" align="center" prop="contractName"/>-->

            <!--            <el-table-column label="方式" align="center" prop="method"/>-->
            <el-table-column label="发票类型" align="center" width="150" prop="invType"/>
            <el-table-column label="开票金额" align="center" width="150" prop="price">
              <template #default="scope">
                {{ formatCurrency(scope.row.price) }}
              </template>
            </el-table-column>
            <el-table-column label="申请人" align="center" width="100" prop="applyName"/>
            <el-table-column label="申请时间" align="center" width="200" prop="applyDate"/>
            <!--            <el-table-column label="币种" align="center" width="100" prop="currency"/>-->
            <!--            <el-table-column label="收付关系" align="center" width="100" prop="paymentRelation"/>-->
            <!--      <el-table-column label="收款方" align="center" prop="payee"/>-->
            <!--            <el-table-column label="备注" align="center" prop="remarks"/>-->
            <!--            <el-table-column-->
            <!--              label="创建时间"-->
            <!--              align="center"-->
            <!--              prop="createTime"-->
            <!--              :formatter="dateFormatter"-->
            <!--              width="180px"-->
            <!--            />-->
          </el-table>

          <!-- 分页 -->
          <Pagination
            :total="totalAddInv"
            v-model:page="queryParamsAddInV.pageNo"
            v-model:limit="queryParamsAddInV.pageSize"
            @pagination="getListAddInv"
          />
        </ContentWrap>


        <ContentWrap :title="`发票数据`" v-if="selectedAddInvRow">
          <el-form label-width="150px" v-loading="loadingAdd">
            <!-- 基本信息 -->
            <div class="form-section">
              <el-row>
                <el-col :span="8">
                  <el-form-item label="发票抬头" prop="title">
                    <el-input v-if="selectedAddInvRow" v-model="selectedAddInvRow.buyer"
                              placeholder="请输入发票抬头" clearable class="!w-480px"/>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="客户税号" prop="title">
                    <el-input v-model="selectedAddInvRow.buyTaxNo" placeholder="客户税号" clearable
                              class="!w-480px"/>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="发票类型">
                    <el-select v-model="selectedAddInvRow.invType" placeholder="请选择发票类型"
                               clearable class="!w-480px">
                      <el-option label="专票" value="专票"/>
                      <el-option label="普票" value="普票"/>
                    </el-select>
                  </el-form-item>
                </el-col>
                <!--                <el-col :span="6">-->
                <!--                  <el-form-item label="税率">-->
                <!--                    <el-input v-model="selectedAddInvRow.taxRate" placeholder="请输入税率" clearable-->
                <!--                              class="!w-480px">-->
                <!--                      <template #append>%</template>-->
                <!--                    </el-input>-->
                <!--                  </el-form-item>-->
                <!--                </el-col>-->
              </el-row>

              <!--          <el-row >-->
              <!--            <el-col :span="8">-->
              <!--              <el-form-item label="购方地址">-->
              <!--                <el-input v-model="selectedAddInvRow.buyerAddr" placeholder="请输入购方地址" clearable class="!w-240px"/>-->
              <!--              </el-form-item>-->
              <!--            </el-col>-->
              <!--            <el-col :span="8">-->
              <!--              <el-form-item label="购方银行">-->
              <!--                <el-input v-model="selectedAddInvRow.buyerBank" placeholder="请输入购方银行" clearable class="!w-240px"/>-->
              <!--              </el-form-item>-->
              <!--            </el-col>-->
              <!--            <el-col :span="8">-->
              <!--              <el-form-item label="购方银行账号">-->
              <!--                <el-input placeholder="请输入银行账号" clearable class="!w-240px"/>-->
              <!--              </el-form-item>-->
              <!--            </el-col>-->
              <!--          </el-row>-->
              <!--          <el-row >-->
              <!--            <el-col :span="8">-->
              <!--              <el-form-item label="收款人">-->
              <!--                <el-input placeholder="请输入收款人" clearable class="!w-240px"/>-->
              <!--              </el-form-item>-->
              <!--            </el-col>-->
              <!--            <el-col :span="8">-->
              <!--              <el-form-item label="复核人">-->
              <!--                <el-input placeholder="请输入复核人" clearable class="!w-240px"/>-->
              <!--              </el-form-item>-->
              <!--            </el-col>-->
              <!--            <el-col :span="8">-->
              <!--              <el-form-item label="开票人">-->
              <!--                <el-input placeholder="请输入开票人" clearable class="!w-240px"/>-->
              <!--              </el-form-item>-->
              <!--            </el-col>-->
              <!--          </el-row>-->
              <!--              <el-row>-->
              <!--                <el-col :span="8">-->
              <!--                  <el-form-item label="开票总金额">-->
              <!--                    <el-input v-model="selectedAddInvRow.price" placeholder="请输入总金额" clearable-->
              <!--                              class="!w-240px">-->
              <!--                      <template #append>元</template>-->
              <!--                    </el-input>-->
              <!--                  </el-form-item>-->
              <!--                </el-col>-->
              <!--                <el-col :span="8">-->
              <!--                  <el-form-item label="不含税金额">-->
              <!--                    <el-input v-model="selectedAddInvRow.noTaxPrice" placeholder="请输入不含税金额"-->
              <!--                              clearable class="!w-240px">-->
              <!--                      <template #append>元</template>-->
              <!--                    </el-input>-->
              <!--                  </el-form-item>-->
              <!--                </el-col>-->
              <!--                <el-col :span="8">-->
              <!--                  <el-form-item label="税额">-->
              <!--                    <el-input v-model="selectedAddInvRow.tax" placeholder="请输入税额" clearable-->
              <!--                              class="!w-240px">-->
              <!--                      <template #append>元</template>-->
              <!--                    </el-input>-->
              <!--                  </el-form-item>-->
              <!--                </el-col>-->
              <!--              </el-row>-->
              <!--          <el-row >-->
              <!--            <el-col :span="12">-->
              <!--              <el-form-item label="是否自然人">-->
              <!--                <el-select  v-model="selectedAddInvRow.isPerson" placeholder="请选择" clearable class="!w-240px">-->
              <!--                  <el-option label="是" value="是"/>-->
              <!--                  <el-option label="否" value="否"/>-->
              <!--                </el-select>-->
              <!--              </el-form-item>-->
              <!--            </el-col>-->
              <!--            <el-col :span="12">-->
              <!--              <el-form-item label="备注是否显示账户">-->
              <!--                <el-select placeholder="请选择" clearable class="!w-240px">-->
              <!--                  <el-option label="是" value="是"/>-->
              <!--                  <el-option label="否" value="否"/>-->
              <!--                </el-select>-->
              <!--              </el-form-item>-->
              <!--            </el-col>-->
              <!--          </el-row>-->

              <el-row>
                <el-col :span="12">
                  <el-form-item label="联系邮箱">
                    <el-input v-model="selectedAddInvRow.email" placeholder="请输入邮箱" clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="联系电话">
                    <el-input v-model="selectedAddInvRow.tel" placeholder="请输入电话" clearable
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="发票备注">
                    <el-input type="textarea" v-model="selectedAddInvRow.invRemark"
                              placeholder="发票备注" clearable
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-form>


          <span style="color: red;margin-top: 10px">本张发票总金额:{{
              selectedAddInvRow.price
            }} </span><br/>
          <span style="color: red;margin-top: 10px">剩余分配金额:{{ fpsyje }} </span>
          <!--          <el-button type="text" @click="allPriceAllocation">全额</el-button>-->
          <br/>

          <el-form ref="queryForm"
                   :inline="true"
          >
            <el-form-item>
              <el-input
                placeholder="含税总金额"
                clearable
                style="width: 100px"
                @change="dologicAddInvocieChangeHszj"
                v-model="addInvocieDatailClass.allAmount"
                :disabled="Number(fpsyje)==Number(0)"
              />
            </el-form-item>
            <el-form-item>
              <el-select placeholder="发票商品分类" v-model="addInvocieDatailClass.goodstaxno"
                         style="width: 150px" clearable filterable
                         :disabled="Number(fpsyje)==Number(0)"
              >
                <!--                v-for="dict in getStrDictOptions('inv_goods_type') "-->
                <el-option
                  v-for="dict in invGoodsType "
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item prop="goodsname">
              <el-input
                placeholder="项目名称"
                clearable
                style="width: 200px"
                v-model="addInvocieDatailClass.goodsname"
                :disabled="Number(fpsyje)==Number(0)"
              />
            </el-form-item>
            <el-form-item>
              <el-input
                placeholder="商品规格"
                clearable
                style="width: 80px"
                v-model="addInvocieDatailClass.standard"
                :disabled="Number(fpsyje)==Number(0)"
              />
            </el-form-item>
            <el-form-item>
              <el-input
                placeholder="商品数量"
                clearable
                style="width: 50px"
                v-model="addInvocieDatailClass.number"
                @change="dologicAddInvocieChangeNumber"
                :disabled="Number(fpsyje)==Number(0)"
              />
            </el-form-item>
            <el-form-item>
              <el-input
                placeholder="单价"
                clearable
                style="width: 80px"
                v-model="addInvocieDatailClass.price"
                @change="dologicAddInvocieChangePrice"
                :disabled="Number(fpsyje)==Number(0)"
              />
            </el-form-item>
            <el-form-item>
              <el-input
                placeholder="单位"
                clearable
                style="width: 50px"
                v-model="addInvocieDatailClass.unit"
                :disabled="Number(fpsyje)==Number(0)"
              />
            </el-form-item>
            <el-form-item>
              <!--              <el-input-->
              <!--                placeholder="税率"-->
              <!--                readonly-->
              <!--                size="small"-->
              <!--                style="width: 60px"-->
              <!--                v-model="addInvocieDatailClass.taxrate"-->
              <!--                :disabled="Number(fpsyje)==Number(0)"-->
              <!--              />-->

              <el-select placeholder="税率" v-model="addInvocieDatailClass.taxrate"
                         style="width: 80px" clearable
                         @change="dologicAddInvocieChangeTaxrate"
                         :disabled="Number(fpsyje)==Number(0)"
              >
                <el-option
                  v-for="dict in rateOption"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>


            </el-form-item>
            <el-form-item>
              <el-input
                placeholder="税额"
                readonly
                style="width: 100px"
                v-model="addInvocieDatailClass.taxamount"
                :disabled="Number(fpsyje)==Number(0)"
              />
            </el-form-item>
            <el-form-item>
              <el-input
                placeholder="金额"
                readonly
                style="width: 100px"
                v-model="addInvocieDatailClass.noTaxAmount"
                :disabled="Number(fpsyje)==Number(0)"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" :disabled="Number(fpsyje)==Number(0)"
                         @click="addInvocieDatailBut">添加
              </el-button>
            </el-form-item>
          </el-form>
          <el-table :data="userFInvocieDetailList" highlight-current-row>
            <el-table-column prop="allAmount" label="含税总金额" align="center" width="120px"/>
            <el-table-column label="商品分类" align="center" width="150px">
              <template #default="scope">
                <el-select placeholder="发票商品分类" v-model="scope.row.goodstaxno"
                           style="width: 150px" clearable filterable
                >
                  <el-option
                    v-for="dict in invGoodsType "
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </template>
            </el-table-column>

            <el-table-column prop="goodsname" label="项目名称" align="center" width="180px"/>
            <el-table-column prop="standard" label="商品规格" align="center" width="80px"/>
            <el-table-column prop="unit" label="单位" style="width: 10px" align="center"/>
            <el-table-column prop="number" label="数量" align="center" width="100px"/>
            <el-table-column prop="price" label="单价" align="center" width="100px"/>
            <el-table-column prop="noTaxAmount" label="金额" align="center" width="100px"/>
            <el-table-column prop="taxrate" label="税率" style="width: 20px" align="center"/>
            <el-table-column prop="taxamount" label="税额" align="center" width="100px"/>
            <el-table-column
              fixed="right"
              label="操作"
              align="center"
              width="150"
              class-name="small-padding fixed-width"
            >
              <template #default="scope">
                <!--              <el-button-->
                <!--                size="mini"-->
                <!--                type="text"-->
                <!--                @click="xgUserFInvocieDetailList(scope.row,scope.$index)"-->
                <!--              >修改-->
                <!--              </el-button>-->
                <el-button
                  type="text"
                  style="color: red"
                  @click="delUserFInvocieDetailList(scope.row,scope.$index)"
                >删除
                </el-button>

                <el-button
                  type="text"
                  @click="upUserFInvocieDetailList(scope.row,scope.$index)"
                >上移
                </el-button>

                <el-button
                  type="text"
                  @click="downUserFInvocieDetailList(scope.row,scope.$index)"
                >下移
                </el-button>

              </template>
            </el-table-column>
          </el-table>

        </ContentWrap>

        <template #footer>
          <el-button v-if="selectedAddInvRow" type="warning" @click="createInvData">
            新增开票（税局已开）
          </el-button>
          <el-button @click="addInvShow = false">取消</el-button>
          <el-button v-if="selectedAddInvRow" type="danger" @click="breakFee">驳回</el-button>
          <el-button type="primary" @click="createInvDataBut">确认开票</el-button>
        </template>


      </el-dialog>
      <el-drawer
        title="合同详情"
        v-model="drawerVisible"
        :direction="rtl"
        size="45%"
        class="contract-drawer"
      >
        <div class="drawer-content">
          <!-- 税率和金额信息 -->
          <div class="section-title">税率及金额信息</div>
          <el-row>
            <el-col :span="24">
              <el-form-item prop="rate">
                <div v-for="(item, index) in one.ratesAndAmounts" :key="index"
                     class="rate-item">
                  <el-row :gutter="20">
                    <el-col :span="4">
                      <el-form-item label="税率">
                        <el-input readonly v-model="item.rate" placeholder="请选择税率"/>
                      </el-form-item>
                    </el-col>
                    <el-col :span="7">
                      <el-form-item label="含税金额">
                        <el-input readonly v-model="item.amount" placeholder="含税金额"/>
                      </el-form-item>
                    </el-col>
                    <el-col :span="7">
                      <el-form-item label="不含税金额">
                        <el-input
                          :value="(item.amount/(1+item.rate/100)).toFixed(2)"
                          placeholder="不含税金额" readonly/>
                      </el-form-item>
                    </el-col>
                    <el-col :span="6">
                      <el-form-item label="税额">
                        <el-input readonly
                                  :value="(item.amount/(1+item.rate/100)*(item.rate/100)).toFixed(2)"
                                  placeholder="税额"/>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </div>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 总金额信息 -->
          <div class="section-title">总金额信息</div>
          <el-row justify="center" class="total-amount-row">
            <el-col :span="6">
              <el-form-item label="含税总金额" prop="amount">
                <div class="amount-value">
                  {{ money_format(one.amount, 2, '.', ',') }}
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="不含税总金额" prop="amount">
                <div class="amount-value">
                  {{ money_format(getNoTaxPrice, 2, '.', ',') }}
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="总税额" prop="amount">
                <div class="amount-value">
                  {{ money_format(getTaxPrice, 2, '.', ',') }}
                </div>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 文件列表 -->
          <div class="section-title">相关文件</div>
          <el-row v-for="group in groupFileList" :key="group[0].fileBusinessTypeDetail"
                  class="file-group">
            <el-col :span="24">
              <div class="file-group-title">
                <span>{{ group[0].fileBusinessTypeDetail }}</span>
              </div>
              <UploadFile
                :modelValue="collectedUrls(group)"
                :businessId="one.id||0"
                :fileBusinessType="`合同`"
                :fileBusinessTypeDetail="group[0].fileBusinessTypeDetail"
                :url2Name="collectUrl2NameMap(group)"
                :businessFileList="group"
              />
            </el-col>
          </el-row>
        </div>
      </el-drawer>
    </el-tab-pane>

    <el-tab-pane label="收票" name="收票">

      <el-dialog
        title="新增收票"
        v-model="addInvShow_recInv"
        width="70%"
        :close-on-click-modal="false"
        :close-on-press-escape="false">

        <ContentWrap>
          <el-form
            :model="queryParamsAddInV_recInv"
            class="-mb-15px"
            label-width="68px"
            ref="addInvFormRef"
            :inline="true"
          >
            <el-form-item label="项目名称" prop="projectName">
              <el-input
                v-model="queryParamsAddInV_recInv.projectName"
                placeholder="请输入项目名称"
                class="!w-240px "
                clearable
                @keyup.enter="queryAddInv_recInv"/>
            </el-form-item>
            <el-button type="primary" @click="queryAddInv_recInv" class="mb-15px">搜索
            </el-button>
          </el-form>
        </ContentWrap>

        <el-table
          :data="addInvData_recInv"
          :stripe="true"
          :show-overflow-tooltip="true"
          style="width: 100%;"
          @selection-change="handleSelectionChangeAddInvData_recInv"
          ref="SelectTableInv_recInv"
        >
          <el-table-column type="selection"/>
          <!--      <el-table-column label="项目编号" align="center" prop="projectCode"/>-->
          <!--      <el-table-column label="项目组编号" align="center" prop="projectGroupCode"/>-->
          <el-table-column label="收款方" align="center" prop="payee" width="250" fixed/>
          <el-table-column label="项目名称" width="300" align="center" prop="projectName"/>
          <!--      <el-table-column label="合同id" align="center" prop="contractId"/>-->
          <!--      <el-table-column label="合同编号" align="center" prop="contractCode"/>-->
          <!--          <el-table-column label="合同名称" align="center" prop="contractName" width="250"/>-->
          <!--          <el-table-column label="方式" align="center" prop="method"/>-->
          <el-table-column label="类型" align="center" width="100" prop="type"/>
          <el-table-column label="金额" align="center" width="100" prop="amount"/>
          <!--          <el-table-column label="币种" align="center" width="100" prop="currency"/>-->
          <el-table-column label="收付关系" align="center" width="100" prop="paymentRelation"/>
          <!--      <el-table-column label="收款方" align="center" prop="payee"/>-->
          <el-table-column label="备注" align="center" prop="remarks"/>
          <el-table-column
            label="创建时间"
            align="center"
            prop="createTime"
            :formatter="dateFormatter"
            width="180px"
          />
        </el-table>

        <!--         分页 -->
        <Pagination
          :total="totalAddInv_recInv"
          v-model:page="queryParamsAddInV_recInv.pageNo"
          v-model:limit="queryParamsAddInV_recInv.pageSize"
          @pagination="getListAddInv_recInv"
        />


        <el-form style="margin-top: 75px" label-width="130px">
          <el-row>
            <!--            <el-col :span="12" style="text-align: center">-->
            <!--              <el-card shadow="hover" :span="12">-->
            <!--          <span style="color: red;font-size: xx-large">可收票金额：{{-->
            <!--              money_format(recInvAmount, 2, '.', ',')-->
            <!--            }}</span>-->
            <!--              </el-card>-->
            <!--            </el-col>-->
            <el-col :span="24" style="text-align: center">
              <el-card shadow="hover" :span="12">
                <span
                  style="color: green;font-size: xx-large">本次收票金额：{{
                    money_format(recInvAmount, 2, '.', ',')
                  }}</span>
              </el-card>
            </el-col>
          </el-row>
        </el-form>

        <!--        <el-form style="margin-top: 20px" label-width="130px">-->
        <!--          <el-row>-->
        <!--            <el-col :span="24" style="text-align: center">-->
        <!--              <el-card shadow="hover" :span="24">-->
        <!--                <span style="color: red;font-size: xx-large">收票金额：{{-->
        <!--                    money_format(recInvAmount, 2, '.', ',')-->
        <!--                  }}</span>-->
        <!--              </el-card>-->
        <!--            </el-col>-->
        <!--          </el-row>-->
        <!--        </el-form>-->
        <!--        收票时间-->
        <!--        <el-form-item label="上传发票" prop="invUrl" style="margin-top: 20px">-->
        <!--          <UploadFile v-model="addRecInv.invUrl"/>-->
        <!--        </el-form-item>-->

        <el-row v-for="group in groupFileList2" :key="group[0].fileBusinessTypeDetail">
          <el-col :span="24">
            <!--            <el-card>-->
            <div>
              <span>{{ group[0].fileBusinessTypeDetail }}</span>
            </div>
            <!--group是个数组，每个对象里面有个url,  获取所有的url, 然后v-modle=所有的url,modelValue是url数组 -->
            <UploadFile :modelValue="collectedUrls(group)"
                        :businessId="addRecInv.id||-19"
                        :fileBusinessType="`收票`"
                        :fileBusinessTypeDetail="group[0].fileBusinessTypeDetail"
                        :url2Name="collectUrl2NameMap(group)"
                        :businessFileList="group"
            />
            <!--            </el-card>-->
          </el-col>
        </el-row>

        <template #footer>
          <el-button type="primary" @click="addRecInvConfrimBut">确 定</el-button>
          <el-button @click="handleCancel">取 消</el-button>
        </template>
      </el-dialog>

      <ContentWrap>
        <!-- 搜索工作栏 -->
        <el-form
          class="-mb-15px"
          :model="queryParams_recInv"
          ref="queryFormRef_recInv"
          :inline="true"
          label-width="90px"
        >
          <el-form-item label="收票流水号" prop="recInvCode">
            <el-input
              v-model="queryParams_recInv.recInvCode"
              placeholder="请输入收票流水号"
              clearable
              @keyup.enter="handleQuery_recInv"
              class="!w-240px"
            />
          </el-form-item>
          <el-form-item label="发票号" prop="recInvNo">
            <el-input
              v-model="queryParams.recInvNo"
              placeholder="请输入收票流水号"
              clearable
              @keyup.enter="handleQuery_recInv"
              class="!w-240px"
            />
          </el-form-item>
          <el-form-item label="收票金额" prop="recInvPrice">
            <el-input
              v-model="queryParams.recInvPrice"
              placeholder="请输入收票金额"
              clearable
              @keyup.enter="handleQuery_recInv"
              class="!w-240px"
            />
          </el-form-item>
          <el-form-item label="客户名" prop="customerName">
            <el-input
              v-model="queryParams.customerName"
              placeholder="请输入客户名"
              clearable
              @keyup.enter="handleQuery_recInv"
              class="!w-240px"
            />
          </el-form-item>
          <!--          <el-form-item label="客户id" prop="customerId">-->
          <!--            <el-input-->
          <!--              v-model="queryParams.customerId"-->
          <!--              placeholder="请输入客户id"-->
          <!--              clearable-->
          <!--              @keyup.enter="handleQuery_recInv"-->
          <!--              class="!w-240px"-->
          <!--            />-->
          <!--          </el-form-item>-->

          <el-form-item>
            <el-button @click="handleQuery_recInv">
              <Icon icon="ep:search" class="mr-5px"/>
              搜索
            </el-button>
            <el-button @click="resetQuery_recInv">
              <Icon icon="ep:refresh" class="mr-5px"/>
              重置
            </el-button>
            <!--            <el-button-->
            <!--              type="primary"-->
            <!--              plain-->
            <!--              @click="openForm('create')"-->
            <!--              v-hasPermi="['projectmanage:rec-inv:create']"-->
            <!--            >-->
            <!--              <Icon icon="ep:plus" class="mr-5px" /> 新增-->
            <!--            </el-button>-->
            <el-button
              type="success"
              plain
              @click="handleExport"
              :loading="exportLoading"
              v-hasPermi="['projectmanage:rec-inv:export']"
            >
              <Icon icon="ep:download" class="mr-5px"/>
              导出
            </el-button>
          </el-form-item>
        </el-form>
      </ContentWrap>

      <!-- 列表 -->
      <ContentWrap>
        <el-button
          type="primary"
          plain
          @click="addInvBut_recInv()"
        >
          新增收票
        </el-button>
        <el-table :data="list_recInv" :stripe="true"
                  :show-overflow-tooltip="true" @rowClick="recInvClick">
          <!--          <el-table-column label="id" align="center" prop="id"/>-->
          <el-table-column label="收票流水号" align="center" prop="recInvCode"/>
          <el-table-column label="开票方" align="center" prop="customerName"/>
          <el-table-column label="收票金额" align="center" prop="recInvPrice"/>
          <!--          <el-table-column label="客户id" align="center" prop="customerId" />-->
          <el-table-column label="收票时间" align="center" prop="recInvDate"/>
          <!--          <el-table-column label="是否添加凭证" align="center" prop="isPz" />-->
          <!--          <el-table-column label="凭证添加时间" align="center" prop="pzTime" />-->
          <!--          <el-table-column label="凭证制作人" align="center" prop="pzName" />-->
          <!--          <el-table-column label="备注" align="center" prop="remark"/>-->
          <el-table-column
            label="创建时间"
            align="center"
            prop="createTime"
            :formatter="dateFormatter"
            width="180px"
          />

          <el-table-column label="附件" align="center" width="100px">
            <template #default="scope">
              <businessUploadFile :businessId="scope.row.id" :fileBusinessType="'收票'"/>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" min-width="120px">
            <template #default="scope">
              <!--              <el-button-->
              <!--                link-->
              <!--                type="primary"-->
              <!--                @click="downloadRecInv(scope.row)"-->
              <!--              >-->
              <!--                预览-->
              <!--              </el-button>-->
              <!--              <el-button-->
              <!--                link-->
              <!--                type="primary"-->
              <!--                v-hasPermi="['projectmanage:rec-inv:update']"-->
              <!--                @click="editRecInv(scope.row)"-->
              <!--              >-->
              <!--                编辑-->
              <!--              </el-button>-->
              <el-button
                link
                type="danger"
                @click="deleteRecInv_yw(scope.row.id)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <Pagination
          :total="total_recInv"
          v-model:page="queryParams_recInv.pageNo"
          v-model:limit="queryParams_recInv.pageSize"
          @pagination="getList_recInv"
        />
      </ContentWrap>


      <ContentWrap>
        <el-tabs model-value="业务信息">
          <!--      name="invDetail"-->
          <el-tab-pane label="业务信息" name="业务信息">
            <el-table :data="feeDetailList_sp" :stripe="true" :show-overflow-tooltip="true">
              <!--            <el-table-column label="id" align="center" prop="id" />-->
              <!--            <el-table-column label="项目组id" align="center" prop="groupId" />-->
              <el-table-column label="项目组代码" align="center" prop="groupCode"/>
              <!--            <el-table-column label="项目组名字" align="center" prop="groupName" />-->
              <!--            <el-table-column label="项目id" align="center" prop="projectId" />-->
              <!--            <el-table-column label="项目代码" align="center" prop="projectCode" />-->
              <!--              <el-table-column label="项目名" align="center" prop="projectName"/>-->

              <el-table-column label="项目名" width="200" align="center" fixed="left">
                <template #default="scope">
                  <router-link :to="'/project/' + scope.row.projectId">
                    <el-button link type="primary">{{ scope.row.projectName }}</el-button>
                  </router-link>
                </template>
              </el-table-column>

              <!--            <el-table-column label="合同id" align="center" prop="contractId" />-->
              <el-table-column label="合同代码" align="center" prop="contractCode"/>
              <el-table-column label="合同名" align="center" prop="contractName"/>
              <!--            <el-table-column label="feeId" align="center" prop="feeId" />-->
              <el-table-column label="付款方" align="center" prop="payer"/>
              <el-table-column label="收款方" align="center" prop="payee"/>
              <!--            <el-table-column label="支付方式" align="center" prop="method" />-->
              <!--            <el-table-column label="收付关系" align="center" prop="paymentRelation" />-->
              <el-table-column label="类型" align="center" prop="type"/>

              <!--            <el-table-column label="币种" align="center" prop="currency" />-->
              <!--            <el-table-column-->
              <!--              label="发生时间"-->
              <!--              align="center"-->
              <!--              prop="occurrenceDate"-->
              <!--              :formatter="dateFormatter"-->
              <!--              width="180px"-->
              <!--            />-->
              <el-table-column label="占比" align="center" prop="proportion"/>
              <el-table-column label="备注" align="center" prop="remarks"/>
              <!--              <el-table-column-->
              <!--                label="创建时间"-->
              <!--                align="center"-->
              <!--                prop="createTime"-->
              <!--                :formatter="dateFormatter"-->
              <!--                width="180px"-->
              <!--              />-->
              <!--            <el-table-column label="业务代码" align="center" prop="ywCode" />-->
              <!--            <el-table-column label="业务类型" align="center" prop="ywType" />-->
              <el-table-column label="总金额" align="center" prop="allAmount"/>
              <el-table-column label="具体金额" align="center" prop="detailAmount"/>
              <el-table-column label="占比" align="center" prop="proportion"/>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </ContentWrap>


    </el-tab-pane>
  </el-tabs>


</template>

<script setup lang="ts">
import {dateFormatter} from '@/utils/formatTime'
import {accAdd, Subtr} from "@/utils/add_sub";
import {FeeApi, FeeVO} from '@/api/projectmanage/fee'
import {DICT_TYPE, getStrDictOptions} from "@/utils/dict";
import {InvDetailApi, InvDetailVO} from "@/api/projectmanage/invdetail";
import {RecInvApi} from "@/api/projectmanage/recinv";
import {FeeDetailApi} from "@/api/projectmanage/feedetail";
import {InvMainApi} from "@/api/projectmanage/invmain";
import {ContractApi} from "@/api/projectmanage/contract";
import {reactive, ref} from "vue";
import {ElOption, ElSelect} from "element-plus";
import download from "@/utils/download";
import {PayApi} from "@/api/projectmanage/pay";
import {BusinessFileTypeApi} from "@/api/infraInfo/bussinessFileType";
import UploadFile from "../../../components/UploadFile/src/UploadFile.vue";
import businessUploadFile from '@/views/infraInfo/bussinessFileType/businessUploadFile.vue'

/** 项目费项 列表 */
defineOptions({name: 'Inv'})

const message = useMessage() // 消息弹窗
const {t} = useI18n() // 国际化
const drawerVisible = ref(false);
const contract = ref([]);
const selectedRatesAndAmounts = ref([]);
const addInvShow = ref(false) // 新增开票弹窗
const addInvShow_recInv = ref(false) // 新增开票弹窗
const addInvData = ref([]) // 新增开票数据
const addInvData_recInv = ref([]) // 新增开票数据
const loading = ref(true) // 列表的加载中
const loading_recInv = ref(true) // 列表的加载中
const loadingAddInv = ref(true) // 列表的加载中
const loadingAdd = ref(false) // 列表的加载中
const loadingAddInv_recInv = ref(true) // 列表的加载中
const feeList = ref([])
const groupFileList = ref([])
const fileList = ref([])
const list = ref<FeeVO[]>([]) // 列表的数据
const list_recInv = ref<FeeVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const total_recInv = ref(0) // 列表的总页数
const selectedAddInvRow = ref<any>(null)
const rateOption = ref([])
const selection_recInv = ref([])
const fpsyje = ref(0) // 剩余分配金额
const userFInvocieDetailList = ref([])
const addInvocieDatailClass = ref({})
//发票清单
const totalDetail = ref(0)
const detailList = ref<InvDetailVO[]>([])
const detailLoading = ref(false)
const businessLoading = ref(false)
const selectList = ref(null) // 选中的数据
const businessDetailList = ref([]); // 业务信息数据
const inv = ref(0) // 已开票数量
const groupFileList2 = ref([])
const fileList2 = ref([])

const one = ref({
  id: undefined,
  contractType: undefined,
  handler: undefined,
  executingDeparment: undefined,
  businessType: undefined,
  startTime: undefined,
  endTime: undefined,
  advanceAmount: undefined,
  status: undefined,
  approvalStatus: undefined,
  projectId: undefined,
  projectName: undefined,
  projectGroupId: undefined,
  projectGroupCode: undefined,
  projectGroupName: undefined,
  contractCode: undefined,
  contractName: undefined,
  jiafangId: undefined,
  jiafangName: undefined,
  workDay: undefined,
  yifangId: undefined,
  yifangName: undefined,
  jiafangSignatory: undefined,
  yifangSignatory: undefined,
  signingTime: undefined,
  content: undefined,
  amount: undefined,
  currency: undefined,
  paymentRelationship: undefined,
  contractYear: undefined,
  isAdvancePayment: undefined,
  advanceCurrency: undefined,
  projectCode: undefined,
  remark: undefined,
  financialClassification: undefined,
  businessContractCode: undefined,
  sourceMethods: undefined,
  warrantyService: undefined,
  isWarranty: undefined,
  warrantyAmount: undefined,
  rate: undefined,
  ratesAndAmounts: [] as { rate: number | undefined, amount: number | undefined }[],
  paymentType: undefined,
  paymentDescription: undefined,
  taxRate: undefined,
  taxAmount: undefined,
  taxExclusiveAmount: undefined,
  mainOrPatch: undefined,
  mainId: undefined,
  patchId: undefined
})

const addRecInv = ref({
  id: undefined,
  recInvCode: undefined,
  recInvNo: undefined,
  recInvPrice: undefined,
  customerName: undefined,
  customerId: undefined,
  recInvDate: undefined,
  isPz: undefined,
  pzTime: undefined,
  pzName: undefined,
  invUrl: undefined,
  remark: undefined,
  fees: []
}) // 新增收款
const invGoodsType = getStrDictOptions('inv_goods_type')
const queryParamsAddInV = reactive({
  pageNo: 1,
  pageSize: 200,
  status: '',
})


const queryParamsAddInV_recInv = reactive({
  pageNo: 1,
  pageSize: 10,
  projectCode: undefined,
  projectGroupCode: undefined,
  projectName: undefined,
  contractId: undefined,
  contractCode: undefined,
  contractName: undefined,
  paymentRelation: '付',
  method: undefined,
  type: undefined,
  amount: undefined,
  currency: undefined,
  occurrenceDate: [],
  proportion: undefined,
  payer: undefined,
  payee: undefined,
  remarks: undefined,
  createTime: [],
  projectId: undefined,

  isInv: '',
  invDate: undefined,
  invCode: undefined,
  invEr: undefined,
  isToincome: undefined,
  toincomeDate: undefined,
  toincomeEr: undefined,
  isRec: undefined,
  recDate: undefined,
  recEr: undefined,
  status: '',

  isInvPay: '否',
  invDatePay: undefined,
  invCodePay: undefined,
  invErPay: undefined,
  isToincomePay: undefined,
  toincomeDatePay: undefined,
  toincomeErPay: undefined,
  isRecPay: undefined,
  recDatePay: undefined,
  recErPay: undefined,

  invType: undefined,
  buyer: undefined,
  buyerAddr: undefined,
  buyerBank: undefined,
  buyerBankNo: undefined,
  buyTaxNo: undefined,
  taxRate: undefined,
  shoukuanren: undefined,
  fuheren: undefined,
  kaipiaoren: undefined,
  invRemark: undefined,
  invNo: undefined,
  createInvDate: undefined,
  price: undefined,
  noTaxPrice: undefined,
  tax: undefined,
  reTxt: undefined,
  isRed: undefined,
  specialInvType: undefined,
  invWaterId: undefined,
  redReTxt: undefined,
  pdfUrl: undefined,
  odfUrl: undefined,
  email: undefined,
  tel: undefined,
  redPdf: undefined,
  redOdf: undefined,
  redInvNo: undefined,
  redInfoNo: undefined,
  redInfoId: undefined,
  redInvDate: undefined,
  isPerson: undefined,
  bankShow: undefined,
})

const queryParams_recInv = reactive({
  pageNo: 1,
  pageSize: 10,
  recInvCode: undefined,
  recInvNo: undefined,
  recInvPrice: undefined,
  customerName: undefined,
  customerId: undefined,
  recInvDate: [],
  isPz: undefined,
  pzTime: [],
  pzName: undefined,
  remark: undefined,
  createTime: [],
  projectName: undefined,
  projectId: undefined,
  projectCode: undefined,
  contractId: undefined,
  contractCode: undefined,
  contractName: undefined,
  projectGroupCode: undefined,
  invUrl: undefined,
})
const totalAddInv = ref(0) // 列表的总页数
const totalAddInv_recInv = ref(0) // 列表的总页数

const queryParams = reactive({
  pageNo: 1,
  pageSize: 20,
  status: ['已开票', '已冲红'],
})
const queryFormRef = ref() // 搜索的表单
const queryFormRef_recInv = ref() // 搜索的表单
const feeDetailList_kp = ref([]) // 业务信息数据
const feeDetailList_sp = ref([])
const queryParams_feeDetail_kp = reactive({
  pageNo: 1,
  pageSize: 1000,
  ywCode: '',
})
const queryParams_feeDetail_sp = reactive({
  pageNo: 1,
  pageSize: 1000,
  ywCode: '',
})

const detailQueryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  feeId: undefined,
  goodsname: undefined,
  allAmount: undefined,
  noTaxAmount: undefined,
  number: undefined,
  price: undefined,
  unit: undefined,
  pricekind: undefined,
  standard: undefined,
  taxrate: undefined,
  taxamount: undefined,
  goodstaxno: undefined,
  goodsTaxType: undefined,
  invMainId: undefined,
  invMainBillCode: undefined,
  taxpre: undefined,
  taxprecon: undefined,
  zerotax: undefined,
  seqNo: undefined,
  invoiceLineProperty: undefined,
  remark: undefined,
  createTime: [],
})

//新增收票弹窗搜索
const queryAddInv_recInv = () => {
  queryParamsAddInV_recInv.pageNo = 1
  getListAddInv_recInv()
};

const collectedUrls = (group) => {
  // 创建一个计算属性，返回当前 group 对象中所有 url 的数组
  return group.map(item => item.url);
};

const collectUrl2NameMap = (group) => {
  // 创建一个计算属性，返回当前 group 对象中所有 url 的数组
  const url2NameMap = {};
  group.forEach(item => {
    url2NameMap[item.url] = item.name;
  });
  console.log(url2NameMap)
  return url2NameMap;
};

const openDrawer = async (row) => {
  // 去请求合同信息,
  let data = await ContractApi.getContract(row.contractId)
  console.log("222222")
  console.log(row.contractId)
  console.log(data)
  one.value = reactive(data)
  feeList.value = data.fees || [];
  console.log(one)
  await getGroupFileList()
  drawerVisible.value = true
}

const getGroupFileList = async () => {

  let queryParams = {
    fileBusinessType: `合同`,
    businessId: one.value.id,
  }
  try {
    const data = await BusinessFileTypeApi.businessFileList(queryParams)
    fileList.value = data
    // fileList是一个对象数组，按照fileBusinessTypeDetail进行分组，形成一个二维数组groupFileList
    let group = {}
    for (let i = 0; i < fileList.value.length; i++) {
      let item = fileList.value[i]
      if (!group[item.fileBusinessTypeDetail]) {
        group[item.fileBusinessTypeDetail] = []
      }
      group[item.fileBusinessTypeDetail].push(item)
    }
    groupFileList.value = Object.values(group)
  } finally {

  }
};

const getGroupFileList2 = async () => {

  let queryParams = {
    fileBusinessType: `收票`,
    businessId: addRecInv.value.id || -19,// 新增时使用-19作为临时ID
  }
  try {
    const data = await BusinessFileTypeApi.businessFileList(queryParams)
    fileList2.value = data
    // fileList是一个对象数组，按照fileBusinessTypeDetail进行分组，形成一个二维数组groupFileList
    let group = {}
    for (let i = 0; i < fileList2.value.length; i++) {
      let item = fileList2.value[i]
      if (!group[item.fileBusinessTypeDetail]) {
        group[item.fileBusinessTypeDetail] = []
      }
      group[item.fileBusinessTypeDetail].push(item)
    }
    groupFileList2.value = Object.values(group)
  } finally {

  }
};


const RowClassName = ({row, rowIndex,}) => {
  if (one.value.paymentRelationship == '收') {
    return 'red-row'
  } else {
    return 'green-row'
  }
}

const getNoTaxPrice = computed(() => {
  let sum = 0;
  for (let i = 0; i < one.value.ratesAndAmounts.length; i++) {
    const item = one.value.ratesAndAmounts[i]
    sum += parseFloat((item.amount / (1 + item.rate / 100)).toFixed(2))
  }
  return sum.toFixed(2);
})

const getTaxPrice = computed(() => {
  let sum = 0;
  for (let i = 0; i < one.value.ratesAndAmounts.length; i++) {
    const item = one.value.ratesAndAmounts[i]
    sum += parseFloat(((item.amount / (1 + item.rate / 100) * (item.rate / 100)).toFixed(2)))
  }
  return sum.toFixed(2);
})

//处理行点击事件
const handleRowClick = async (row) => {
  selectList.value = row
  // 获取发票清单
  detailQueryParams.invMainId = row.id
  await getDetailList()
  // await getBusinessList(row.id);

  queryParams_feeDetail_kp.ywCode = row.invBillCode
  const date = await FeeDetailApi.getFeeDetailPage(queryParams_feeDetail_kp)
  console.log(date)
  feeDetailList_kp.value = date.list
}


/** 查询发票清单列表 */
const getDetailList = async () => {
  detailLoading.value = true
  try {
    const data = await InvDetailApi.getInvDetailPage(detailQueryParams)
    detailList.value = data.list
    totalDetail.value = data.total
  } finally {
    detailLoading.value = false
  }
}

/** 查询业务信息列表 */
const getBusinessList = async (id) => {

}


// 查询可以开票的数据
const getListAddInv = async () => {
  loadingAddInv.value = true
  try {
    queryParamsAddInV.status = '申请'
    const data = await InvMainApi.getInvMainPage(queryParamsAddInV)
    addInvData.value = data.list
    totalAddInv.value = data.total
    console.log(totalAddInv.value)
    console.log("1111111")
  } finally {
    loadingAddInv.value = false
  }
}

const addInvBut = async () => {
  // 打开新增开票弹窗
  addInvShow.value = true
  // 请求状态为"申请开票"的数据
  const data = await InvMainApi.getInvMainPage(queryParamsAddInV)
  addInvData.value = data.list
  totalAddInv.value = data.total

}

const tableRowClassName = ({row, rowIndex}) => {
  // if (row.redReturn != null && row.redReturn.indexOf("成功") != -1) {
  //   return 'red-row'
  // }
  //

  // 有红票的pdf才会变红redPdf
  if (row.isRed == '是' ) {
    return 'red-row'
  }
  return ''
}

const getListAddInv_recInv = async () => {
  loadingAddInv_recInv.value = true
  try {
    const data = await FeeApi.getFeePage(queryParamsAddInV_recInv)
    addInvData_recInv.value = data.list
    totalAddInv_recInv.value = data.total
  } finally {
  }
}

// 计算recInvAmount
const recInvAmount = computed(() => {
  let sum = 0
  selection_recInv.value.forEach((item) => {
    sum = accAdd(sum, item.amount)
  })
  return sum
})


const addRecInvConfrimBut = async () => {
  // 将选中的数据添加到addRecInv中
  addRecInv.value.fees = selection_recInv.value
  try {
    await RecInvApi.createRecInv_yw(addRecInv.value)
    message.success(t('common.addSuccess'))
    addInvShow_recInv.value = false
    getList_recInv()
  } catch {
  }
}

//驳回
const breakFee = async (id: number) => {
  try {
    await InvMainApi.breakFee(selectedAddInvRow.value.id)
    // const response = await InvMainApi.breakFee({
    //   id: selectedAddInvRow.value.id
    // })
    message.success('驳回成功')
    getListAddInv()
  } catch (e) {
    message.error(`驳回失败`);
  }
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    // const data = await FeeApi.getFeePagepx(queryParams)
    const data = await InvMainApi.getInvMainPage(queryParams)
    console.log(data)
    list.value = data.list
    total.value = data.total
    // const [data1, data2] = await Promise.all([
    //   InvMainApi.getInvMainPage({...queryParams, status: '已开票'}),
    //   InvMainApi.getInvMainPage({...queryParams, status: '已冲红'})
    // ])
    // list.value = [...data1.list,...data2.list]
  } finally {
    loading.value = false
  }
}

const addInvBut_recInv = async () => {
  console.log("1")
  addInvShow_recInv.value = true
  const data = await FeeApi.getFeePage(queryParamsAddInV_recInv)
  console.log("2")
  console.log(data)
  addInvData_recInv.value = data.list
  totalAddInv_recInv.value = data.total
  console.log("3")
  await getGroupFileList2()
}


/** 日期格式化 */
const formatDate = (row: any, column: any, cellValue: string) => {
  if (!cellValue) return ''
  const date = new Date(cellValue)
  const year = date.getFullYear()
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  return `${year}-${month}-${day}`
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

const addInvocieDatailBut = () => {
  if (addInvocieDatailClass.value.goodsname == undefined) {
    message.error('项目名称不能为空')
    return
  }
  // 将addInvocieDatailClass添加到userFInvocieDetailList中
  userFInvocieDetailList.value.push(deepClone(addInvocieDatailClass.value))
  // 清空addInvocieDatailClass
  addInvocieDatailClass.value = {}
  // 补充税率
  addInvocieDatailClass.value.taxrate = selectedAddInvRow.value.taxRate
}

const delUserFInvocieDetailList = (row, index) => {
  userFInvocieDetailList.value.splice(index, 1)
}


//通过js的内置对象JSON来进行数组对象的深拷贝
const deepClone = (obj) => {
  let _obj = JSON.stringify(obj),
    objClone = JSON.parse(_obj)
  return objClone
}

const dologicAddInvocieChangeTaxrate = async (val) => {
  if (val == null || val == undefined || addInvocieDatailClass.value.allAmount == null || addInvocieDatailClass.value.allAmount == undefined) {
    return
  }
  const date = await FeeApi.getSeAndBhsjeByHszjAndSl({
    hszj: addInvocieDatailClass.value.allAmount,
    sl: val
  })
  console.log(date)
  addInvocieDatailClass.value.noTaxAmount = date['bhsje']
  addInvocieDatailClass.value.taxamount = date['se']
}


const dologicAddInvocieChangeHszj = async (val) => {
  if (addInvocieDatailClass.value.taxrate == null || addInvocieDatailClass.value.taxrate == undefined || val == null || val == undefined) {
    return
  }
  const date = await FeeApi.getSeAndBhsjeByHszjAndSl({
    hszj: val,
    sl: addInvocieDatailClass.value.taxrate
  })
  addInvocieDatailClass.value.noTaxAmount = date['bhsje']
  addInvocieDatailClass.value.taxamount = date['se']
}

const dologicAddInvocieChangeNumber = async (val) => {
  // 根据数量，填充单价，结果最多可保留8位，后端计算
  // 金额，数量
  if (addInvocieDatailClass.value.noTaxAmount) {
    const data = await FeeApi.divi({
      bcs: addInvocieDatailClass.value.noTaxAmount,
      cs: val,
      jd: 8
    })
    addInvocieDatailClass.value.price = data
  }
}

const recInvClick = async (row) => {
  console.log(row)
  queryParams_feeDetail_sp.ywCode = row.recInvCode
  const date = await FeeDetailApi.getFeeDetailPage(queryParams_feeDetail_sp)
  console.log(date)
  feeDetailList_sp.value = date.list
}

const getKpStatus = (reTxt) => {
  if (reTxt == null) {
    return "未提交开票！"
  }
  if (reTxt.indexOf("开票提交成功") != -1) {
    return '开票提交成功'
  }
  return '开票提交失败'
}


const dologicAddInvocieChangePrice = async (val) => {
  // 根据单价，填充数量，结果最多可保留8位，后端计算
  // 金额，数量
  if (addInvocieDatailClass.value.noTaxAmount) {
    const data = await FeeApi.divi({
      bcs: addInvocieDatailClass.value.noTaxAmount,
      cs: val,
      jd: 8
    })
    addInvocieDatailClass.value.number = data
  }
}

const upUserFInvocieDetailList = (row, index) => {
  // 上移动
  if (index === 0) {
    return
  }
  const temp = userFInvocieDetailList.value[index]
  userFInvocieDetailList.value[index] = userFInvocieDetailList.value[index - 1]
  userFInvocieDetailList.value[index - 1] = temp
}

const allPriceAllocation = () => {
  // 将所有的剩余分配金额放到含税总金额
  addInvocieDatailClass.value.allAmount = fpsyje.value
}


const downloadRecInv = async (row) => {
  if (!row.invUrl) {
    message.error('没有发票！')
    return
  }
  window.open(row.invUrl)
}

// 预览
const openPdf = async (row) => {
  const data = await InvMainApi.download({
    id: row.id
  })
  let blob = new Blob([data], {type: 'application/pdf;charset=utf-8'});
  const href = URL.createObjectURL(blob);
  window.open(href, 'newWindow');
}

const downloadInv = async (row) => {
  if (!row.pdfUrl) {
    message.error('没有发票！')
    return
  }
  loading.value = true
  const data = await InvMainApi.download({
    id: row.id
  })
  // row.invData 20250114113920 ,取20250114就行了
  // 客户_项目名_发票号_含税金额_不含税金额_税额_开票日期.pdf
  const fileName = row.customerName + "_" + row.projectName + ".pdf"
  download.pdfdown(data, fileName)
  loading.value = false
}

const hongpiaoyulan = async (row) => {
  // 百旺
  if (row.redInfoId == null) {
    message.error('没有红票信息')
    return
  }
  if (!row.redPdf) {
    const data = await FeeApi.getRedInfoQuery({
      fpId: row.id,
      zshj: true
    })
    const data_pdf = await FeeApi.bwDefpQuery({
      fpId: row.id,
      zshj: true
    })
    console.log(data_pdf)
    console.log("pdf")
    console.log(data_pdf.pdfUrl)
    if (data_pdf != null && data_pdf.pdfUrl != null) {
      window.open(data_pdf.pdfUrl)
    } else {
      message.error('有红发票信息号！没查询到红票，请稍后再试，或者请联系管理员！')
      return
    }

    // getRedInfoQuery({ fpId: row.id, zshj: true }).then(res => {
    //   // 有红票号码，再去查询红票的地址
    //   if (res != null && res.code == 200 && res.data.redInvoiceNo != null) {
    //     bwDefpQuery({ fpId: row.id, zshj: true }).then(res_ => {
    //       if (res_ != null && res_.data != null && res_.data.pdfUrl != null) {
    //         window.open(res_.data.pdfUrl)
    //       } else {
    //         this.$message.error('有红发票信息号！没查询到红票，请稍后再试，或者请联系管理员！')
    //         return
    //       }
    //     })
    //   } else {
    //     this.$message.error('红发票信息id,没有红票信息！请稍后再试，或者请联系管理员！')
    //   }
    // })

  } else {
    window.open(row.redPdf)
  }
}

const chonghong = async (row) => {
  try {
    if (!row.invNo) {
      message.error('请先开票')
      return
    }
    await message.confirm('确认冲红？')
    const data = await FeeApi.baiwangchonghong({
      invMainId: row.id,
      zshj: false
    })
    message.success('冲红成功')
    console.log("data:::")
    console.log(data)
    getList()
  } catch (e) {

  }
}

const delInv = async (row) => {
  try {
    await message.confirm('确认取消？')
    await FeeApi.cancel_confirmInv({
      feeSaveReqVO: row
    })
    message.success('取消成功')
    getList()
    // 清空发票清单
    detailList.value = []
  } catch (e) {

  }

}

const money_format = (number, decimals, dec_point, thousands_sep) => {
  /*
    * 参数说明：
    * number：要格式化的数字
    * decimals：保留几位小数
    * dec_point：小数点符号
    * thousands_sep：千分位符号
    * */
  number = (number + '').replace(/[^0-9+-Ee.]/g, '')
  let n = !isFinite(+number) ? 0 : +number,

    prec = !isFinite(+decimals) ? 0 : Math.abs(decimals),
    sep = (typeof thousands_sep === 'undefined') ? ',' : thousands_sep,
    dec = (typeof dec_point === 'undefined') ? '.' : dec_point,
    s = '',
    toFixedFix = function (n, prec) {
      var k = Math.pow(10, prec)
      return '' + Math.floor(n * k) / k
    }
  s = (prec ? toFixedFix(n, prec) : '' + Math.floor(n)).split('.')
  var re = /(-?\d+)(\d{3})/
  while (re.test(s[0])) {
    s[0] = s[0].replace(re, '$1' + sep + '$2')
  }

  if ((s[1] || '').length < prec) {
    s[1] = s[1] || ''
    s[1] += new Array(prec - s[1].length + 1).join('0')
  }
  return s.join(dec)
  // 使用案例
  //   number_format(1234567.089, 2, ".", ",");//1,234,567.08
}


const resetQuery_recInv = () => {
  queryFormRef_recInv.value.resetFields()
  handleQuery_recInv()
}

const handleQuery_recInv = () => {
  queryParams_recInv.pageNo = 1
  getList_recInv()
}

const getList_recInv = async () => {
  loading_recInv.value = true
  try {
    const data = await RecInvApi.getRecInvPage(queryParams_recInv)
    list_recInv.value = data.list
    total_recInv.value = data.total
  } finally {
    loading_recInv.value = false
  }
}

const downUserFInvocieDetailList = (row, index) => {
  // 下移动
  if (index === userFInvocieDetailList.value.length - 1) {
    return
  }
  const temp = userFInvocieDetailList.value[index]
  userFInvocieDetailList.value[index] = userFInvocieDetailList.value[index + 1]
  userFInvocieDetailList.value[index + 1] = temp

}

// 监听userFInvocieDetailList的变化，计算剩余金额fpsyje，剩余金额等于selectedAddInvRow.price 减去（ userFInvocieDetailList所有的hszje之和）
// 监听userFInvocieDetailList的变化
watch(() => userFInvocieDetailList.value, (newVal) => {
  let hszjeSum = 0;
  for (let i = 0; i < newVal.length; i++) {
    console.log("1")
    console.log("accAdd(hszjeSum, newVal[i].allAmount)")
    console.log(accAdd(hszjeSum, newVal[i].allAmount))
    hszjeSum = accAdd(hszjeSum, newVal[i].allAmount);
  }
  fpsyje.value = Subtr(selectedAddInvRow.value.price, hszjeSum);
}, {deep: true}); // 使用deep选项来监听对象内部变化

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}


//减法函数
const Subtr = (arg1, arg2) => {
  var r1, r2, m, n
  try {
    r1 = arg1.toString().split('.')[1].length
  } catch (e) {
    r1 = 0
  }
  try {
    r2 = arg2.toString().split('.')[1].length
  } catch (e) {
    r2 = 0
  }
  m = Math.pow(10, Math.max(r1, r2))
  //last modify by deeka
  //动态控制精度长度
  n = (r1 >= r2) ? r1 : r2
  return ((arg1 * m - arg2 * m) / m).toFixed(n)
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}


const handleSelectionChangeAddInvData_recInv = async (selection: any[]) => {
  selection_recInv.value = selection

}

const handleSelectionChangeAddInvData = async (selection: any[]) => {
  if (selection.length > 1) {
    message.error('只能选择一行')
    return
  }
  if (selection.length === 1) {
    selectedAddInvRow.value = selection[0]
    // 填充数据
    selectedAddInvRow.value.isPerson = '否'
    selectedAddInvRow.value.bankShow = '否'

    // 请求userFInvocieDetailList
    // let data = await InvDetailApi.getInvDetailPage({
    //   pageNo: 1,
    //   pageSize: 100,
    //   feeId: selectedAddInvRow.value.id,
    // })
    // userFInvocieDetailList.value = data.list
    // // 税率一致
    // addInvocieDatailClass.value.taxrate = selectedAddInvRow.value.taxRate

    userFInvocieDetailList.value = []
    // 去请求合同信息,
    let data = await ContractApi.getContract(selectedAddInvRow.value.contractId)
    // 拿合同的税率
    rateOption.value = []
    for (let i = 0; i < data.rate.length; i++) {
      rateOption.value.push({
        value: data.rate[i],
        label: data.rate[i]
      })
    }
    if (rateOption.value.length == 1) {
      addInvocieDatailClass.value.taxrate = rateOption.value[0].value
    }

    // 填充addInvocieDatailClass
    addInvocieDatailClass.value.allAmount = selectedAddInvRow.value.price
    addInvocieDatailClass.value.goodsname = selectedAddInvRow.value.projectName
    addInvocieDatailClass.value.number = 1
    addInvocieDatailClass.value.unit = '项'
    await dologicAddInvocieChangeHszj(selectedAddInvRow.value.price)
    await dologicAddInvocieChangeNumber(1)
  } else {
    selectedAddInvRow.value = null
  }
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await FeeApi.deleteFee(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {
  }
}


interface SummaryMethodProps<T = Product> {
  columns: TableColumnCtx<T>[]
  data: T[]
}

const summaryMethodForInvDetail = (param) => {
  const {columns, data} = param;
  const sums = [];
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = h('div', {style: {textDecoration: 'underline'}}, [
        '合计',
      ]);
      return;
    }

    if (column.property === 'noTaxAmount') {
      const values = data.map((item) => Number(item[column.property]));
      const sum = values.reduce((prev, curr) => {
        const value = Number(curr);
        if (!Number.isNaN(value)) {
          return prev + value;
        } else {
          return prev;
        }
      }, 0);
      sums[index] = h('div', {style: {color: 'red'}}, [
        `${sum.toFixed(2)}`
      ]);
    } else if (column.property === 'taxamount') {
      const values = data.map((item) => Number(item[column.property]));
      const sum = values.reduce((prev, curr) => {
        const value = Number(curr);
        if (!Number.isNaN(value)) {
          return prev + value;
        } else {
          return prev;
        }
      }, 0);
      sums[index] = h('div', {style: {color: 'red'}}, [
        `${sum.toFixed(2)}`
      ]);
    } else {
      sums[index] = '';
    }
  });

  return sums;
}

const summaryMethod = (param) => {
  const {columns, data} = param;
  const sums = [];
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = h('div', {style: {textDecoration: 'underline'}}, [
        '合计',
      ]);
      return;
    }

    if (column.property === 'detailAmount') {
      const values = data.map((item) => Number(item[column.property]));
      const sum = values.reduce((prev, curr) => {
        const value = Number(curr);
        if (!Number.isNaN(value)) {
          return prev + value;
        } else {
          return prev;
        }
      }, 0);
      // 为detailAmount的合计值添加红色文本样式
      sums[index] = h('div', {style: {color: 'red'}}, [
        `${sum.toFixed(2)}` // 保留两位小数
      ]);
    } else {
      sums[index] = ''; // 其他列不显示合计
    }
  });

  return sums;
}


const checkSelectable = (row: any, index: number) => {
  return !selectedAddInvRow.value || selectedAddInvRow.value === row
}

const createInvDataBut = async () => {
  // 检查只勾选了一行
  if (!selectedAddInvRow.value) {
    message.error('请选择一行数据')
    return
  }
  if (userFInvocieDetailList.value.length == 0) {
    message.error('请先添加发票清单')
    return
  }
  for (let i = 0; i < userFInvocieDetailList.value.length; i++) {
    if (!userFInvocieDetailList.value[i].allAmount) {
      message.error('请填写含税总金额')
      return
    }
    if (!userFInvocieDetailList.value[i].goodstaxno) {
      message.error('请填写商品分类')
      return
    }
    if (!userFInvocieDetailList.value[i].goodsname) {
      message.error('请填写项目名称')
      return
    }
    if (!userFInvocieDetailList.value[i].noTaxAmount) {
      message.error('请填写金额')
      return
    }
    if (!userFInvocieDetailList.value[i].taxrate) {
      message.error('请填写税率')
      return
    }
    if (!userFInvocieDetailList.value[i].taxamount) {
      message.error('请填写税额')
      return
    }
    // 同时根据goodstaxno，invGoodsType,填充goodsTaxType
    for (let j = 0; j < invGoodsType.length; j++) {
      if (userFInvocieDetailList.value[i].goodstaxno === invGoodsType[j].value) {
        userFInvocieDetailList.value[i].goodsTaxType = invGoodsType[j].label
        break
      }
    }
  }
  loadingAdd.value = true
  try {
    await FeeApi.confirmInv({
        invMainSaveReqVO: selectedAddInvRow.value,
        invDetailSaveReqVOS: userFInvocieDetailList.value
      }
    )
    message.success('操作成功')
    addInvShow.value = false
    selectedAddInvRow.value.invType = '专票'
    getList()
    getListAddInv()
  } catch (e) {
    message.error(e)
  } finally {
    loadingAdd.value = false
  }
}

const handleDeleteInv = async (invMainId: number) => {
  try {
    await message.delConfirm()
    await FeeApi.deleteInvData(invMainId)
    ElMessage.success('删除成功')
    getList()
    getListAddInv()
  } catch (e) {
    console.error('删除开票记录失败:', e)
  }
}

//已在税局开票
const createInvData = async () => {
  // 检查只勾选了一行
  if (!selectedAddInvRow.value) {
    message.error('请选择一行数据')
    return
  }
  if (userFInvocieDetailList.value.length == 0) {
    message.error('请先添加发票清单')
    return
  }
  for (let i = 0; i < userFInvocieDetailList.value.length; i++) {
    if (!userFInvocieDetailList.value[i].allAmount) {
      message.error('请填写含税总金额')
      return
    }
    if (!userFInvocieDetailList.value[i].goodstaxno) {
      message.error('请填写商品分类')
      return
    }
    if (!userFInvocieDetailList.value[i].goodsname) {
      message.error('请填写项目名称')
      return
    }
    if (!userFInvocieDetailList.value[i].noTaxAmount) {
      message.error('请填写金额')
      return
    }
    if (!userFInvocieDetailList.value[i].taxrate) {
      message.error('请填写税率')
      return
    }
    if (!userFInvocieDetailList.value[i].taxamount) {
      message.error('请填写税额')
      return
    }
    // 同时根据goodstaxno，invGoodsType,填充goodsTaxType
    for (let j = 0; j < invGoodsType.length; j++) {
      if (userFInvocieDetailList.value[i].goodstaxno === invGoodsType[j].value) {
        userFInvocieDetailList.value[i].goodsTaxType = invGoodsType[j].label
        break
      }
    }
  }
  loadingAdd.value = true
  try {
    await FeeApi.createInvData({
        invMainSaveReqVO: selectedAddInvRow.value,
        invDetailSaveReqVOS: userFInvocieDetailList.value
      }
    )
    message.success('操作成功')
    addInvShow.value = false
    selectedAddInvRow.value.invType = '专票'
    getList()
    getListAddInv()
  } catch (e) {
    message.error(e)
  } finally {
    loadingAdd.value = false
  }
}

// 金额显示规范
const formatCurrency = (amount: number | undefined): string => {
  if (amount === undefined) {
    return '¥ 0.00'
  }
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount)
}

const handleCancel = () => {
  queryParamsAddInV_recInv.projectName = undefined
  addRecInv.value.invUrl = undefined;
  addInvShow_recInv.value = false;
}

const deleteRecInv_yw = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await RecInvApi.deleteRecInv_yw(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList_recInv()
  } catch {
  }
}


/** 初始化 **/
onMounted(() => {
  getList()
  getListAddInv()
  getList_recInv()
})
</script>


<style lang="scss">

::v-deep .el-table__header-wrapper .el-checkbox {
  visibility: hidden;
}

.invoice-form {
  .invoice-form-container {
    padding: 20px;
    background-color: #fff;
    border-radius: 4px;
  }

  .form-section {
    margin-bottom: 24px;
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 4px;

    &:last-child {
      margin-bottom: 0;
    }

    .section-title {
      margin-bottom: 16px;
      padding-left: 8px;
      font-size: 16px;
      font-weight: 500;
      color: #1f2f3d;
      border-left: 4px solid #409EFF;
    }
  }

  :deep(.el-form-item) {
    margin-bottom: 18px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.el-table__footer-wrapper .cell {
  color: red;
}

.el-table .red-row {
  background: orangered;
}

.vertical-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.contract-drawer {
  :deep(.el-drawer__header) {
    margin-bottom: 0;
    padding: 16px 20px;
    border-bottom: 1px solid #e4e7ed;
    font-size: 16px;
    font-weight: 500;
  }

  :deep(.el-drawer__body) {
    padding: 0;
  }
}

.drawer-content {
  padding: 20px;
}

.section-title {
  font-size: 15px;
  font-weight: 500;
  color: #303133;
  margin: 20px 0 15px;
  padding-left: 10px;
  border-left: 3px solid #409eff;
}

.rate-item {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 15px;

  :deep(.el-form-item) {
    margin-bottom: 0;
  }

  :deep(.el-input) {
    width: 100%;
  }
}

.total-amount-row {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  margin: 15px 0;

  :deep(.el-form-item) {
    margin-bottom: 0;
  }
}

.amount-value {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  text-align: center;
}

.file-group {
  margin-bottom: 20px;
}

.file-group-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 10px;
  padding-left: 10px;
}
</style>
