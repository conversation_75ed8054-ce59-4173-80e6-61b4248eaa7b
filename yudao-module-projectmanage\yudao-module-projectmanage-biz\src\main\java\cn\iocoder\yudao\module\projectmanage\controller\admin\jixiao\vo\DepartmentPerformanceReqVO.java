package cn.iocoder.yudao.module.projectmanage.controller.admin.jixiao.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 部门经理绩效表查询 Request VO")
@Data
public class DepartmentPerformanceReqVO {

    @Schema(description = "月期", example = "2025-07")
    private String monthPeriod;

    @Schema(description = "主办部门", example = "软件开发一部")
    private String managingDepartment;
    
    @Schema(description = "状态", example = "pending", allowableValues = {"pending", "rejected"})
    private String status;
} 