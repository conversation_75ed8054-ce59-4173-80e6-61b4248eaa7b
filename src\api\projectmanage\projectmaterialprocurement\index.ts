import request from '@/config/axios'

// 物资采购审批 VO
export interface ProjectMaterialProcurementVO {
  id: number // id
  projectId: number // 项目id
  fdTemplataId: string // 模版id
  flowId: string // 流程id
  docCreator: string // 发起人
  projectName: string // 项目名称
  detail: string // 具体内容
  estimateMoney: number // 预算金额
  priceComparison: string // 比价邀标情况
  isAssets: string // 是否列入固定资产
}

// 物资采购审批 API
export const ProjectMaterialProcurementApi = {
  // 查询物资采购审批分页
  getProjectMaterialProcurementPage: async (params: any) => {
    return await request.get({ url: `/projectmanage/project-material-procurement/page`, params })
  },

  // 查询物资采购审批详情
  getProjectMaterialProcurement: async (id: number) => {
    return await request.get({ url: `/projectmanage/project-material-procurement/get?id=` + id })
  },

  // 新增物资采购审批
  createProjectMaterialProcurement: async (data: ProjectMaterialProcurementVO) => {
    return await request.post({ url: `/projectmanage/project-material-procurement/create`, data })
  },

  // 修改物资采购审批
  updateProjectMaterialProcurement: async (data: ProjectMaterialProcurementVO) => {
    return await request.put({ url: `/projectmanage/project-material-procurement/update`, data })
  },

  // 删除物资采购审批
  deleteProjectMaterialProcurement: async (id: number) => {
    return await request.delete({ url: `/projectmanage/project-material-procurement/delete?id=` + id })
  },

  // 导出物资采购审批 Excel
  exportProjectMaterialProcurement: async (params) => {
    return await request.download({ url: `/projectmanage/project-material-procurement/export-excel`, params })
  },

  getData: async (projectId: number) => {
    return await request.get({ url: `/projectmanage/project-material-procurement/list?projectId=` + projectId })
  },

  pushProjectMaterialProcurementApproval: async (data: any) => {
    return await request.post({ url: `/projectmanage/project-material-procurement/push`, data })
  },
}
