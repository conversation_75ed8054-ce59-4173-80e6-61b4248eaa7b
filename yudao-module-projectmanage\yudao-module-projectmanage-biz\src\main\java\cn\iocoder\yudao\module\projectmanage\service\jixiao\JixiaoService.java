package cn.iocoder.yudao.module.projectmanage.service.jixiao;

import java.util.*;
import jakarta.validation.*;
import cn.iocoder.yudao.module.projectmanage.controller.admin.jixiao.vo.*;
import cn.iocoder.yudao.module.projectmanage.dal.dataobject.jixiao.JixiaoDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 绩效考核 Service 接口
 *
 * <AUTHOR>
 */
public interface JixiaoService {

    /**
     * 创建绩效考核
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createJixiao(@Valid JixiaoSaveReqVO createReqVO);

    /**
     * 更新绩效考核
     *
     * @param updateReqVO 更新信息
     */
    void updateJixiao(@Valid JixiaoSaveReqVO updateReqVO);

    /**
     * 删除绩效考核
     *
     * @param id 编号
     */
    void deleteJixiao(Long id);

    /**
     * 获得绩效考核
     *
     * @param id 编号
     * @return 绩效考核
     */
    JixiaoDO getJixiao(Long id);

    /**
     * 获得绩效考核分页
     *
     * @param pageReqVO 分页查询
     * @return 绩效考核分页
     */
    PageResult<JixiaoDO> getJixiaoPage(JixiaoPageReqVO pageReqVO);

    /**
     * 获取部门经理绩效表数据
     *
     * @param reqVO 查询参数
     * @return 部门经理绩效表数据列表
     */
    List<DepartmentPerformanceRespVO> getDepartmentPerformanceData(DepartmentPerformanceReqVO reqVO);

    /**
     * 确认部门绩效占比
     *
     * @param reqVO 确认数据
     */
    void confirmDepartmentPerformance(ConfirmDepartmentPerformanceReqVO reqVO);

    /**
     * 驳回部门绩效占比
     *
     * @param reqVO 驳回数据
     */
    void rejectDepartmentPerformance(RejectDepartmentPerformanceReqVO reqVO);

    /**
     * 撤销驳回部门绩效占比
     *
     * @param reqVO 撤销驳回数据
     */
    void revokeRejectDepartmentPerformance(RevokeRejectDepartmentPerformanceReqVO reqVO);

}