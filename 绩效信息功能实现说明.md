# 绩效信息功能实现说明

## 功能概述
在mainprojectgrop\index.vue页面添加了一列"绩效信息"，该列通过项目组ID请求projectmanage_project_person表获取time(格式为2025-07)和manager_confirm字段，并集成在ProjectGropController的page方法中。

## 修改内容

### 1. 后端修改

#### 1.1 ProjectGropRespVO.java
- **文件路径**: `yudao-module-projectmanage\yudao-module-projectmanage-biz\src\main\java\cn\iocoder\yudao\module\projectmanage\controller\admin\projectgrop\vo\ProjectGropRespVO.java`
- **修改内容**: 添加了绩效信息字段
```java
@Schema(description = "绩效信息")
private String performanceInfo;
```

#### 1.2 ProjectGropController.java
- **文件路径**: `yudao-module-projectmanage\yudao-module-projectmanage-biz\src\main\java\cn\iocoder\yudao\module\projectmanage\controller\admin\projectgrop\ProjectGropController.java`
- **修改内容**: 
  1. 在page方法中添加了绩效信息获取逻辑
  2. 新增了`getPerformanceInfoForProjectGroups`私有方法用于获取绩效信息

**主要逻辑**:
- 在现有的统计数据查询后，添加绩效信息查询
- 为每个项目组设置绩效信息
- 绩效信息格式：`绩效统计: 2025-07(已确认): 3人; 2025-08(未确认): 2人`

**绩效信息获取方法**:
```java
private Map<Long, String> getPerformanceInfoForProjectGroups(List<Long> projectGroupIds) {
    // 根据项目组ID列表获取每个项目组的绩效信息
    // 统计不同时间和确认状态的人员数量
    // 返回项目组ID到绩效信息字符串的映射
}
```

### 2. 前端修改

#### 2.1 ProjectGropVO接口定义
- **文件路径**: `src\api\projectmanage\projectgrop\index.ts`
- **修改内容**: 在ProjectGropVO接口中添加绩效信息字段
```typescript
performanceInfo: string // 绩效信息
```

#### 2.2 主项目组列表页面
- **文件路径**: `src\views\projectmanage\mainprojectgrop\index.vue`
- **修改内容**: 在表格中添加绩效信息列
```vue
<el-table-column label="绩效信息" align="center" width="200px" :show-overflow-tooltip="true">
  <template #default="scope">
    <span>{{ scope.row.performanceInfo || '暂无绩效信息' }}</span>
  </template>
</el-table-column>
```

## 数据流程

1. **前端请求**: 前端调用项目组分页接口
2. **后端处理**: 
   - ProjectGropController.page方法接收请求
   - 获取项目组基本数据和统计信息
   - 调用`getPerformanceInfoForProjectGroups`方法获取绩效信息
   - 为每个项目组设置绩效信息
3. **数据返回**: 返回包含绩效信息的项目组列表
4. **前端展示**: 在表格的绩效信息列中显示数据

## 绩效信息格式说明

绩效信息按以下格式展示：
- **有数据时**: `绩效统计: 2025-07(已确认): 3人; 2025-08(未确认): 2人`
- **无项目成员时**: `暂无项目成员`
- **无绩效记录时**: `暂无绩效记录`
- **查询失败时**: `绩效信息查询失败`
- **前端默认**: `暂无绩效信息`

## 技术要点

1. **数据来源**: projectmanage_project_person表的time和manager_confirm字段
2. **查询优化**: 批量查询所有项目组的绩效信息，避免N+1查询问题
3. **错误处理**: 对每个项目组的绩效信息查询都有异常处理
4. **数据统计**: 使用Java Stream API进行数据分组和统计
5. **前端展示**: 使用tooltip显示完整信息，避免列宽过大

## 注意事项

1. 绩效信息查询依赖ProjectPersonService，确保该服务正常工作
2. time字段格式应为"YYYY-MM"格式（如2025-07）
3. manager_confirm字段用于标识经理确认状态
4. 前端表格列设置了固定宽度200px和tooltip显示，避免影响整体布局
