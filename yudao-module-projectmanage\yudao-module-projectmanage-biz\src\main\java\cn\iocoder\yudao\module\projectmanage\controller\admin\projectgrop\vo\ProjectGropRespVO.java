package cn.iocoder.yudao.module.projectmanage.controller.admin.projectgrop.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 项目组 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ProjectGropRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "26098")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "项目组编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("项目组编号")
    private String projectGropNumber;

    @Schema(description = "项目组名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @ExcelProperty("项目组名称")
    private String projectGropName;

    @Schema(description = "收款金额")
    @ExcelProperty("收款金额")
    private BigDecimal receivable;

    @Schema(description = "已收款金额")
    @ExcelProperty("已收款金额")
    private BigDecimal receivedAmount;

    @Schema(description = "付款金额")
    @ExcelProperty("付款金额")
    private BigDecimal payable;

    @Schema(description = "已付款金额")
    @ExcelProperty("已付款金额")
    private BigDecimal paidAmount;

    @Schema(description = "已开票金额")
    @ExcelProperty("已开票金额")
    private BigDecimal invAmount;

    @Schema(description = "已收票金额")
    @ExcelProperty("已收票金额")
    private BigDecimal invPayAmount;

    @Schema(description = "利润")
    @ExcelProperty("利润")
    private BigDecimal profit;

    @Schema(description = "备注")
    @ExcelProperty("备注")
    private String remarks;

    @Schema(description = "项目简介")
    @ExcelProperty("项目简介")
    private String projectIntroduction;

    @Schema(description = "业主")
    @ExcelProperty("业主")
    private List<String> ownerName;

    @Schema(description = "总包")
    @ExcelProperty("总包")
    private List<String> generalContractor;

    @Schema(description = "二包")
    @ExcelProperty("二包")
    private List<String> subcontractorSecond;

    @Schema(description = "三包")
    @ExcelProperty("三包")
    private List<String> subcontractorThird;

    @Schema(description = "四包")
    @ExcelProperty("四包")
    private List<String> subcontractorFourth;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "项目经理")
    @ExcelProperty("项目经理")
    private String projectManager;


    @Schema(description = "项目组验收")
    @ExcelProperty("项目组进度")
    private String statusText;

    @Schema(description = "进度")
    @ExcelProperty("进度")
    private String percentage;

    @Schema(description = "项目组类型")
    @ExcelProperty("项目组类型")
    private List<String> type;


    @Schema(description = "部门id")
    @ExcelProperty("部门id")
    private Integer deptId;

    @Schema(description = "最早合同年份")
    private String earliestContractYear;

    @Schema(description = "项目工期（天）")
    private Integer workDay;

    @Schema(description = "项目组成员数量")
    private Integer personCount;

    @Schema(description = "项目组进度")
    private String projectGroupProgress;

    @Schema(description = "是否推送审批")
    private String approved;

    @Schema(description = "主办部门")
    private String department;

    @Schema(description = "金额超五万项目数")
    private Integer overFiveWanCount;

    @Schema(description = "收款项目验收信息")
    private String reciAccepted;

    @Schema(description = "付款项目验收信息")
    private String payAccepted;

    @Schema(description = "经办人信息")
    private String handler;

    @Schema(description = "金额规模")
    private Integer amountScale;

    @Schema(description = "金额规模范围")
    private String amountScaleRange;

    @Schema(description = "性质")
    @ExcelProperty("性质")
    private String nature;

    @Schema(description = "重要性")
    @ExcelProperty("重要性")
    private String importance;

    @Schema(description = "项目系数")
    @ExcelProperty("项目系数")
    private BigDecimal coefficient;

    @Schema(description = "创建者ID")
    private String creator;

    @Schema(description = "创建者昵称")
    @ExcelProperty("创建者昵称")
    private String creatorNickname;

    private String flowId;

    @Schema(description = "绩效信息")
    private String performanceInfo;
}