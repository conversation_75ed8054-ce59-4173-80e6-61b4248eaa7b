package cn.iocoder.yudao.module.projectmanage.service.projectgrop;

import java.math.BigDecimal;
import java.util.*;

import cn.iocoder.yudao.module.projectmanage.controller.admin.fee.vo.FeeSaveReqVO;
import cn.iocoder.yudao.module.projectmanage.controller.admin.project.vo.ProjectSaveReqVO;
import cn.iocoder.yudao.module.projectmanage.controller.admin.project.vo.ProjectWithFeesRespVO;
import jakarta.validation.*;
import cn.iocoder.yudao.module.projectmanage.controller.admin.projectgrop.vo.*;
import cn.iocoder.yudao.module.projectmanage.dal.dataobject.projectgrop.ProjectGropDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

/**
 * 项目组 Service 接口
 *
 * <AUTHOR>
 */
public interface ProjectGropService {

    /**
     * 创建项目组
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createProjectGrop(@Valid ProjectGropSaveReqVO createReqVO);

    /**
     * 更新项目组
     *
     * @param updateReqVO 更新信息
     */
    void updateProjectGrop(@Valid ProjectGropSaveReqVO updateReqVO);

    /**
     * 删除项目组
     *
     * @param id 编号
     */
    void deleteProjectGrop(Long id);

    /**
     * 获得项目组
     *
     * @param id 编号
     * @return 项目组
     */
    ProjectGropDO getProjectGrop(Long id);

    /**
     * 获得项目组分页
     *
     * @param pageReqVO 分页查询
     * @return 项目组分页
     */
    PageResult<ProjectGropDO> getProjectGropPage(ProjectGropPageReqVO pageReqVO);

    PageResult<ProjectGropDO> getProjectGropPageAndManagerIsNull(ProjectGropPageReqVO pageReqVO);

    List<ProjectSaveReqVO> getProjectsByGroupCode(String projectGroupCode);

    List<FeeSaveReqVO> getFeesByGroupCode(String projectGroupCode);

    List<FeeSaveReqVO> getFeesByProjectCode(String projectCode);

    List<ProjectWithFeesRespVO> getReceivableProjects(String projectGropNumber);

    ProjectGropDO getProjectGropByNumber(String projectGropNumber);

    List<ProjectWithFeesRespVO> getPayableProjects(String projectGropNumber);

    List<FeeSaveReqVO> getFeeByContractId(String contractId);

    /**
     * 获取项目组的合同金额统计
     *
     * @param projectGropNumber 项目组编号
     * @return 收付款金额统计
     */
    Map<String, BigDecimal> getContractAmounts(String projectGropNumber);

    String generateProjectGropNumber();
    String generateAppointProjectGropNumber(Long year);
    /**
     * 获取项目组的收付款总额
     *
     * @param projectGropNumber 项目组编号
     * @return 包含收款总额和付款总额的Map
     */
    Map<String, BigDecimal> getPaymentAmounts(String projectGropNumber);

    /**
     * 获取项目组下的项目和系统列表
     *
     * @param projectGropId 项目组ID
     * @return 项目和系统列表
     */
    List<Map<String, Object>> getProjectSystemList(Long projectGropId);

    /**
     * 按部门查询项目组
     *
     * @param department 部门名称
     * @param pageReqVO 分页参数
     * @return 项目组分页结果
     */
    PageResult<ProjectGropDO> getProjectGropByDepartment(String department, ProjectGropPageReqVO pageReqVO);

    /**
     * 按照年份查询项目组记录
     *
     * @param year 查询年份
     * @return 项目组查询结果
     */
    PageResult<ProjectGropDO> selectProjectListByYear(String year, ProjectGropPageReqVO pageReqVO);

    /**
     * 自动计算项目系数
     *
     * @param deptId 部门ID
     * @param nature 项目组性质
     * @param importance 项目组重要性
     * @param receivable 收款合同总金额
     * @param type 项目组类型
     * @return 项目系数
     */
    BigDecimal calculateProjectCoefficient(Integer deptId, String nature, String importance, BigDecimal receivable, List<String> type);

    /**
     * 检查用户是否为部门经理
     *
     * @param userId 用户ID
     * @return 是否为部门经理（软件一部、二部、系统集成部经理或超级管理员）
     */
    boolean checkIsDepartmentManager(Long userId);
}
