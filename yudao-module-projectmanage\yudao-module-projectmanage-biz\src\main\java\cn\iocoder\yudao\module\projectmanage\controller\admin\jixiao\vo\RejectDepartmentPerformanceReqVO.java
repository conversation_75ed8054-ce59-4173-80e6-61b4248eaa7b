package cn.iocoder.yudao.module.projectmanage.controller.admin.jixiao.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;

import java.util.List;

@Schema(description = "管理后台 - 驳回部门绩效占比 Request VO")
@Data
public class RejectDepartmentPerformanceReqVO {

    @Schema(description = "月期", requiredMode = Schema.RequiredMode.REQUIRED, example = "2025-01")
    @NotBlank(message = "月期不能为空")
    private String monthPeriod;

    @Schema(description = "主办部门", requiredMode = Schema.RequiredMode.REQUIRED, example = "软件开发一部")
    @NotBlank(message = "主办部门不能为空")
    private String managingDepartment;

    @Schema(description = "驳回原因", requiredMode = Schema.RequiredMode.REQUIRED, example = "绩效占比不合理，需要重新分配")
    @NotBlank(message = "驳回原因不能为空")
    private String rejectReason;

    @Schema(description = "被驳回的项目绩效数据列表", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "被驳回的数据不能为空")
    private List<DepartmentPerformanceRespVO> data;
}