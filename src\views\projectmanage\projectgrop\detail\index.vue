<template>
  <ContentWrap>
    <!-- 项目组基本信息 -->
    <el-card class="mb-4">
      <template #header>
        <div class="card-header"
             style="display: flex; justify-content: space-between; align-items: center;">
          <div style="display: flex; align-items: center; gap: 12px;">
            <span>项目组信息</span>
            <el-button
              type="primary"
              @click="goBack"
            >
              返回项目组台账
            </el-button>
          </div>
<!--          <div style="display: flex; gap: 20px; align-items: center;">-->
<!--            <div style="display: flex; align-items: center; gap: 8px;">-->
<!--              <span style="font-weight: normal; color: #666;">性质:</span>-->
<!--              <dict-tag :type="'xmzxz'" :value="detail.nature" v-if="detail.nature"/>-->
<!--              <span v-else style="color: #999;">-</span>-->
<!--            </div>-->
<!--            <div style="display: flex; align-items: center; gap: 8px;">-->
<!--              <span style="font-weight: normal; color: #666;">重要性:</span>-->
<!--              <dict-tag :type="'xmzzyx'" :value="detail.importance" v-if="detail.importance"/>-->
<!--              <span v-else style="color: #999;">-</span>-->
<!--            </div>-->
<!--            <div style="display: flex; align-items: center; gap: 8px;">-->
<!--              <span style="font-weight: normal; color: #666;">项目系数:</span>-->
<!--              <span style="font-weight: 500;">{{ detail.coefficient || '-' }}</span>-->
<!--            </div>-->
<!--          </div>-->
        </div>
      </template>
      <el-descriptions
        :column="2"
        border
        v-loading="loading"
        class="project-info-descriptions"
        :cell-style="{ width: '50%' }"
        :label-style="{
          width: '180px',
          textAlign: 'left',
          paddingLeft: '12px',
          fontWeight: 'bold',
          backgroundColor: '#fafafa'
        }"
        :content-style="{
          paddingLeft: '12px'
        }"
      >

        <el-descriptions-item label="项目组名称">{{ detail.projectGropName }}</el-descriptions-item>
        <el-descriptions-item label="项目组编号">{{
            detail.projectGropNumber
          }}
        </el-descriptions-item>
        <el-descriptions-item label="收款合同总金额（元）">
          <span style="color: #F56C6C">{{ contractAmounts.receivableAmount }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="付款合同总金额（元）">
          <span style="color: #67C23A">{{ contractAmounts.payableAmount }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="已开票总金额">
          <span style="color: #F56C6C">{{ totalReceivedAmount_inv }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="已收票总金额">
          <span style="color: #67C23A">{{ totalPaidAmount_inv_pay }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="已开票占比">
          <span style="color: #F56C6C">
            {{ isNaN(invReceivedPercentage) ? '0' : invReceivedPercentage }}%
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="已收票占比">
          <span style="color: #67C23A">
            {{ isNaN(invPaidPercentage) ? '0' : invPaidPercentage }}%
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="已转收入凭证金额">
          <span style="color: #F56C6C">{{ totalReceivedAmount_toincome }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="已付款凭证总金额">
          <span style="color: #67C23A">{{ totalPayedAmount_toincome_pay }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="已收款总金额（元）">
          <span style="color: #F56C6C">{{ totalReceivedAmount }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="已付款总金额（元）">
          <span style="color: #67C23A">{{ totalPaidAmount }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="已收款占比">
  <span style="color: #F56C6C">
    {{ isNaN(receivedPercentage) ? '0' : receivedPercentage }}%
  </span>
        </el-descriptions-item>
        <el-descriptions-item label="已付款占比">
          <span style="color: #67C23A">
            {{ isNaN(paidPercentage) ? '0' : paidPercentage }}%
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="剩余收款金额（元）">
          <span style="color: #F56C6C">{{ remainingReceivable }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="剩余付款金额（元）">
          <span style="color: #67C23A">{{ remainingPayable }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="毛利（元）">
          <span
            style="
          color: #409EFF">{{ contractDifference }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="毛利率">
          <span style="color: #409EFF">{{ contractDifferenceRate }}%</span>
        </el-descriptions-item>
        <el-descriptions-item label="项目经理" prop="projectManager">
  <span :style="{ color: projectManagerStatus === '审批通过' ? '#67C23A' : '' }">
    {{ detail.projectManager }}
  {{
      projectManagerStatus === '审批通过'
        ? '(已选任)'
        : projectManagerStatus
          ? `(${projectManagerStatus})`
          : ''
    }}
  </span>

        </el-descriptions-item>
        <el-descriptions-item label="项目简介" :span="2">{{
            (detail.amountScaleRange ? "(" + detail.amountScaleRange  + ") " : "") + (detail.projectIntroduction ?? "")
          }}
        </el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 业务部附件 -->
    <el-row v-for="group in groupFileList" :key="group[0].fileBusinessTypeDetail">
      <el-col :span="24">
        <div>
          <span>{{ group[0].fileBusinessTypeDetail }}</span>
        </div>
        <UploadFile
          :modelValue="collectedUrls(group)"
          :businessId="detail.id"
          :fileBusinessType="`业务部附件`"
          :fileBusinessTypeDetail="group[0].fileBusinessTypeDetail"
          :url2Name="collectUrl2NameMap(group)"
          :businessFileList="group"
          @update:model-value="handleFileChange"
        />
      </el-col>
    </el-row>

    <!-- 收款项目 -->
    <el-card class="mb-4">
      <template #header>
        <div>
          <div>
            <span>收款项目</span>
          </div>
          <div style="margin-top: 10px;">
            <el-button
              type="primary"
              plain
              @click="openForm('create', 'receivable')"
              v-hasPermi="['projectmanage:project:create']"
            >
              <Icon icon="ep:plus" class="mr-5px"/>
              新增收款项目
            </el-button>
          </div>
        </div>
      </template>
      <el-table
        :data="receivableProjects" v-loading="loading" @row-dblclick="navigateToProject"
        @selection-change="handleReceivableSelectionChange"
        @row-click="showRelatedPayProjects">
        <el-table-column type="selection" width="55"/>
        <el-table-column type="expand" fixed="left">
          <template #default="props">
            <el-table :data="props.row.fees" border>
              <el-table-column label="金额（元）" prop="amount"/>
              <el-table-column label="款项类型" prop="type"/>
              <el-table-column label="发票号" prop="invNos"/>
              <el-table-column label="税率" align="center">
                <template #default="scope">
                  <span>{{ scope.row.taxRate }}%</span>
                </template>
              </el-table-column>
              <el-table-column label="已收款金额(元)" align="center">
                <template #default="scope">
                  <span class="green-text">{{ scope.row.alreadyAmount || '0.00' }}</span>
                </template>
              </el-table-column>
            </el-table>
          </template>
        </el-table-column>

        <el-table-column label="项目编号" align="center" width="110" prop="projectCode">
          <template #default="scope">
            <span>{{ scope.row.projectCode }}</span>
          </template>
        </el-table-column>
        <el-table-column label="项目名称" align="center" prop="projectName" width="400">
          <template #default="scope">
    <span v-if="scope.row.attribute === '零星'">
      <span style="color: #F56C6C">(零星)</span> {{ scope.row.projectName }}
    </span>
            <span v-else>
      {{ scope.row.projectName }}
    </span>
          </template>
        </el-table-column>
        <el-table-column label="项目金额（元）" align="center" prop="totalAmount" width="125"/>
        <el-table-column label="合作单位" align="center" prop="payerName" width="280"/>
        <el-table-column label="验收情况" align="center" prop="ifAccepted" width="120"/>
        <el-table-column label="来源" align="center" prop="sourceMethods" width="120"/>
        <el-table-column label="项目类别" align="center" prop="type" width="100"/>
        <el-table-column label="项目负责人" align="center" prop="projectManagerName" width="100"/>
        <el-table-column fixed="right" width="150" label="操作" align="center">
          <template #default="scope">
            <router-link :to="'/project/' + scope.row.id">
              <el-button link type="primary">详情</el-button>
            </router-link>


            <el-button
              link
              type="primary"
              @click="handleContractClick(scope.row)"
            >
              合同
            </el-button>

            <el-button
              link
              type="primary"
              @click="handleoarouter(scope.row)"
            >
              跳转oa
            </el-button>

            <el-button
              link
              type="danger"
              @click="handleDeleteProject(scope.row.id)"
              v-hasPermi="['projectmanage:project:delete']"
            >
              删除
            </el-button>
          </template>
        </el-table-column>

      </el-table>
    </el-card>

    <!-- 付款项目 -->
    <el-card class="mb-4">
      <template #header>
        <div>
          <div>
            <span>付款项目</span>
          </div>
          <div style="margin-top: 10px; display: flex; align-items: center;">
            <el-tag v-if="isFiltered" type="info" style="margin-right: 10px;">
              已筛选关联付款项目
              <el-button type="primary" link @click="resetPayableProjects">
                重置
              </el-button>
            </el-tag>
            <el-tooltip
              content="添加付款项目前，请先在收款项目中勾选要关联的项目"
              placement="top"
              v-if="selectedRecProjects.length === 0"
            >
              <el-button
                type="primary"
                plain
                @click="openForm('create', 'payable')"
                v-hasPermi="['projectmanage:project:create']"
                :class="{'warning-button': true}"
              >
                <Icon icon="ep:plus" class="mr-5px"/>
                新增付款项目
              </el-button>
            </el-tooltip>
            <el-button
              v-else
              type="primary"
              plain
              @click="openForm('create', 'payable')"
              v-hasPermi="['projectmanage:project:create']"
            >
              <Icon icon="ep:plus" class="mr-5px"/>
              新增付款项目
            </el-button>
          </div>
        </div>
      </template>
      <el-table
        :data="filteredPayableProjects" v-loading="loading"
        @row-dblclick="navigateToProject" @row-click="handleRowClick">
        <el-table-column type="expand" fixed="left">
          <template #default="props">
            <el-table :data="props.row.fees" border>
              <el-table-column label="金额" align="center" prop="amount"/>
              <el-table-column label="款项类型" align="center" prop="type"/>
              <el-table-column label="发票号" align="center" prop="invNo"/>
              <el-table-column label="税率" align="center">
                <template #default="scope">
                  <span>{{ scope.row.taxRate }}%</span>
                </template>
              </el-table-column>
              <el-table-column label="已付款金额(元)" align="center">
                <template #default="scope">
                  <span class="red-text">{{ scope.row.alreadyAmount || '0.00' }}</span>
                </template>
              </el-table-column>
            </el-table>
          </template>
        </el-table-column>

        <el-table-column width="55"/>

        <el-table-column fixed="right" width="140" label="操作" align="center">
          <template #default="scope">
            <router-link :to="'/project/' + scope.row.id">
              <el-button link type="primary">详情</el-button>
            </router-link>


            <el-button
              link
              type="primary"
              @click="handleContractClick(scope.row)"
            >
              合同
            </el-button>


            <el-button
              link
              type="primary"
              @click="handleoarouter(scope.row)"
            >
              跳转oa
            </el-button>

            <el-button
              link
              type="danger"
              @click="handleDeleteProject(scope.row.id)"
              v-hasPermi="['projectmanage:project:delete']"
            >
              删除
            </el-button>
          </template>
        </el-table-column>

        <el-table-column label="项目编号" align="center" width="100" prop="projectCode"/>
        <el-table-column label="项目名称" align="center" prop="projectName" width="320">
          <template #default="scope">
    <span v-if="scope.row.attribute === '零星'">
      <span style="color: #F56C6C">(零星)</span> {{ scope.row.projectName }}
    </span>
            <span v-else>
      {{ scope.row.projectName }}
    </span>
          </template>
        </el-table-column>
        <el-table-column label="项目金额（元）" align="center" prop="totalAmount" width="125"/>
        <el-table-column label="合作单位" align="center" prop="payeeName" width="250"/>
        <el-table-column label="验收情况" align="center" prop="ifAccepted" width="110"/>
        <el-table-column label="采购方式" align="center" prop="buyMethods" width="120"/>
        <el-table-column label="项目类别" align="center" prop="type" width="100"/>
        <el-table-column label="项目负责人" align="center" prop="projectManagerName" width="100"/>
        <el-table-column label="审批状态" align="center" width="130" prop="approvalStatus">
          <template #default="scope">
<!--            <el-button-->
<!--              link-->
<!--              type="primary"-->
<!--              @click="oarouter(scope.row.flowId)"-->
<!--            >-->
<!--              {{ scope.row.approvalStatus }}-->
<!--            </el-button>-->
            <el-button
              link
              type="primary"
              @click="oarouter(scope.row.flowId || scope.row.materialFlowId)"
            >
      <span v-if="scope.row.fdId === '********************************'">
        <span v-if="scope.row.materialApprovalStatus">{{ `（物资）${scope.row.materialApprovalStatus}` }}</span>
        <span v-else>-</span>
      </span>
              <span v-else>
        <span v-if="scope.row.approvalStatus">{{ `（立项）${scope.row.approvalStatus}` }}</span>
        <span v-else>-</span>
      </span>
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <el-dialog v-model="editProjectManagerDialogVisible" title="修改项目经理" width="30%">
      <el-form :model="projectManagerForm" label-width="80px">
        <el-form-item label="项目经理">
          <el-input v-model="projectManagerForm.name" placeholder="请输入项目经理名称"/>
        </el-form-item>
      </el-form>
      <template #footer>
    <span class="dialog-footer">
      <el-button @click="editProjectManagerDialogVisible = false">取消</el-button>
      <el-button type="primary" @click="saveProjectManager">保存</el-button>
    </span>
      </template>
    </el-dialog>


    <!-- 添加项目表单组件 -->
    <ProjectForm ref="formRef" @success="handleSuccess"/>

  </ContentWrap>
</template>

<script setup lang="ts">
import {ProjectGropApi, ProjectGropVO} from '@/api/projectmanage/projectgrop'
import {useRoute} from 'vue-router'
import {dateFormatter, formatDate} from '@/utils/formatTime'
import ProjectForm from '@/views/projectmanage/project/ProjectForm.vue'
import {ProjectApi} from '@/api/projectmanage/project'
import {useMessage} from '@/hooks/web/useMessage'
import {useI18n} from '@/hooks/web/useI18n'
import businessUploadFile from '@/views/infraInfo/bussinessFileType/businessUploadFile.vue'
import {CustomerApi} from '@/api/projectmanage/customer'
import {BusinessFileTypeApi} from "@/api/infraInfo/bussinessFileType";
import ProjectManagerApprovalForm
  from "@/views/projectmanage/project/projectperson/ProjectManagerApprovalForm.vue";
import {ref} from "vue";
import {
  ProjectManagerApprovalApi,
  ProjectManagerApprovalVO
} from '@/api/projectmanage/projectmanagerapproval'
import {pinyin} from "pinyin";
import {ProjectPayRecRelationApi} from '@/api/projectmanage/projectpayrecrelation'
import {ContractApi} from "@/api/projectmanage/contract";
import {ElMessage} from "element-plus";

// 定义项目和费项的类型
interface Fee {
  amount: number
  type: string
  invNo: string
  taxRate: number
  price: number
  alreadyAmount: number
  status: string
  isRec?: string
  isRecPay?: string
  isToincome?: string
  isToincomePay?: string
  isInv?: string
  isInvPay?: string
}

interface Project {
  id: number
  projectCode: string
  projectName: string
  payerName: string
  payeeName: string
  totalAmount: number
  receivedAmount: number
  paidAmount: number
  type: string
  managingDepartment: string
  projectManagerName?: string
  fees: Fee[]
}

defineOptions({name: 'ProjectGropDetail'})

const route = useRoute()
const loading = ref(false)
const approvalloading = ref(false)

const projectManagerStatus = ref('')
const detail = ref<Record<string, any>>({})
const receivableProjects = ref<Project[]>([])
const payableProjects = ref<Project[]>([])
const contractAmounts = ref({
  receivableAmount: 0,
  payableAmount: 0
})
const paidPercentage = ref<number | string>(0)
const totalReceivedAmount = ref(0)
const totalPaidAmount = ref(0)
const formRef = ref()
const receivedPercentage = ref<number | string>(0)
const projectManager = ref('')
const message = useMessage() // 消息弹窗
const {t} = useI18n() // 国际化
const approvalList = ref<ProjectManagerApprovalVO[]>([])
const total_approval = ref(0)
// 客户选项
const customerOptions = ref([])
const approvalFormRef = ref()
const currentProjectGrop = ref({});

const queryApprovalParams = reactive({
  pageNo: 1,
  pageSize: 10,
  projectGropId: currentProjectGrop,
  flowId: undefined,
  fdTemplataId: undefined,
  projectName: undefined,
  projectIndroduction: undefined,
  mainProjectName: undefined,
  childProjectName: undefined,
  docSubject: undefined,
  projectManager: undefined,
  phone: undefined,
  projectCatagory: undefined,
  estimateTime: undefined,
  estimateMoney: undefined,
  source: undefined,
  createTime: [],
  approvalForm: undefined,
  docCreator: undefined,
  selectedRows: [],
  status: undefined,
})

const editProjectManagerDialogVisible = ref(false);

// 表单数据
const projectManagerForm = reactive({
  name: ''
});

const groupFileList = ref([])
const fileList = ref([])
const handleEditProjectManager = () => {
  // 初始化表单数据
  projectManagerForm.name = detail.value.projectManager || '';
  editProjectManagerDialogVisible.value = true;
};

const saveProjectManager = async () => {
  try {
    const updateParams = {
      ...detail.value,
      projectManager: projectManagerForm.name,
      id: detail.value.id
    }
    await ProjectGropApi.updateProjectGropManager(updateParams as unknown as ProjectGropVO);
    // 更新本地数据
    detail.value.projectManager = projectManagerForm.name;
    message.success('项目经理信息已更新');
  } catch (error) {
    message.error('更新失败，请稍后重试');
  } finally {
    // 关闭弹窗
    editProjectManagerDialogVisible.value = false;
  }
};


console.log(currentProjectGrop.value)
// 获取客户列表
const getCustomerOptions = async () => {
  const data = await CustomerApi.getCustomerName()
  customerOptions.value = data
}

//  合同详情页跳转
const handleContractClick = (row) => {
  let contractId = row.contractId ?? 0
  let projectId = row.id
  router.push({name: 'contractData', params: {projectId, contractId}});
};

// 合同oa跳转打印
const handleoarouter =async (row) => {
  if (!row.contractId) {
    ElMessage.error('该项目还没有合同！！')
    return
  }
  const timeTick = Date.now().toString();//时间戳
  const name = await ProjectManagerApprovalApi.getName()
  const yonghu = pinyin(name, {style: pinyin.STYLE_NORMAL}).flat().join('');
  const data=await ContractApi.oarouter({
    yonghu:yonghu,
    timeTick:timeTick
  })
  const signature = data          //获取的临时口令
  const flowId = await ContractApi.getflowId({
    contractId: row.contractId
  })
  if(flowId!=null){
    let encodedFlowId = encodeURIComponent(flowId);    //flowId转URL码
    let nexturl = `/km/review/km_review_main/kmReviewMain.do?method=view&fdId=${encodedFlowId}`
    const url=`https://oa.gzport.com/oa/api/auth/login_by_sso?sso=customer_sso&loginName=${yonghu}&tickTime=${timeTick}&signature=${signature}&next=`+escape(nexturl)
    console.log(url)
    window.open(url);
  }else {
    ElMessage.error(t('未找到合同OA，请重试！！'));
  }
}

const getPaidAmount_isRecPay = (fees: any) => {
  let total = 0
  fees.forEach((fee: any) => {
    if (fee.isRecPay === '是') {
      total += Number(fee.amount)
    }
  })
  return total
}

const getReceivedAmount = (row: any) => {
  ProjectApi.getProjecNowAmounttByProjectIdAndYwType({
    projectId: row.id,
    ywType: '收款'
  }).then(res => {
    console.log(res)
    console.log("dddd")
    return res
  })
}

const getReceivedAmount_isRec = (fees: any) => {
  let total = 0
  fees.forEach((fee: any) => {
    if (fee.isRec === '是') {
      total += Number(fee.amount)
    }
  })
  return total
}

// 计算当前利润：付款费项金额之和-收款费项金额之和
const currentProfit = computed(() => {
  let receivableFeeSum = 0
  let payableFeeSum = 0

  // 计算收款费项总和
  receivableProjects.value.forEach(project => {
    project.fees?.forEach(fee => {
      receivableFeeSum += Number(fee.amount) || 0
    })
  })

  // 计算付款费项总和
  payableProjects.value.forEach(project => {
    project.fees?.forEach(fee => {
      payableFeeSum += Number(fee.amount) || 0
    })
  })

  return receivableFeeSum - payableFeeSum
})

// 计算预计利润：项目已收款之和-已付款之和
const expectedProfit = computed(() => {
  let totalReceived = 0
  let totalPaid = 0

  // 计算已收款总和
  receivableProjects.value.forEach(project => {
    totalReceived += Number(project.receivedAmount) || 0
  })

  // 计算已付款总和
  payableProjects.value.forEach(project => {
    totalPaid += Number(project.paidAmount) || 0
  })

  return totalReceived - totalPaid
})


const totalReceivedAmount_toincome = computed(() => {
  let total = 0
  receivableProjects.value.forEach(project => {
    project.fees?.forEach(fee => {
      if ("是" == fee.isToincome) {
        total += Number(fee.amount) || 0
      }
    })
  })
  return total.toFixed(2)
})

const totalPayedAmount_toincome_pay = computed(() => {
  let total = 0
  payableProjects.value.forEach(project => {
    project.fees?.forEach(fee => {
      console.log(fee.isToincomePay)
      if ("是" == fee.isToincomePay) {
        total += Number(fee.amount) || 0
      }
    })
  })
  return total.toFixed(2)
})

// 计算已经开票金额
const totalReceivedAmount_inv = computed(() => {
  let total = 0
  receivableProjects.value.forEach(project => {
    project.fees?.forEach(fee => {
      if ("是" == fee.isInv) {
        total += Number(fee.amount) || 0
      }
    })
  })
  return total.toFixed(2)
})

// 计算已经收票金额

const totalPaidAmount_inv_pay = computed(() => {
  let total = 0
  payableProjects.value.forEach(project => {
    project.fees?.forEach(fee => {
      if ("是" == fee.isInvPay) {
        total += Number(fee.amount) || 0
      }
    })
  })
  return total.toFixed(2)
})

// 计算已开票占比
const invReceivedPercentage = computed(() => {
  const receivableAmount = Number(contractAmounts.value.receivableAmount) || 0
  const invAmount = Number(totalReceivedAmount_inv.value) || 0
  if (receivableAmount === 0) return '0.00'
  return ((invAmount / receivableAmount) * 100).toFixed(2)
})

// 计算已收票占比
const invPaidPercentage = computed(() => {
  const payableAmount = Number(contractAmounts.value.payableAmount) || 0
  const invPayAmount = Number(totalPaidAmount_inv_pay.value) || 0
  if (payableAmount === 0) return '0.00'
  return ((invPayAmount / payableAmount) * 100).toFixed(2)
})


// 计算剩余收款金额
const remainingReceivable = computed(() => {
  const receivableAmount = Number(contractAmounts.value.receivableAmount) || 0
  const received = Number(totalReceivedAmount.value) || 0
  return (receivableAmount - received).toFixed(2)
})

// 计算剩余付款金额
const remainingPayable = computed(() => {
  const payableAmount = Number(contractAmounts.value.payableAmount) || 0
  const paid = Number(totalPaidAmount.value) || 0
  return (payableAmount - paid).toFixed(2)
})

// 计算收付合同差
const contractDifference = computed(() => {
  const receivableAmount = Number(contractAmounts.value.receivableAmount) || 0
  const payableAmount = Number(contractAmounts.value.payableAmount) || 0
  return (receivableAmount - payableAmount).toFixed(2)
})

// （收付合同差/收合同）毛利率
const contractDifferenceRate = computed(() => {
  const receivableAmount = Number(contractAmounts.value.receivableAmount) || 0
  const contractDiff = Number(contractDifference.value) || 0
  if (receivableAmount === 0) return '0.00'
  return ((contractDiff / receivableAmount) * 100).toFixed(2)
})

// 关联付款项目
const filteredPayableProjects = ref<Project[]>([])
const isFiltered = ref(false)
const allPayableProjects = ref<Project[]>([])

// 显示关联的付款项目
const showRelatedPayProjects = async (project: any) => {
  try {
    // 更新当前行，以便双击时可以正确导航
    currentRow.value = project

    // 获取与该收款项目关联的付款项目编号列表
    const res = await ProjectPayRecRelationApi.getProjectPayRecRelationListByRecProjectCode({
      recProjectCode: project.projectCode
    })

    if (res && res.length > 0) {
      // 根据关联关系筛选付款项目
      const payProjectCodes = res.map((item: any) => item.payProjectCode)
      filteredPayableProjects.value = allPayableProjects.value.filter(item =>
        payProjectCodes.includes(item.projectCode)
      )
      isFiltered.value = true
    } else {
      // message.info('该收款项目没有关联的付款项目')
      // 如果没有关联付款项目，显示空列表
      filteredPayableProjects.value = []
      isFiltered.value = true
    }
  } catch (error) {
    message.error('获取关联付款项目失败')
  }
}

// 重置付款项目列表，显示所有项目
const resetPayableProjects = () => {
  filteredPayableProjects.value = allPayableProjects.value
  isFiltered.value = false
}

/** 获取详情数据 */
const getDetail = async () => {
  const projectGropNumber = route.params.projectGropNumber as string
  if (!projectGropNumber) return

  loading.value = true
  try {
    // 获取项目组详情（废弃）
    // detail.value = await ProjectGropApi.getProjectGropByNumber(projectGropNumber)
    // 获取客户列表
    await getCustomerOptions()

    // 获取项目组 ID
    const projectGropList = await ProjectGropApi.getProjectGropPage({
      pageNo: 1,
      pageSize: 1,
      projectGropNumber: projectGropNumber
    })

    if (projectGropList.list && projectGropList.list.length > 0) {
      const projectGropId = projectGropList.list[0].id
      currentProjectGrop.value = projectGropList.list[0].id;
      console.log('projectGropId', projectGropId)
      console.log('projectGropId', currentProjectGrop.value)
      // 使用 get 方法获取详情
      detail.value = await ProjectGropApi.getProjectGrop(projectGropId)
      // 获取业务部附件
      getGroupFileList()
      console.log(detail.value)
      try {
        const status = await ProjectGropApi.getStatus(projectGropId)
        projectManagerStatus.value = status
        console.log(status)
      } catch (error) {
        console.error('获取状态失败:', error.response?.data || error.message)
      }

    }

    // 获取收款项目及费项
    receivableProjects.value = await ProjectGropApi.getReceivableProjects(projectGropNumber)
    console.log(receivableProjects.value)
    // 获取付款项目及费项
    payableProjects.value = await ProjectGropApi.getPayableProjects(projectGropNumber)
    console.log(payableProjects.value)
    // 保存所有付款项目数据，用于筛选恢复
    allPayableProjects.value = JSON.parse(JSON.stringify(payableProjects.value))
    // 初始化筛选后的付款项目数据
    filteredPayableProjects.value = payableProjects.value

    // 获取合同金额统计
    contractAmounts.value = await ProjectGropApi.getContractAmounts(projectGropNumber)
    // 请求已收款总金额
    totalReceivedAmount.value = await ProjectGropApi.getProjecGroupNowAmounttByGroupCodeAndYwType({
      groupCode: projectGropNumber,
      ywType: '收款'
    })
    if (contractAmounts.value.receivableAmount == 0) {
      receivedPercentage.value = 0
    } else {
      // 计算已收款占比
      receivedPercentage.value = ((totalReceivedAmount.value / contractAmounts.value.receivableAmount) * 100).toFixed(2)
    }
    // 请求已付款总金额
    totalPaidAmount.value = await ProjectGropApi.getProjecGroupNowAmounttByGroupCodeAndYwType({
      groupCode: projectGropNumber,
      ywType: '付款'
    })
    if (contractAmounts.value.payableAmount == 0) {
      paidPercentage.value = 0
    } else {
      // 计算已付款占比
      paidPercentage.value = ((totalPaidAmount.value / contractAmounts.value.payableAmount) * 100).toFixed(2)
    }
  } finally {
    loading.value = false
  }
}

/** 格式化日期时间 */
const formatDateTime = (time: string) => {
  if (!time) return ''
  return formatDate(new Date(time), 'YYYY-MM-DD HH:mm:ss')
}

// 选中的收款项目
const selectedRecProjects = ref<Project[]>([])

// 处理收款项目选择变化
const handleReceivableSelectionChange = (selection: any[]) => {
  selectedRecProjects.value = selection
}

// 打开表单
const openForm = async (type: string, projectType: string) => {
  if (projectType === 'payable' && selectedRecProjects.value.length === 0) {
    message.error('请先勾选要关联的收款项目')
    return
  }

  // 如果是新增收款项目，先检查前期资料
  if (type === 'create' && projectType === 'receivable') {
    const hasEarlyMaterials = await checkHasEarlyMaterials()
    if (!hasEarlyMaterials) {
      try {
        await message.confirm('无前期附件，是否确认新增收款项目？')
      } catch {
        // 用户点击取消，不继续执行
        return
      }
    }
  }

  // 新增辅助变量
  const typeMap = ref()
  const managingDepartment = ref()
  const projectManagerName = ref()

  // 根据项目具体收/付情况传入数据
  if (projectType === 'payable') {
    if (selectedRecProjects.value.length > 0) {
      typeMap.value = selectedRecProjects.value[0].type
      managingDepartment.value = selectedRecProjects.value[0].managingDepartment
      projectManagerName.value = selectedRecProjects.value[0].projectManagerName || ''

    }
  } else {
    if (payableProjects.value.length > 0) {
      typeMap.value = payableProjects.value[0].type
      managingDepartment.value = payableProjects.value[0].managingDepartment
    }
  }

  // 打开表单，并传入数据
  formRef.value.open(type, undefined, {
    projectGroupCode: detail.value.projectGropNumber,
    projectGroupName: detail.value.projectGropName,
    type: typeMap.value,
    managingDepartment: managingDepartment.value,
    projectManagerName: projectManagerName.value,
    payReciRelation: projectType === 'receivable' ? '收' : '付',
    selectedReceivableProjects: selectedRecProjects.value,
    projectIntroduction: detail.value.projectIntroduction,
  })
}

// 记录新增项目前的收款项目数量
const previousReceivableCount = ref(0)

// 检查是否有前期资料的通用方法
const checkHasEarlyMaterials = async (): Promise<boolean> => {
  try {
    // 过滤掉临时文件（id为null的文件）
    const validFiles = groupFileList.value?.flat().filter((file: any) => file.id !== null && file.id !== undefined) || []
    return validFiles.length > 0
  } catch (error) {
    console.error('检查前期资料失败:', error)
    return false
  }
}

// 处理表单提交成功
const handleSuccess = async () => {
  // 重新获取项目组详情数据
  await getDetail()
}


// 添加删除项目的方法
const handleDeleteProject = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()

    // 先获取项目信息
    const project = await ProjectApi.getProject(id)

    // 根据收付关系删除关联关系
    if (project.payReciRelation === '付') {
      // 如果是付款项目，删除所有与该付款项目相关的关联关系
      await ProjectPayRecRelationApi.deleteProjectPayRecRelationByPayProjectCode(project.projectCode)
    } else if (project.payReciRelation === '收') {
      // 如果是收款项目，删除所有与该收款项目相关的关联关系
      await ProjectPayRecRelationApi.deleteProjectPayRecRelationByRecProjectCode(project.projectCode)
    }

    // 发起删除项目
    await ProjectApi.deleteProject(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getDetail()
  } catch (error: any) {
    // message.error('删除失败：' + (error.message || '未知错误'))
  }
}

const openApprovalForm = (type: string, id?: number) => {
  if (type === 'create') {
    // 检查列表是否为空或所有数据的状态是否为"已废弃"
    if (approvalList.value.length === 0 || approvalList.value.every(approval => approval.status === '已废弃' || approval.status === '被驳回')) {
      approvalFormRef.value.open(type, id, currentProjectGrop.value);
      console.log('projectGropId123123:', currentProjectGrop.value);
    } else {
      message.warning('只有在列表为空或所有数据的状态为"已废弃"或"被驳回"时才能新增');
    }
  } else {
    approvalFormRef.value.open(type, id, currentProjectGrop.value);
  }
}

const oarouter = async (flowId: string) => {
  const nickname = await ProjectManagerApprovalApi.getName({})
  console.log(nickname)
  const timeTick = Date.now().toString();//时间戳
  const name = nickname;//获取当前登录用户的姓名
  console.log(name)
  const yonghu = pinyin(name, {style: pinyin.STYLE_NORMAL}).flat().join('');//获取当前登录用户的拼音
  console.log(yonghu)
  const data = await ProjectManagerApprovalApi.oarouter({
    yonghu: yonghu,
    timeTick: timeTick
  })
  const signature = data          //获取的临时口令
  let encodedFlowId = encodeURIComponent(flowId);
  let nexturl = `/km/review/km_review_main/kmReviewMain.do?method=view&fdId=${encodedFlowId}`
  const url = `http://oa.gzport.com/oa/api/auth/login_by_sso?sso=customer_sso&loginName=${yonghu}&tickTime=${timeTick}&signature=${signature}&next=` + escape(nexturl)
  console.log(url)
  window.open(url);
}

//项目经理废弃
const handleApprovalAbandon = async (flowId: string) => {
  approvalloading.value = true
  console.log(flowId)
  try {
    await message.confirm('确认废弃该审批吗？')
    const data = await ProjectManagerApprovalApi.abandonProjectManagerApproval({flowId});
    console.log(data)
    message.success('废弃成功')
    await getApprovalList()
  } finally {
    approvalloading.value = false
  }
}

const getApprovalList = async () => {
  approvalloading.value = true
  try {
    const data = await ProjectManagerApprovalApi.getProjectManagerApprovalPage(
      queryApprovalParams
    )
    console.log(currentProjectGrop.value)
    approvalList.value = data.list
    total_approval.value = data.total
    await getDetail()
  } finally {
    approvalloading.value = false
  }
}

const getGroupFileList = async () => {
  let queryParams = {
    fileBusinessType: `业务部附件`,
    businessId: detail.value.id,
  }
  try {
    const data = await BusinessFileTypeApi.businessFileList(queryParams)
    fileList.value = data
    // fileList是一个对象数组，按照fileBusinessTypeDetail进行分组，形成一个二维数组groupFileList
    let group: any = {}
    for (let i = 0; i < fileList.value.length; i++) {
      let item: any = fileList.value[i]
      if (!group[item.fileBusinessTypeDetail]) {
        group[item.fileBusinessTypeDetail] = []
      }
      group[item.fileBusinessTypeDetail].push(item)
    }

    // 如果没有任何附件，创建一个默认的"项目前期资料"组
//     if (Object.keys(group).length === 0) {
//       group['项目前期资料'] = [{
//         id: null,
//         fileBusinessType: '业务部附件',
//         fileBusinessTypeDetail: '项目前期资料',
//         configId: null,
//         name: null,
//         path: null,
//         url: null,
//         type: null,
//         size: null,
//         creator: null,
//         createTime: null,
//         updater: null,
//         updateTime: null,
//         deleted: null,
//         businessId: null
//       }]
//     }

    groupFileList.value = Object.values(group)
  } finally {

  }
};

const collectedUrls = (group: any[]) => {
  // 创建一个计算属性，返回当前 group 对象中所有 url 的数组
  return group.map(item => item.url);
};

const collectUrl2NameMap = (group: any[]) => {
  // 创建一个计算属性，返回当前 group 对象中所有 url 的数组
  const url2NameMap: any = {};
  group.forEach(item => {
    url2NameMap[item.url] = item.name;
  });
  return url2NameMap;
};

// 处理文件变化（上传/删除后）
const handleFileChange = async () => {
  // 添加短暂延迟，确保后端数据已更新
  setTimeout(async () => {
    await getGroupFileList()
  }, 500)
}

onMounted(() => {
  getDetail().then(() => {
    getApprovalList();
  });


});

//
const router = useRouter()
const navigateToProject = (row: any) => {
  router.push(`/project/${row.id}`)
}

const currentRow = ref()
const handleRowClick = (row: any) => {
  currentRow.value = row
}

// 返回项目组台账页面
const goBack = () => {
  router.push('/projectmanage/projectgrop')
}
</script>

<style scoped>
.project-info-descriptions {
  width: 100%;
}

.project-info-descriptions :deep(.el-descriptions__table) {
  width: 100%;
  table-layout: fixed;
}

.project-info-descriptions :deep(.el-descriptions__cell) {
  width: 50%;
}

.project-info-descriptions :deep(.el-descriptions__label) {
  width: 180px;
  text-align: left;
  padding-left: 12px;
  font-weight: bold;
  background-color: #fafafa;
}

.project-info-descriptions :deep(.el-descriptions__content) {
  padding-left: 12px;
}

.project-info-descriptions :deep(.el-descriptions__body) {
  width: 100%;
}

.project-info-descriptions :deep(.el-descriptions__row) {
  display: flex;
}

.project-info-descriptions :deep(.el-descriptions-item__container) {
  display: flex;
  width: 100%;
}

.project-info-descriptions :deep(.el-descriptions__cell.is-bordered-content) {
  width: 50%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.green-text {
  color: #67C23A;
  font-weight: bold;
}

.red-text {
  color: #F56C6C;
  font-weight: bold;
}

.blue-text {
  color: #409EFF;
  font-weight: bold;
}

.mb-4 {
  margin-bottom: 16px;
}

.warning-button {
  border-color: #E6A23C !important;
  color: #E6A23C !important;
}

.warning-button:hover {
  background-color: #fdf6ec !important;
  border-color: #E6A23C !important;
  color: #E6A23C !important;
}
</style>
