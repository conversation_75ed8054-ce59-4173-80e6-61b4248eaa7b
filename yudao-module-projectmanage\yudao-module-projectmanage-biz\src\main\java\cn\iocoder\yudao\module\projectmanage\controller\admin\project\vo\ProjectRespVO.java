package cn.iocoder.yudao.module.projectmanage.controller.admin.project.vo;

import cn.iocoder.yudao.module.projectmanage.dal.dataobject.project.ProjectDO;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDate;
import java.util.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
@Schema(description = "管理后台 - 项目 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ProjectRespVO {

    @Schema(description = "记录id", requiredMode = Schema.RequiredMode.REQUIRED, example = "12920")
    @ExcelProperty("记录id")
    private Long id;

    @Schema(description = "项目组id", example = "11192")
    @ExcelProperty("项目组id")
    private Long projectGroupId;

    @Schema(description = "项目组编号")
    @ExcelProperty("项目组编号")
    private String projectGroupCode;

    @Schema(description = "项目组名称", example = "王五")
    @ExcelProperty("项目组名称")
    private String projectGroupName;

    @Schema(description = "项目编号")
    @ExcelProperty("项目编号")
    private String projectCode;

    @Schema(description = "业务部项目编号", example = "11123123")
    @ExcelProperty("业务部项目编号")
    private String busiDepProjectCode;

    @Schema(description = "项目名称", example = "张三")
    @ExcelProperty("项目名称")
    private String projectName;

    @Schema(description = "项目内容")
    @ExcelProperty("项目内容")
    private String projectContent;

    @Schema(description = "主管部门")
    @ExcelProperty("主管部门")
    private String managingDepartment;

    @Schema(description = "配合部门")
    @ExcelProperty("配合部门")
   //private String cooperatingDepartment;
    private List<String> cooperatingDepartment;

    @Schema(description = "项目经理", example = "李四")
    @ExcelProperty("项目经理")
    private String projectManagerName;

    @Schema(description = "金额")
    @ExcelProperty("金额")
    private BigDecimal totalAmount;

    @Schema(description = "合同id ", example = "24008")
    @ExcelProperty("合同id ")
    private Long contractId;

    @Schema(description = "合同编号")
    @ExcelProperty("合同编号")
    private String contractCode;

    @Schema(description = "合同名称", example = "李四")
    @ExcelProperty("合同名称")
    private String contractName;

    @Schema(description = "收付关系")
    @ExcelProperty("收付关系")
    private String payReciRelation;

    @Schema(description = "付款方", example = "张三")
    @ExcelProperty("付款方")
    private String payerName;

    @Schema(description = "付款方id", example = "张三")
    @ExcelProperty("付款方id")
    private Long payerId;

    @Schema(description = "付款方联系人")
    @ExcelProperty("付款方联系人")
    private String payerContactor;

    @Schema(description = "付款方联系方式")
    @ExcelProperty("付款方联系方式")
    private String payerContactDetails;

    @Schema(description = "收款方", example = "张三")
    @ExcelProperty("收款方")
    private String payeeName;

    @Schema(description = "收款方id", example = "张三")
    @ExcelProperty("收款方id")
    private Long payeeId;

    @Schema(description = "收款方联系人")
    @ExcelProperty("收款方联系人")
    private String payeeContactor;

    @Schema(description = "收款方联系方式")
    @ExcelProperty("收款方联系方式")
    private String payeeContactDetails;

    @Schema(description = "来源方式（直接委托、比质比价、公开投标、其他）")
    @ExcelProperty("来源方式（直接委托、比质比价、公开投标、其他）")
    private String sourceMethods;

    @Schema(description = "采购方式")
    @ExcelProperty("采购方式")
    private String buyMethods;

    @Schema(description = "属性")
    @ExcelProperty("属性")
    private String attribute;

    @Schema(description = "类别", example = "1")
    @ExcelProperty("类别")
    private String type;

    @Schema(description = "计划验收时间")
    @ExcelProperty("计划验收时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate plannedAcceptanceTime;

    @Schema(description = "是否验收（是、否）")
    @ExcelProperty("是否验收（是、否）")
    private String ifAccepted;

    @Schema(description = "实际验收时间")
    @ExcelProperty("实际验收时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate actualAcceptanceTime;

    @Schema(description = "是否超期（是、否）", example = "32735")
    @ExcelProperty("是否超期（是、否）")
    private String ifRelaid;

    @Schema(description = "已付款金额")
    @ExcelProperty("已付款金额")
    private BigDecimal paidAmount;

    @Schema(description = "是否付款完成（是、否）")
    @ExcelProperty("是否付款完成（是、否）")
    private String ifPaidFully;

    @Schema(description = "已收款金额")
    @ExcelProperty("已收款金额")
    private BigDecimal receivedAmount;

    @Schema(description = "是否收款完成（是、否）")
    @ExcelProperty("是否收款完成（是、否）")
    private String ifReceivedFully;

    @Schema(description = "中标候选人")
    @ExcelProperty("中标候选人")
    private String successedBidderCandidate;

    @Schema(description = "备注")
    @ExcelProperty("备注")
    private String remarks;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "项目参与人员")
    @ExcelProperty("项目参与人员")
    private String projectParticipatedPerson;

    @Schema(description = "项目前期对接人")
    @ExcelProperty("项目前期对接人")
    private String projectPreStartupLiaison;

    @Schema(description = "项目来源单位名称")
    @ExcelProperty("项目来源单位名称")
    private String projectSourceUnitName;

    @Schema(description = "项目来源单位联系人")
    @ExcelProperty("项目来源单位联系人")
    private String projectSourceUnitPerson;

    @Schema(description = "项目来源单位联系方式")
    @ExcelProperty("项目来源单位联系方式")
    private String projectSourceUnitContactDetails;

    @Schema(description = "预算金额")
    @ExcelProperty("预算金额")
    private BigDecimal estimatedAmount;

    @Schema(description = "进度")
    @ExcelProperty("进度")
    private String percentage;

    @Schema(description = "是否推送审批")
    private String approved;

    @Schema(description = "项目状态（未完结，已完结）")
    private String state;

    @Schema(description = "项目审批状态")
    private String approvalStatus;

    private String flowId;

    private String materialApprovalStatus;

    private String materialFlowId;

    private String fdId;

    /**
     * 项目重要性
     */
    private String importance;

    /**
     * 项目性质
     */
    private String nature;

    /**
     * 项目系数
     */
    private BigDecimal coefficient;

    /**
     * 项目类型（多选，JSON存储）
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private String projectType;
}
