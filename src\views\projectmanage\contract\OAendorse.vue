<template>
  <div>
    <table class="approval-table">
      <thead>
      <tr>
        <th>审批节点</th>
        <th>审批人</th>
        <th>审批意见</th>
      </tr>
      </thead>
      <tbody>
      <tr v-for="(node, index) in approvalNodes" :key="index" class="approval-row">
        <td>{{ node.name }}</td>
        <td>
          <el-select v-model="node.approver" placeholder="请选择审批人" class="approval-select" clearable :disabled="node.name === '总经理' || node.name === '总法律顾问'">
            <el-option
              v-for="dict in getStrDictOptions(node.name === '总法律顾问' || node.name === '分管领导' || node.name === '总经理' ? 'oa领导' : 'oa审批人')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </td>
        <td class="comment-cell">
          <el-input v-model="node.comment" placeholder="请输入审批意见" class="approval-input" readonly />
        </td>
      </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup lang="ts">
import { ref, defineExpose, defineProps, onMounted } from 'vue';
import { getStrDictOptions } from "@/utils/dict";
import {ContractApi} from "@/api/projectmanage/contract";
import {ProjectApi} from "@/api/projectmanage/project";

const approvalNodes = ref([
  { name: '主办部门', approver: '', comment: '', key: 'N6' },
  { name: '协办部门', approver: '', comment: '', key: 'N5' },
  { name: '业务拓展部', approver: '174b3931ea7967dee708621485188408', comment: '', key: 'N16' },
  { name: '财务部', approver: '174b3931cd9fdf14b9d522b4265afafe', comment: '', key: 'N7' },
  { name: '法务审核', approver: '1820febca893a98622b577545059a8ab', comment: '', key: 'N9' },
  { name: '业务拓展部（审批）', approver: '174b3931ea7967dee708621485188408', comment: '', key: 'N8' },
  { name: '总法律顾问', approver: '174b3931ced1bd0db29365f40a3b08fd', comment: '', key: 'N18' },
  { name: '分管领导', approver: '', comment: '', key: 'N17' },
  { name: '总经理', approver: '174b3931ce210bd9084e56d414a9fb92', comment: '', key: 'N4' },
  { name: '归档', approver: '174b3931ea75f307e98b4bb43a7bfce3', comment: '', key: 'N13'}
]);

const getApproverList = () => {
  return approvalNodes.value.map(node => ({
    key: node.key,
    approver: node.approver,
    comment: node.comment,
    name: node.name
  }));
};

// 定义一个异步提交方法（如果需要）


const props = defineProps({
  executingDeparment: {
    type: String,
    required: true
  },
  contractId: {
    type: Number,
    required: true
  },
  handle:{
    type:String,
    required: true
  },
  projectId: {
    type: String,
  },
});
onMounted(async () => {
  console.log(props.contractId+"显示值看看看看")
  if(props.contractId!==undefined){
    try {
      const response = await ContractApi.getoaendorse({
        contractId: props.contractId
      })
      if (response && response.length > 0) {
        approvalNodes.value = response.map((node: any) => ({
          name: node.name,
          approver: node.approver,
          comment: node.comment,
          key: node.key
        }));
      }
    } catch (error) {
      console.error('获取审批记录失败:', error);
    }
  }
  console.log(props.contractId+"显示值看看看看")
  if (props.projectId) {
    try {
      const project = await ProjectApi.getProject(props.projectId)
      const departments = Array.isArray(project.cooperatingDepartment)
        ? project.cooperatingDepartment
        : [project.cooperatingDepartment].filter(Boolean)

      updateCooperativeApprovalNodes(departments)
    } catch (error) {
      console.error('获取项目信息失败:', error)
    }
  }
  updateLegalCounselNode()
  console.log(approvalNodes.value)
});

const updateLegalCounselNode = () => {
  const legalCounselNode = approvalNodes.value.find(node => node.name === '总法律顾问')
  const guidangNode = approvalNodes.value.find(node => node.name === '归档')
  if (legalCounselNode) {
    legalCounselNode.key = props.handle === '王慧敏' ? 'N20' : 'N18'
  }
  if (guidangNode){
    if(props.handle === '王慧敏') {
      guidangNode.approver = '1820febca893a98622b577545059a8ab'
    } else if(props.handle === '张子良') {
      guidangNode.approver = '18928d3dd1885e9649292f04c8da8b21'
    } else {
      guidangNode.approver = '174b3931ea75f307e98b4bb43a7bfce3'
    }
  }
}
// 新增 watch 监听 projectId，获取项目详情并更新协办部门节点
watch(() => props.projectId, async (newProjectId) => {
  if (!newProjectId) return

  try {
    const project = await ProjectApi.getProject(newProjectId)
    const departments = Array.isArray(project.cooperatingDepartment)
      ? project.cooperatingDepartment
      : [project.cooperatingDepartment].filter(Boolean)

    updateCooperativeApprovalNodes(departments)
  } catch (error) {
    console.error('获取项目信息失败:', error)
  }
}, { immediate: true })

// 动态更新协办部门审批节点
const updateCooperativeApprovalNodes = (departments) => {
  // 移除旧的协办部门节点
  approvalNodes.value = approvalNodes.value.filter(node => node.key !== 'N5')

  // 插入新节点（在 "主办部门" 节点后插入）
  const hostIndex = approvalNodes.value.findIndex(node => node.name === '主办部门')

  // 插入多个协办部门节点
  departments.forEach((dept, index) => {
    approvalNodes.value.splice(hostIndex + 1 + index, 0, {
      name: `协办部门（${dept}）`,
      approver: getDefaultApprover(dept),
      comment: '',
      key: `N5`
    })
  })

}
const getDefaultApprover = (department) => {
  const departmentMap = {
    '软件开发一部': '174b3931ea85b9cf0f5087a48b0952fc',  //陈华
    '软件开发二部': '174b3931eab9d9a9ac2b31546db9ff0c',  //甘永嘉
    '系统集成部': '174b3931eaf6cc7ee4fbd914a23bf43e',    //潘松涛
    '通信服务部': '174b3931eaf6cc7ee4fbd914a23bf43e',    //潘松涛
    '业务拓展部': '174b3931ea7967dee708621485188408',    //余飞
    '财务会计部': '174b3931cd9fdf14b9d522b4265afafe',    //林玲
    '综合事务部': '174b3931ea5e655229992924989be165',    //高晖
  }
  return departmentMap[department] || ''
}


// props.handle 变化时更新
watch(() => props.handle, (newHandle) => {
  updateLegalCounselNode()
})
// 监听 executingDeparment 的变化
watch(() => props.executingDeparment, (newExecutingDeparment) => {
  const leaderNode = approvalNodes.value.find(node => node.name === '分管领导');
  if (leaderNode) {
    // 根据 executingDeparment 的值设置 approver
    if (newExecutingDeparment === '系统集成部' || newExecutingDeparment === '财务会计部') {
      leaderNode.approver = '174b3931eab521b0e3961994ffa8b5cd';   //张志真
    } else if (newExecutingDeparment ==='业务拓展部'){
      leaderNode.approver = '174b3931ce210bd9084e56d414a9fb92';   //李小毛
    }else if(newExecutingDeparment === '综合事务部'){
      leaderNode.approver = '174b3931eab521b0e3961994ffa8b5cd';   //张志真
    }else if (newExecutingDeparment === '通信服务部'){
      leaderNode.approver = '174b3931eab521b0e3961994ffa8b5cd';   //张志真
    }else{
      leaderNode.approver = '174b3931eab521b0e3961994ffa8b5cd';   //张志真
    }
  }
}, { immediate: true });
// 监听 executingDeparment 的变化
watch(() => props.executingDeparment, (newExecutingDeparment) => {
  const leaderNode = approvalNodes.value.find(node => node.name === '主办部门');
  if (leaderNode) {
    // 根据 executingDeparment 的值设置 approver
    if (newExecutingDeparment === '系统集成部') {
      leaderNode.approver = '174b3931eaf6cc7ee4fbd914a23bf43e';
    } else if (newExecutingDeparment === '软件开发一部') {
      leaderNode.approver = '174b3931ea85b9cf0f5087a48b0952fc';
    } else if (newExecutingDeparment === '软件开发二部') {
      leaderNode.approver = '174b3931eab9d9a9ac2b31546db9ff0c';
    }
  }
}, { immediate: true });


const hasCooperativeDepartments = () => {
  return approvalNodes.value.some(node => node.name.startsWith('协办部门'))
}

// 暴露方法给父组件
defineExpose({ getApproverList, hasCooperativeDepartments })
</script>

<style scoped>
/* 保持原有样式 */
.approval-table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  overflow: hidden;
}

.approval-table th {
  background: linear-gradient(to right, #f2f2f2, #ebeef5);
  color: #606266;
  font-weight: bold;
  padding: 12px;
  text-align: left;
}

.approval-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #ebeef5;
  transition: background-color 0.3s;
}

.approval-table tr:hover {
  background-color: #f5f7fa;
}

.approval-select,
.approval-input {
  width: 100%;
}

.comment-cell {
  border: none;
  padding: 12px;
  text-align: left;
}

.approval-select .el-input__inner {
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  transition: border-color 0.3s;
}

.approval-select .el-input__inner:focus {
  border-color: #409eff;
}

.approval-input .el-input__inner {
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  transition: border-color 0.3s;
}

.approval-input .el-input__inner:focus {
  border-color: #409eff;
}
</style>
