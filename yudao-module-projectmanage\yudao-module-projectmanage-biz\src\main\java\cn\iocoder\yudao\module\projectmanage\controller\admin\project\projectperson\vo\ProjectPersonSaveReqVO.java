package cn.iocoder.yudao.module.projectmanage.controller.admin.project.projectperson.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;
import java.util.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 项目人员新增/修改 Request VO")
@Data
public class ProjectPersonSaveReqVO {
    @Schema(description = "记录id", example = "26665")
    private Long id;

    @Schema(description = "项目组id", example = "15541")
    private Long projectGroupId;

    @Schema(description = "项目组编号")
    private String projectGroupCode;

    @Schema(description = "项目组名称", example = "李四")
    private String projectGroupName;

    @Schema(description = "项目id", example = "3773")
    private Long projectId;

    @Schema(description = "项目编号")
    private String projectCode;

    @Schema(description = "项目名称", example = "芋艿")
    private String projectName;

    @Schema(description = "姓名", example = "张三")
    private String name;

    @Schema(description = "部门")
    private String department;

    @Schema(description = "岗位")
    private String occupation;

    @Schema(description = "状态", example = "1")
    private String status;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "系统名")
    private  String systemName;

    @Schema(description = "系统ID")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String systemId;

    @Schema(description = "比例")
    private String percentage;

    @Schema(description = "时间")
    private String time;

    @Schema(description = "经理是否确认")
    private String managerConfirm;

    @Schema(description = "驳回原因")
    private String rejectReason;

    @Schema(description = "经理驳回时间")
    private LocalDateTime managerRejectTime;

    @Schema(description = "经理操作时间")
    private LocalDateTime managerOperateTime;


    @Schema(description = "部门id")
    private Integer deptId;

}
