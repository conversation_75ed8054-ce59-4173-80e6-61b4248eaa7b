package cn.iocoder.yudao.module.projectmanage.dal.mysql.projectgrop;

import java.math.BigDecimal;
import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.projectmanage.controller.admin.fee.vo.FeeSaveReqVO;
import cn.iocoder.yudao.module.projectmanage.controller.admin.project.vo.ProjectSaveReqVO;
import cn.iocoder.yudao.module.projectmanage.controller.admin.project.vo.ProjectWithFeesRespVO;
import cn.iocoder.yudao.module.projectmanage.dal.dataobject.project.ProjectDO;
import cn.iocoder.yudao.module.projectmanage.dal.dataobject.projectgrop.ProjectGropDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.projectmanage.controller.admin.projectgrop.vo.*;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 项目组 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProjectGropMapper extends BaseMapperX<ProjectGropDO> {

    default PageResult<ProjectGropDO> selectPage(ProjectGropPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ProjectGropDO>()
                .eqIfPresent(ProjectGropDO::getId, reqVO.getId())
                .likeIfPresent(ProjectGropDO::getProjectManager, reqVO.getProjectManager())
                .likeIfPresent(ProjectGropDO::getProjectGropNumber, reqVO.getProjectGropNumber())
                .likeIfPresent(ProjectGropDO::getProjectGropName, reqVO.getProjectGropName())
                .likeIfPresent(ProjectGropDO::getPercentage, reqVO.getPercentage())
                .likeIfPresent(ProjectGropDO::getApproved, reqVO.getApproved())
                .eqIfPresent(ProjectGropDO::getReceivable, reqVO.getReceivable())
                .eqIfPresent(ProjectGropDO::getType, reqVO.getType())
                .eqIfPresent(ProjectGropDO::getReceivedAmount, reqVO.getReceivedAmount())
                .eqIfPresent(ProjectGropDO::getPayable, reqVO.getPayable())
                .eqIfPresent(ProjectGropDO::getPaidAmount, reqVO.getPaidAmount())
                .eqIfPresent(ProjectGropDO::getProfit, reqVO.getProfit())
                .eqIfPresent(ProjectGropDO::getRemarks, reqVO.getRemarks())
                .betweenIfPresent(ProjectGropDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(ProjectGropDO::getAmountScale,  reqVO.getAmountScale())
                .apply(reqVO.getContractHandler() != null,
                        "EXISTS (SELECT 1 FROM projectmanage_contract c WHERE c.project_group_code = project_grop_number AND c.handler = {0})",
                        reqVO.getContractHandler())
                // 添加项目成员搜索条件
                .apply(reqVO.getProjectMemberName() != null && !reqVO.getProjectMemberName().trim().isEmpty(),
                        "EXISTS (SELECT 1 FROM projectmanage_project_person pp WHERE pp.project_group_id = id AND pp.name LIKE CONCAT('%', {0}, '%') AND pp.deleted = 0)",
                        reqVO.getProjectMemberName())
                .orderByDesc(ProjectGropDO::getProjectGropNumber));
    }

    default PageResult<ProjectGropDO> selectPageByManagerIsNullOrEmpty(ProjectGropPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ProjectGropDO>()
                .eqIfPresent(ProjectGropDO::getId, reqVO.getId())
                .likeIfPresent(ProjectGropDO::getProjectGropNumber, reqVO.getProjectGropNumber())
                .likeIfPresent(ProjectGropDO::getProjectGropName, reqVO.getProjectGropName())
                .likeIfPresent(ProjectGropDO::getPercentage, reqVO.getPercentage())
                .likeIfPresent(ProjectGropDO::getApproved, reqVO.getApproved())
                .eqIfPresent(ProjectGropDO::getReceivable, reqVO.getReceivable())
                .eqIfPresent(ProjectGropDO::getType, reqVO.getType())
                .eqIfPresent(ProjectGropDO::getReceivedAmount, reqVO.getReceivedAmount())
                .eqIfPresent(ProjectGropDO::getPayable, reqVO.getPayable())
                .eqIfPresent(ProjectGropDO::getPaidAmount, reqVO.getPaidAmount())
                .eqIfPresent(ProjectGropDO::getProfit, reqVO.getProfit())
                .eqIfPresent(ProjectGropDO::getRemarks, reqVO.getRemarks())
                .betweenIfPresent(ProjectGropDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(ProjectGropDO::getAmountScale,  reqVO.getAmountScale())
                .apply(reqVO.getContractHandler() != null,
                        "EXISTS (SELECT 1 FROM projectmanage_contract c WHERE c.project_group_code = project_grop_number AND c.handler = {0})",
                        reqVO.getContractHandler())
                // 添加项目成员搜索条件
                .apply(reqVO.getProjectMemberName() != null && !reqVO.getProjectMemberName().trim().isEmpty(),
                        "EXISTS (SELECT 1 FROM projectmanage_project_person pp WHERE pp.project_group_id = id AND pp.name LIKE CONCAT('%', {0}, '%') AND pp.deleted = 0)",
                        reqVO.getProjectMemberName())
                .and(wrapper -> wrapper
                        .isNull(ProjectGropDO::getProjectManager)
                        .or()
                        .eq(ProjectGropDO::getProjectManager, ""))
                .orderByDesc(ProjectGropDO::getProjectGropNumber));
    }

    /**
     * 根据用户权限查询项目组分页
     *
     * @param params 包含reqVO(请求参数)、loginUserName(用户名)和offset(偏移量)
     * @return 项目组列表
     */
    List<ProjectGropDO> selectPageByPermission(Map<String, Object> params);

    List<ProjectGropDO> selectPageByPermissionAndManagerIsNull(Map<String, Object> params);

    /**
     * 根据用户权限计算项目组总数
     *
     * @param params 包含reqVO(请求参数)和loginUserName(用户名)
     * @return 项目组总数
     */
    Long selectCountByPermission(Map<String, Object> params);

    Long selectCountByPermissionAndManagerIsNull(Map<String, Object> params);
    /**
     * 检查用户是否在任何项目组的manager字段中
     *
     * @param loginUserName 登录用户名
     * @return 匹配的记录数
     */
    @Select("SELECT COUNT(*) FROM projectmanage_project_grop WHERE project_manager = #{loginUserName} AND deleted = 0")
    Integer countProjectGropByManager(@Param("loginUserName") String loginUserName);

    /**
     * 检查用户是否在项目人员表中
     *
     * @param loginUserName 登录用户名
     * @return 匹配的记录数
     */
    @Select("SELECT COUNT(*) FROM projectmanage_project_person WHERE name = #{loginUserName} AND deleted = 0")
    Integer countProjectPersonByName(@Param("loginUserName") String loginUserName);

    //检查用户是否为项目负责人
    @Select("SELECT COUNT(*) FROM projectmanage_project WHERE project_manager_name = #{loginUserName} AND deleted = 0")
    Integer countProjectyManager(@Param("loginUserName") String loginUserName);

    List<ProjectSaveReqVO> selectProjectsByGroupCode(String projectGroupCode);

    List<FeeSaveReqVO> selectFeesByGroupCode(String projectGroupCode);

    List<FeeSaveReqVO> selectFeesByProjectCode(String projectCode);

    @Select("SELECT * FROM projectmanage_fee WHERE contract_id = #{contractId}")
    List<FeeSaveReqVO> selectFeeByContractId(@Param("contractId") String contractId);


//    @Select("SELECT * FROM projectmanage_project_grop WHERE project_grop_number = #{projectGropNumber} AND deleted = 0 LIMIT 1")
    ProjectGropDO selectByProjectGropNumber(@Param("projectGropNumber") String projectGropNumber);

    List<ProjectWithFeesRespVO> selectReceivableProjects(String projectGropNumber);

    List<ProjectWithFeesRespVO> selectPayableProjects(String projectGropNumber);

    /**
     * 获取项目组的合同金额统计
     *
     * @param projectGropNumber 项目组编号
     * @return 收付款金额统计
     */
    Map<String, BigDecimal> selectContractAmounts(String projectGropNumber);
    Map<String, BigDecimal> selectProjectAmounts(String projectGropNumber);

    String getMaxProjectGropNumberByYear(@Param("yearPrefix") String yearPrefix);

    /**
     * 获取项目组下的项目和系统列表
     *
     * @param projectGropId 项目组ID
     * @return 项目和系统列表
     */
    @Select("SELECT " +
            "projectmanage_project.id as project_id, " +
            "projectmanage_project.project_name, " +
            "projectmanage_project.pay_reci_relation, " +
            "projectmanage_system.id as system_id, " +
            "projectmanage_system.system_name " +
            "FROM " +
            "projectmanage_project_grop, " +
            "projectmanage_project, " +
            "projectmanage_system " +
            "WHERE " +
            "projectmanage_project_grop.id=#{projectGropId} " +
            "and projectmanage_system.project_group_id=projectmanage_project_grop.id " +
            "and projectmanage_system.project_id = projectmanage_project.id")
    List<Map<String, Object>> selectProjectSystemList(@Param("projectGropId") Long projectGropId);

    /**
     * 根据用户ID检查是否为项目人员角色
     *
     * @param userId 用户ID
     * @return 角色名称列表
     */
    @Select("SELECT r.name FROM system_role r " +
            "INNER JOIN system_user_role ur ON r.id = ur.role_id " +
            "WHERE ur.user_id = #{userId} AND r.deleted = 0 AND ur.deleted = 0")
    List<String> selectUserRoleNamesByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID获取角色标识列表
     *
     * @param userId 用户ID
     * @return 角色标识列表
     */
    @Select("SELECT r.code FROM system_role r " +
            "INNER JOIN system_user_role ur ON r.id = ur.role_id " +
            "WHERE ur.user_id = #{userId} AND r.deleted = 0 AND ur.deleted = 0")
    List<String> selectUserRoleCodesByUserId(@Param("userId") Long userId);

    /**
     * 按部门查询项目组
     *
     * @param params 包含department(部门名称)、pageSize(每页大小)和offset(偏移量)
     * @return 项目组列表
     */
    List<ProjectGropDO> selectPageByDepartment(Map<String, Object> params);

    /**
     * 按部门统计项目组数量
     *
     * @param department 部门名称
     * @return 项目组数量
     */

    Long selectCountByDepartment(@Param("department") String department);

    /**
     * 按照年份显示项目组内容
     *
     * @return 对应年份的项目组记录
     */
//    List<ProjectGropDO> selectListByYear(@Param("year") String year);

    @Select("select * from projectmanage_project_grop WHERE substring(project_grop_number, 2, 4) = #{year} and deleted=0 limit #{pageSize} offset #{offset}")
    List<ProjectGropDO> selectGropListByYear(Map<String, Object> params);

    @Select("select count(distinct id) from projectmanage_project_grop WHERE substring(project_grop_number, 2, 4) = #{year} and deleted=0")
    Integer selectCountByYear(String year);

    ProjectGropDO selectTypeByGroupId(@Param("id") Long id);


}
