<template>
  <div id="project-members" class="section-block">
<!--    <h2>{{ title }}</h2>-->
    <h2>项目成员</h2>
    <el-button
      v-if="hasEditPermission"
      @click="handleAddMember"
      type="primary"
    >
      新增
    </el-button>
    <el-table :data="membersData"  fit highlight-current-row v-loading="loading"   :default-sort="defaultSort" >
      <el-table-column prop="name" label="姓名" align="center" min-width="80" />
      <el-table-column
        prop="time" label="参与时间" align="center" min-width="80" sortable
        :filters="timeFilters"
        :filter-method="filterByTime"
        :filtered-value="filteredTime"
      />
      <el-table-column prop="percentage" label="参与比例" align="center" min-width="80" >
        <template #default="{ row }">
          {{ row.percentage != null && row.percentage !== '' ? row.percentage + '%' : '' }}
        </template>
      </el-table-column>
      <el-table-column label="是否分配完成" align="center" min-width="100">
        <template #default="{ row }">
          <el-tag :type="isMonthAllocated(row.time, row.projectId) ? 'success' : 'danger'">
            {{ isMonthAllocated(row.time, row.projectId) ? '已分配完成' : '未分配完成' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="projectName" label="所属项目" align="center" min-width="100" />
<!--      <el-table-column prop="systemName" label="所属系统" align="center" min-width="100"    v-if="userDeptId !== 203"/>-->
      <el-table-column prop="department" label="部门" align="center" min-width="80" />
             <el-table-column label="审批状态" align="center" min-width="120">
         <template #default="{ row }">
           <el-tag 
             :type="getApprovalStatusType(row.managerConfirm, row)" 
             effect="plain"
           >
             {{ getApprovalStatusText(row.managerConfirm, row) }}
           </el-tag>
         </template>
       </el-table-column>
      <!-- 新增驳回信息列 -->
      <el-table-column label="驳回信息" align="center" min-width="120">
        <template #default="{ row }">
          <div v-if="row.rejectReason">
            <el-popover placement="top-start" width="300" trigger="hover">
              <template #default>
                <div>
                  <p><strong>驳回原因：</strong>{{ row.rejectReason }}</p>
                  <p v-if="row.managerRejectTime"><strong>驳回时间：</strong>{{ formatDateTime(row.managerRejectTime) }}</p>
                </div>
              </template>
              <template #reference>
                <el-tag type="danger" size="small" style="cursor: pointer;">
                  查看详情
                </el-tag>
              </template>
            </el-popover>
          </div>
          <span v-else class="text-muted">-</span>
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注" align="center" min-width="80" />
      <el-table-column v-if="hasEditPermission" label="操作" width="280" align="center" fixed="right">
        <template #default="scope">
          <el-button size="small" type="primary" @click="handleEditMember(scope.row)">编辑</el-button>
                                <!-- 重新提交审批按钮 -->
           <el-button 
             v-if="canResubmit(scope.row)"
             size="small" 
             type="warning" 
             @click="handleResubmit(scope.row)"
           >
             重新提交
           </el-button>
          <el-popconfirm
            title="确认删除该记录吗？"
            @confirm="handleDeleteMember(scope.row.id!)"
          >
            <template #reference>
              <el-button size="small" type="danger">删除</el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
<!--    <ProjectPersonForm ref="personFormRef" @success="fetchProjectMembers" />-->

    <!-- 添加/编辑记录对话框 -->
    <el-dialog v-model="dialogVisible" :title="formData.id ? '编辑成员记录' : '添加成员记录'" width="70%">
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px">
<!--          <el-form-item label="项目组编号" prop="projectGroupCode">-->
<!--            <el-input v-model="formData.projectGroupCode" placeholder="自动生成" readonly/>-->
<!--          </el-form-item>-->
<!--          <el-form-item label="项目编号" prop="projectCode">-->
<!--            <el-input v-model="formData.projectCode" placeholder="自动生成" readonly/>-->
<!--          </el-form-item>-->
        <el-row :gutter="15">
          <el-col :span="8">
            <el-form-item label="项目组" prop="projectGropName">
              <el-input
                v-model="formData.projectGroupName"
                readonly
                style="width: 280px"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="项目" prop="projectId">
              <el-select
                v-model="formData.projectName"
                placeholder="请选择项目"
                style="width: 280px"
                @change="handleProjectChange"
                clearable
              >
                <el-option
                  v-for="item in projectList.filter(p => p.payReciRelation === '收')"
                  :key="item.id"
                  :label="item.projectName + (item.payReciRelation === '收' ? '(上)' : '(下)')"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="系统名" v-if="userDeptId !== 203">

              <el-select
                v-model="formData.systemName"
                placeholder="请选择系统"
                style="width: 280px"
                @change="handleSystemChange"
                clearable
              >
                <el-option
                  v-for="item in systemNameList"
                  :key="item.systemName"
                  :label="item.systemName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
  <el-form-item label="姓名" prop="name">
<!--    <el-input v-model="formData.name" placeholder="请输入姓名"/>-->
    <el-select v-model="formData.name" placeholder="请选择姓名" clearable  filterable   >
      <el-option
        v-for="dict in getFilteredDictOptions()"
        :key="dict.value"
        :label="dict.label"
        :value="dict.value"
      />
    </el-select>
  </el-form-item>
              </el-col>
<!--        <el-form-item label="系统名称id" prop="systemName">-->
<!--          <el-input v-model="formData.systemId" placeholder="请输入系统名称" />-->
<!--        </el-form-item>-->
          <el-col :span="12">
  <el-form-item label="部门" prop="department">
    <el-select v-model="formData.department" placeholder="请选择部门" clearable   >
      <el-option
        v-for="dict in getStrDictOptions(DICT_TYPE.MANAGING_DEPARTMENT)"
        :key="dict.value"
        :label="dict.label"
        :value="dict.value"
      />
    </el-select>
  </el-form-item>
            </el-col>
        </el-row>
<!--  <el-form-item label="岗位" prop="occupation">-->
<!--    <el-input v-model="formData.occupation" placeholder="请输入岗位"/>-->
<!--  </el-form-item>-->
<!--  <el-form-item label="状态" prop="status" disabled>-->
<!--    <el-select v-model="formData.status" placeholder="请选择状态" clearable>-->
<!--      <el-option-->
<!--        v-for="dict in getStrDictOptions(DICT_TYPE.PROJECT_PERSON_STATUS)"-->
<!--        :key="dict.value"-->
<!--        :label="dict.label"-->
<!--        :value="dict.value"-->
<!--      />-->
<!--    </el-select>-->
<!--  </el-form-item>-->
        <el-row>
          <el-col :span="12">
        <el-form-item label="参与时间" prop="time">
          <el-date-picker
            v-model="formData.time"
            type="month"
            placeholder="请选择参与时间"
            value-format="YYYY-MM"
            style="width: 538px"

          />
        </el-form-item>
          </el-col>
        <el-form-item label="参与比例" prop="percentage">
    <el-input v-model="formData.percentage" placeholder="请输入参与比例"    @input="handleInput"
              type="number"   style="width: 538px"   >
      <template #append>%</template>
    </el-input>
        </el-form-item>
        </el-row>
  <el-form-item label="备注" prop="remark">
    <el-input v-model="formData.remark" type="textarea" placeholder="请输入备注"    />
  </el-form-item>

      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="debouncedSubmitForm">保存</el-button>
      </template>
    </el-dialog>

    <!-- 重新提交审批对话框 -->
    <el-dialog v-model="resubmitDialogVisible" title="重新提交审批" width="50%">
      <el-form ref="resubmitFormRef" :model="resubmitFormData" :rules="resubmitFormRules" label-width="120px">
        <el-form-item label="成员姓名">
          <el-input v-model="resubmitFormData.name" readonly />
        </el-form-item>
        <el-form-item label="项目名称">
          <el-input v-model="resubmitFormData.projectName" readonly />
        </el-form-item>
        <el-form-item label="参与时间">
          <el-input v-model="resubmitFormData.time" readonly />
        </el-form-item>
        <el-form-item label="参与比例">
          <el-input v-model="resubmitFormData.percentage" readonly>
            <template #append>%</template>
          </el-input>
        </el-form-item>
        <el-form-item label="修改说明" prop="resubmitRemark">
          <el-input 
            v-model="resubmitFormData.resubmitRemark" 
            type="textarea" 
            :rows="4"
            placeholder="请说明重新提交的原因或修改内容..."
          />
        </el-form-item>
        <!-- 显示上次驳回信息 -->
        <el-form-item label="上次驳回原因" v-if="resubmitFormData.lastRejectReason">
          <el-input 
            v-model="resubmitFormData.lastRejectReason" 
            type="textarea" 
            :rows="2"
            readonly
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="resubmitDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmResubmit">确认提交</el-button>
      </template>
    </el-dialog>


  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, defineProps, defineEmits, onBeforeUnmount, computed } from 'vue'
import { ProjectPersonApi, ProjectPersonVO } from '@/api/projectmanage/projectperson'
import emitter from '@/utils/eventBus'
// import ProjectPersonForm from '@/views/projectmanage/project/projectperson/ProjectPersonForm.vue'
import {ElMessage, ElOption, ElSelect} from 'element-plus'
import {DICT_TYPE, getStrDictOptions} from "@/utils/dict";
import { ProjectGropApi } from '@/api/projectmanage/projectgrop/index'
import { ProjectApi } from '@/api/projectmanage/project/index'
import {SystemMessageApi} from "@/api/projectmanage/system";
import { useUserStoreWithOut } from '@/store/modules/user'
import { checkRole } from '@/utils/permission'
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'
import {AddressBookApi} from "@/api/projectmanage/addressbook";
import { debounce } from 'lodash-es';

// 初始化用户相关工具
const { wsCache } = useCache()
const userStore = useUserStoreWithOut()

// 定义props接收项目ID
const props = defineProps({
  id: {
    type: [String, Number],
    required: true
  },
  projectGroupCode: {
    type: String,
    required: true
  },
  projectGroupName: {
    type: String,
    default: ''
  },
  projectCode: {
    type: String,
    required: true
  },
  projectName: {
    type: String,
    required: true
  },
  projectGroupId: {
    type: [String, Number],
    required: true
  },
  projectGroupManager: {
    type: String,
    required: false,
    default: ''
  },
  projectManagers: {
    type: Array as () => Array<{ projectManagerName: string }>,
    default: () => []
  }
})

// 权限控制 - 添加编辑权限判断
const hasEditPermission = computed(() => {
  // 1. 检查是否是项目组经理
  const userInfo = wsCache.get(CACHE_KEY.USER);
  const currentUsername = userInfo?.user?.nickname || '';
  const isProjectGroupManager = currentUsername && props.projectGroupManager === currentUsername;
  // 2. 检查是否是超级管理员
  const isAdmin = currentUsername === "超级管理员";
  // 3. 检查是否是项目负责人
  const isProjectManagerInList = props.projectManagers && props.projectManagers.some(
    manager => manager.projectManagerName === currentUsername
  );

  return isAdmin || isProjectGroupManager || isProjectManagerInList;
})

const filteredTime = ref<string[]>([]);
// 页面标题
const title = ref('项目成员')
// 加载状态
const loading = ref(false)
// 项目成员数据
const membersData = ref<ProjectPersonVO[]>([])

const message = useMessage() // 消息弹窗

const personFormRef = ref()

const formRef = ref()

const dialogVisible = ref(false)

// 重新提交审批对话框相关
const resubmitDialogVisible = ref(false)
const resubmitFormRef = ref()
const resubmitFormData = reactive({
  id: undefined as number | undefined,
  name: '',
  projectName: '',
  time: '',
  percentage: '',
  resubmitRemark: '',
  lastRejectReason: '',
  batchProjectId: undefined as number | string | undefined, // 批量操作的项目ID
  batchTime: '' // 批量操作的时间
})

const resubmitFormRules = reactive({
  resubmitRemark: [{ required: true, message: '请填写重新提交说明', trigger: 'blur' }]
})

// 添加项目列表引用
const projectList = ref<ProjectVO[]>([]);

const systemNameList= ref<SystemItem[]>([]); // 将类型改为对象数组

// 定义系统列表项接口
interface SystemItem {
  id: number;
  projectGroupId: number;
  projectId: number;
  systemName: string;
  proceeding?: string;
  remark?: string;
  deleted?: boolean;
}

// 添加项目接口定义
interface ProjectVO {
  id: number;
  projectName: string;
  projectCode: string;
  [key: string]: any;
}

const formData = reactive({
  id: undefined as number | undefined,
  projectGroupId: undefined as number | undefined,
  projectId: undefined as number | undefined,
  systemId: undefined as number | undefined,
  projectGroupCode: '',
  projectGroupName: '',
  projectCode: '',
  projectName: '',
  name: '',
  department: '',
  occupation: '',
  status: '',
  remark: '',
  systemName: '',
  percentage: '',
  time: '',
  managerConfirm: '未确认', // 新增记录默认为未确认状态
})

const formRules = reactive({
  name: [{ required: true, message: '姓名不能为空' }],
  department: [{ required: true, message: '部门不能为空' }],
  percentage: [{ required: true, message: '参与比例不能为空' }],
  time: [{ required: true, message: '参与时间不能为空' }]
})

const defaultSort = ref({
  prop: 'time',
  order: 'descending' as 'descending' | 'ascending'
})


// 根据系统ID获取系统名称的辅助函数
const getSystemNameById = (systemId: number | null): string => {
  if (!systemId) return '';

  const system = systemNameList.value.find(item => item.id === systemId);
  return system ? system.systemName : '';
}

// 获取项目列表
const getProjectList = async () => {
  try {
    if (!props.projectGroupId) {
      console.error('项目组ID无效');
      return;
    }

    // 如果有projectGroupCode，则通过projectGroupCode获取项目列表
    // 如果没有，则通过projectGroupId获取项目组信息，再获取项目列表
    let projectGroupCode = '';
    if (props.projectGroupCode) {
      projectGroupCode = props.projectGroupCode.toString();
    } else {
      // 先获取项目组详情
      const projectGroupInfo = await ProjectGropApi.getProjectGrop(Number(props.projectGroupId));
      if (projectGroupInfo && projectGroupInfo.projectGropNumber) {
        projectGroupCode = projectGroupInfo.projectGropNumber;
      }
    }

    if (!projectGroupCode) {
      console.error('无法获取项目组编号');
      return;
    }

    console.log('通过项目组编号获取项目列表:', projectGroupCode);
    const res = await ProjectApi.getProjectByGroupCode(projectGroupCode);

    if (Array.isArray(res)) {
      projectList.value = res;
      console.log('获取项目列表成功', projectList.value);
    } else {
      console.error('获取项目列表返回格式错误');
      projectList.value = [];
    }
  } catch (error) {
    console.error('获取项目列表失败', error);
    ElMessage.error('获取项目列表失败');
    projectList.value = [];
  }
}

// 项目选择变更处理函数
const handleProjectChange = async (projectId: number | string) => {
  console.log('项目选择已变更:', projectId);
  formData.projectId = projectId ? Number(projectId) : null;

  // 获取项目名称
  if (projectId) {
    const selectedProject = projectList.value.find(item => item.id === Number(projectId));
    if (selectedProject) {
      formData.projectName = selectedProject.projectName || '';
      formData.projectCode = selectedProject.projectCode || '';
    }
  } else {
    formData.projectName = '';
    formData.projectCode = '';
  }

  // 清空系统名并重新加载
  formData.systemName = '';
  formData.systemId = null;
  await getSystemByProject(projectId);
}

// 系统选择变更处理函数
const handleSystemChange = (systemId: number) => {
  console.log('系统选择已变更:', systemId);

  if (systemId) {
    // 根据ID查找系统名称
    const selectedSystem = systemNameList.value.find(item => item.id === systemId);

    if (selectedSystem) {
      // 同时更新系统名称和ID
      formData.systemName = selectedSystem.systemName;
      formData.systemId = systemId;
      console.log(`已关联系统: ${selectedSystem.systemName}，ID: ${systemId}`);
    }
  } else {
    // 清空系统时，同时清空系统名称和ID
    formData.systemName = '';
    formData.systemId = null;
    console.log('系统已清空');
  }
}

const getSystemByProject  = async (projectId?: number | string) => {
  try {
    // 如果传入了项目ID，优先使用传入的值，否则使用props中的值
    const targetProjectId = projectId !== undefined ? projectId : props.id;
    const targetGroupId = props.projectGroupId;

    console.log('获取系统列表，参数:', { projectGroupId: targetGroupId, projectId: targetProjectId });

    const res = await SystemMessageApi.getSystemByProject(targetGroupId, targetProjectId);
    console.log('获取系统条目列表成功', res);

    // 确保结果是数组
    if (Array.isArray(res)) {
      systemNameList.value = res;
    } else {
      console.error('获取系统列表返回格式错误', res);
      systemNameList.value = [];
    }
  } catch (error) {
    console.error('获取系统列表失败:', error);
    ElMessage.error('获取系统列表失败');
    systemNameList.value = [];
  }
}

// 更新成员数据处理函数
const handleUpdateMembers = (data: { title: string, data: ProjectPersonVO[] }) => {
  console.log('接收到成员数据更新:', data)
  title.value = data.title
  membersData.value = data.data
}

// 处理新增成员
const handleAddMember = async () => {

  // 获取当前日期
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();
  const currentMonth = (currentDate.getMonth() + 1).toString().padStart(2, '0');
  const currentMonthStr = `${currentYear}-${currentMonth}`;


  let projectId = props.id;

  // 如果 props.id 不存在，尝试使用 lastSelectedInfo 中的 projectId
  if (!projectId && lastSelectedInfo.value.projectId) {
    projectId = String(lastSelectedInfo.value.projectId);
  }

  if (!projectId) {
    ElMessage.warning('请先选择上家项目')
    return
  }
  console.log('添加成员记录', lastSelectedInfo.value);

  await getProjectList()

  // 检查当前 projectId 是否为上家项目
  const selectedProject = projectList.value.find(p => String(p.id) === projectId)

  if (!selectedProject || selectedProject.payReciRelation !== '收') {
    ElMessage.warning('仅允许选择上家项目')
    return
  }

  // 重置表单验证状态
  if (formRef.value) {
    formRef.value.resetFields()
  }

  // 加载项目组和项目列表
  // getSystemByProject()
 await  getProjectList()

  formData.id = undefined
  formData.projectGroupId = props.projectGroupId
  formData.projectId = props.id
  formData.projectGroupCode = props.projectGroupCode
  formData.projectGroupName = props.projectGroupName
  // formData.projectCode = props.projectCode || ''
  formData.projectCode =  ''
  formData.projectName = props.projectName
  formData.systemId = undefined
  formData.name = ''
  formData.department = ''
  formData.occupation = ''
  formData.status = ''
  formData.remark = ''
  formData.systemName = ''
  formData.percentage = ''
  formData.time = currentMonthStr;


  // 自动填充项目名和系统名（如果有）
  if (lastSelectedInfo.value && lastSelectedInfo.value.projectName) {
    console.log('设置项目名:', lastSelectedInfo.value.projectName);
    formData.projectName = lastSelectedInfo.value.projectName

    // 如果有项目ID，设置项目ID
    if (lastSelectedInfo.value.projectId) {
      const projectIdStr = String(lastSelectedInfo.value.projectId)
      formData.projectId = projectIdStr
      console.log('设置项目ID:', projectIdStr);

      // 查找并设置项目编号
      const selectedProject = projectList.value.find(p => String(p.id) === projectIdStr)
      if (selectedProject) {
        formData.projectCode = selectedProject.projectNumber || ''
        console.log('设置项目编号:', selectedProject.projectNumber);
      }

      // 加载系统列表
      await getSystemByProject(projectIdStr);
    }
  }

  // 自动填充系统名（如果有）
  if (lastSelectedInfo.value && lastSelectedInfo.value.systemName) {
    console.log('设置系统名:', lastSelectedInfo.value.systemName);
    formData.systemName = lastSelectedInfo.value.systemName

    // 设置系统ID（如果有）
    if (lastSelectedInfo.value.systemId) {
      const systemIdStr = String(lastSelectedInfo.value.systemId);
      formData.systemId = systemIdStr;
      console.log('设置系统ID:', systemIdStr);
    }
  }


  dialogVisible.value = true

}

const handleEditMember = async (item: ProjectPersonVO) => {
  await getSystemByProject();
  await getProjectList()
  formData.id = item.id
  formData.projectGroupId = item.projectGroupId
  formData.projectId = item.projectId
  formData.projectGroupCode = item.projectGroupCode
  formData.projectGroupName = item.projectGroupName
  formData.projectCode = item.projectCode
  formData.projectName = item.projectName
  formData.name = item.name
  formData.department = item.department
  formData.occupation = item.occupation
  formData.status = item.status
  formData.remark = item.remark
  formData.percentage = item.percentage
  formData.time = item.time
  formData.managerConfirm = item.managerConfirm || '未确认' // 设置审批状态

  // 处理系统信息 - 保存 systemId 作为主要引用，系统名称用于显示
  formData.systemId = item.systemId ? Number(item.systemId) : null;
  formData.systemName = item.systemName || '';

  if (formData.systemId && systemNameList.value.length > 0) {
    const systemName = getSystemNameById(Number(formData.systemId));
    if (systemName && systemName !== formData.systemName) {
      console.log(`系统名称与ID不匹配，更新系统名称: ${formData.systemName} -> ${systemName}`);
      formData.systemName = systemName;
    }
  }
  dialogVisible.value = true
}

const handleDeleteMember = async (id:  number) => {
  try {
    await ProjectPersonApi.deleteProjectPerson(id)
    ElMessage.success('删除成功')
    fetchProjectMembers()
  } catch (error) {
    console.error('删除失败', error)
    ElMessage.error('删除失败')
  }
}

// 获取审批状态的显示文本
const getApprovalStatusText = (status: string, row?: any) => {
  switch (status) {
    case '确认':
    case '是': // 兼容旧数据
      return '已确认'
    case '驳回':
      return '已驳回'
    case '否':  // 兼容旧数据 - 根据是否有驳回原因判断
      return (row && row.rejectReason) ? '已驳回' : '待审批'
    case '未确认':
    case null:
    case undefined:
    case '':
      return '待审批'
    default:
      return '待审批'
  }
}

// 获取审批状态的标签类型
const getApprovalStatusType = (status: string, row?: any) => {
  switch (status) {
    case '确认':
    case '是': // 兼容旧数据
      return 'success'
    case '驳回':
      return 'danger'
    case '否':  // 兼容旧数据 - 根据是否有驳回原因判断
      return (row && row.rejectReason) ? 'danger' : 'warning'
    case '未确认':
    case null:
    case undefined:
    case '':
      return 'warning'
    default:
      return 'info'
  }
}

// 判断记录是否可以重新提交审批（按项目+月份分组）
const canResubmit = (row: any) => {
  const projectId = row.projectId
  const time = row.time
  
  if (!projectId || !time) return false
  
  // 检查该项目该月份是否有被驳回的记录
  const hasRejectedRecords = membersData.value.some(item => 
    item.projectId === projectId && 
    item.time === time && 
    (item.managerConfirm === '驳回' || (item.managerConfirm === '否' && item.rejectReason))
  )
  
  if (!hasRejectedRecords) return false
  
  // 只在该项目该月份的第一条记录上显示重新提交按钮
  const sameProjectTimeRecords = membersData.value.filter(item => 
    item.projectId === projectId && item.time === time
  ).sort((a, b) => (a.id || 0) - (b.id || 0))
  
  return sameProjectTimeRecords.length > 0 && sameProjectTimeRecords[0].id === row.id
}

// 检查项目月份占比是否为100%
const checkProjectMonthPercentage = (projectId: number, time: string) => {
  const totalPercentage = membersData.value
    .filter(item => item.projectId === projectId && item.time === time)
    .reduce((sum, item) => sum + (Number(item.percentage) || 0), 0)
  
  return { total: totalPercentage, isValid: totalPercentage === 100 }
}

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return ''
  const date = new Date(dateTime)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 处理重新提交审批（按项目+月份批量操作）
const handleResubmit = (row: ProjectPersonVO) => {
  const projectId = row.projectId
  const time = row.time
  
  if (!projectId || !time) {
    ElMessage.error('项目或时间信息不完整')
    return
  }
  
  // 检查该项目该月份占比是否为100%
  const percentageCheck = checkProjectMonthPercentage(Number(projectId), time)
  if (!percentageCheck.isValid) {
    ElMessage.warning(`该项目${time}月份总占比为${percentageCheck.total}%，需要调整为100%后才能重新提交审批`)
    return
  }
  
  // 获取该项目该月份的所有成员记录
  const projectTimeRecords = membersData.value.filter(item => 
    item.projectId === projectId && item.time === time
  )
  
  if (projectTimeRecords.length === 0) {
    ElMessage.error('未找到相关成员记录')
    return
  }
  
  // 重置表单验证状态
  if (resubmitFormRef.value) {
    resubmitFormRef.value.resetFields()
  }

  // 填充表单数据（显示项目和月份信息）
  resubmitFormData.id = undefined // 不显示具体的记录ID，因为是批量操作
  resubmitFormData.name = `共${projectTimeRecords.length}名成员`
  resubmitFormData.projectName = row.projectName
  resubmitFormData.time = row.time
  resubmitFormData.percentage = '100' // 显示总占比
  resubmitFormData.resubmitRemark = ''
  resubmitFormData.lastRejectReason = row.rejectReason || ''
  
  // 保存项目和时间信息用于提交
  resubmitFormData.batchProjectId = projectId
  resubmitFormData.batchTime = time

  resubmitDialogVisible.value = true
}

// 确认重新提交（批量操作）
const confirmResubmit = async () => {
  try {
    await resubmitFormRef.value.validate()

    const { batchProjectId, batchTime, resubmitRemark } = resubmitFormData
    
    if (!batchProjectId || !batchTime) {
      ElMessage.error('项目或时间信息不完整')
      return
    }

    // 获取该项目该月份的所有成员记录
    const projectTimeRecords = membersData.value.filter(item => 
      item.projectId === batchProjectId && item.time === batchTime
    )
    
    if (projectTimeRecords.length === 0) {
      ElMessage.error('未找到相关成员记录')
      return
    }
    
    // 再次验证占比是否为100%
    const percentageCheck = checkProjectMonthPercentage(Number(batchProjectId), batchTime)
    if (!percentageCheck.isValid) {
      ElMessage.warning(`该项目${batchTime}月份总占比为${percentageCheck.total}%，需要调整为100%后才能重新提交审批`)
      return
    }

    // 批量更新所有相关记录
    const updatePromises = projectTimeRecords.map(record => {
      return ProjectPersonApi.updateProjectPerson({
        ...record,
        remark: resubmitRemark, // 将重新提交说明作为备注
        managerConfirm: '未确认', // 重置为未确认状态
        rejectReason: '', // 清空驳回原因
        managerRejectTime: undefined, // 清空驳回时间
        managerOperateTime: new Date().toISOString() // 更新操作时间
      })
    })

    // 等待所有更新完成
    await Promise.all(updatePromises)
    
    ElMessage.success(`重新提交成功，已更新${projectTimeRecords.length}条记录，等待部门经理审批`)
    resubmitDialogVisible.value = false
    
    // 刷新列表
    await fetchProjectMembers()
    
  } catch (error) {
    console.error('重新提交失败', error)
    ElMessage.error('重新提交失败')
  }
}



const fetchProjectMembers = async () => {
  if (!props.projectGroupId) {
    return
  }
  try {
    loading.value = true
    let res;
    if (lastSelectedInfo.value.systemId) {
      console.log('通过系统ID获取成员:', lastSelectedInfo.value.systemId)
      res = await ProjectPersonApi.getProjectPersonListBySystemId(
        lastSelectedInfo.value.systemId.toString()
      )
    }
    else if (props.id) {
      console.log('通过项目ID获取成员:', props.id, props.projectGroupId)
      res = await ProjectPersonApi.getProjectPersonListByProjectId(
        props.id.toString(),
      )
    } else {
      console.log('通过项目组ID获取成员:', props.projectGroupId)
      res = await ProjectPersonApi.getProjectPersonListByProjectGroupId(
        props.projectGroupId.toString()
      )
    }
    console.log('获取到的项目成员数据:', res)

    // 判断返回的数据是数组还是包含data属性的对象
    const dataArray = Array.isArray(res) ? res : (res?.data || []);

    if (dataArray && dataArray.length > 0) {

      dataArray.sort((a, b) => {
        // 时间降序
        const timeCompare = b.time.localeCompare(a.time);
        if (timeCompare !== 0) return timeCompare;

        // 时间相同时按比例降序
        const percentageA = a.percentage ? Number(a.percentage) : 0;
        const percentageB = b.percentage ? Number(b.percentage) : 0;
        return percentageA - percentageB;
      });

      membersData.value = dataArray.map(item => {
        console.log('处理单条项目成员数据:', item)

        return {
          id: item.id,
          name: item.name,
          department: item.department,
          occupation: item.occupation,
          status: item.status,
          remark: item.remark,
          projectName: item.projectName,
          systemName: item.systemName,
          systemId: item.systemId,
          projectId: item.projectId,
          projectCode: item.projectCode,
          projectGroupId: item.projectGroupId,
          projectGroupCode: item.projectGroupCode,
          projectGroupName: item.projectGroupName,
          percentage: item.percentage,
          time: item.time,
          managerConfirm: item.managerConfirm,
          rejectReason: item.rejectReason,
          managerRejectTime: item.managerRejectTime
        }
      })
      console.log('处理后的项目成员数据:', membersData.value)
    } else {
      console.log('没有找到项目成员数据')
      membersData.value = [];
    }
  }
   catch (error) {
    console.error('获取项目成员失败', error)
  } finally {
    loading.value = false
  }
}

// const fetchProjectMembers = async () => {
//   if (!props.projectGroupId) {
//     return
//   }
//
//   try {
//     loading.value = true
//     let res;
//     if (props.id) {
//       console.log('11111111111111111112221111111111111通过ID获取项目成员:', props.id, props.projectGroupId)
//       res = await ProjectPersonApi.getProjectPersonListByProjectId(
//         props.id.toString(),
//         props.projectGroupId.toString())
//     } else {
//       console.log('111111333331111111112221111111111111通过ID获取项目成员:', props.projectGroupId)
//       res = await ProjectPersonApi.getProjectPersonListByProjectId(
//         '',  // 不传项目ID
//         props.projectGroupId.toString()
//       )
//     }
//     console.log('获取到的项目成员数据:', res)
//
//     // 判断返回的数据是数组还是包含data属性的对象
//     const dataArray = Array.isArray(res) ? res : (res?.data || []);
//
//     if (dataArray && dataArray.length > 0) {
//       membersData.value = dataArray.map(item => {
//         console.log('处理单条项目成员数据:', item)
//
//         return {
//           id: item.id,
//           name: item.name,
//           department: item.department,
//           occupation: item.occupation,
//           status: item.status,
//           remark: item.remark,
//           projectName: item.projectName,
//           systemName: item.systemName,
//           systemId: item.systemId,
//           projectId: item.projectId,
//           projectCode: item.projectCode,
//           projectGroupId: item.projectGroupId,
//           projectGroupCode: item.projectGroupCode,
//           projectGroupName: item.projectGroupName,
//         }
//       })
//       console.log('处理后的项目成员数据:', membersData.value)
//     } else {
//       console.log('没有找到项目成员数据')
//       membersData.value = [];
//     }
// } catch (error) {
//     console.error('获取项目成员失败', error)
//   } finally {
//     loading.value = false
//   }
// }

const handleInput = (value: string) => {
  const numValue = Number(value.replace(/[^0-9]/g, ''));
  if (numValue > 100) {
    formData.percentage = '100'; // 限制最大值为 100
  } else if (numValue < 0) {
    formData.percentage = '0'; // 限制最小值为 0
  } else {
    formData.percentage = String(numValue); // 正常赋值
  }
};


// 提交方法
const submitForm = async () => {
  // if (!formData.systemName) {
  //   if (userDeptId.value !== 203) {
  //     ElMessage.warning('请选择系统名称');
  //     return;
  //   }
  // }
  await formRef.value.validate()

  const currentPercentage = formData.percentage ? Number(formData.percentage) : 0;
  const currentMonth = formData.time;
  const currentProjectId = formData.projectId;

  if (currentPercentage > 0 && currentMonth && currentProjectId) {
    // 计算该月份现有比例总和（排除当前编辑项）
    const totalPercentage = membersData.value
      .filter(item =>
        item.time === currentMonth &&
         item.projectId === currentProjectId &&
        item.id !== formData.id // 排除当前编辑项
      )
      .reduce((sum, item) => sum + (Number(item.percentage) || 0), 0);

    // 计算新增/更新后的总比例
    const newTotal = totalPercentage + currentPercentage;

    // 新增校验
    if (newTotal > 100) {
      ElMessage.warning(`当前月份 ${currentMonth} 在项目 ${formData.projectName} 下的总参与比例已达 ${totalPercentage}%，新增后将超出100%`);
      return;
    }
  }

  try {
    if (formData.systemId) {
      // 如果有系统ID，确保系统名称对应正确
      const systemName = getSystemNameById(Number(formData.systemId));
      if (systemName) {
        formData.systemName = systemName;
      }
    } else if (formData.systemName) {
      // 如果只有系统名称没有ID，尝试查找对应的ID
      const system = systemNameList.value.find(item => item.systemName === formData.systemName);
      if (system) {
        formData.systemId = system.id;
      } else {
        // 系统名称无效，清空
        formData.systemName = '';
      }
    }

    const saveData = {
      ...formData,
      projectGroupId: Number(props.projectGroupId),
    }

    if (formData.id) {
      // 更新
      await ProjectPersonApi.updateProjectPerson(saveData)
      ElMessage.success('更新成功')
    } else {
      // 新增
      const res = await ProjectPersonApi.createProjectPerson(saveData)
      console.log('新增通讯录返回数据:', res)
      ElMessage.success('添加成功')
    }
    dialogVisible.value = false
    await fetchProjectMembers() // 刷新列表
  } catch (error) {
    console.error('创建失败', error)
  }
}

const timeFilters = computed(() => {
  // 只保留唯一的时间
  const uniqueTimes = Array.from(new Set(membersData.value.map(item => item.time).filter(Boolean)))as string[];
  uniqueTimes.sort((a, b) => b.localeCompare(a));
  return uniqueTimes.map(time => ({
    text: time,
    value: time
  }));
});

const isMonthAllocated = (month: string, projectId: number | string) => {
  if (!month) return false;

  // 计算该月份的比例总和
  const total = membersData.value
    .filter(item => item.time === month && item.projectId === projectId)
    .reduce((sum, item) => sum + (Number(item.percentage) || 0), 0);

  return total === 100;
};

function filterByTime(value: string, row: any) {
  // value 是选中的 filter value，row 是当前行
  return row.time === value;
}


// 创建防抖后的submitForm函数
const debouncedSubmitForm = debounce(submitForm, 300)

// 定义member-form-info事件数据的接口
interface MemberFormInfo {
  systemName: string;
  projectName: string;
  projectId: string | number;
  systemId: string | number;
}

// 定义临时存储项目名和系统名的变量
const lastSelectedInfo = ref<MemberFormInfo>({
  systemName: '',
  projectName: '',
  projectId: '',
  systemId: ''
})

const userDeptId = computed(() => {
  const userInfo = wsCache.get(CACHE_KEY.USER);
  return userInfo?.user?.deptId || null;
});

console.log('用户部门ID11:', userDeptId.value)

const getFilteredDictOptions = () => {
  const deptIdStr = String(userDeptId.value);

  // 公司领导（197/115）看到所有四个部门字典的合集
  if (deptIdStr === '197' || deptIdStr === '115' || deptIdStr === '201' || deptIdStr === '203' || deptIdStr === '202' || deptIdStr === '199') {
    return [
      ...getStrDictOptions('rjyb').map(item => ({ ...item, department: '软件开发一部' })),
      ...getStrDictOptions('rjeb').map(item => ({ ...item, department: '软件开发二部' })),
      ...getStrDictOptions('xtjc').map(item => ({ ...item, department: '系统集成部' })),
      ...getStrDictOptions('txfw').map(item => ({ ...item, department: '通信服务部' }))
    ];
  }

  // 按部门ID匹配对应字典
  // if (deptIdStr === '199') {        // 软件一部
  //   return getStrDictOptions('rjyb').map(item => ({ ...item, department: '软件开发一部' }));
  // } else if (deptIdStr === '201') { // 软件二部
  //   return getStrDictOptions('rjeb').map(item => ({ ...item, department: '软件开发二部' }));
  // } else if (deptIdStr === '203') { // 系统集成部
  //   return getStrDictOptions('xtjc').map(item => ({ ...item, department: '系统集成部' }));
  // } else if (deptIdStr === '202') { // 通信服务部
  //   return getStrDictOptions('txfw').map(item => ({ ...item, department: '通信服务部' }));
  // }

  return []; // 默认返回空数组
};

// 监听属性变化，重新加载数据
watch(() => [props.projectGroupId, props.id], () => {
  console.log('成员参数变化，重新加载数据', props.projectGroupId, props.id)
  fetchProjectMembers()
}, { immediate: true })

watch(() => lastSelectedInfo.value.systemId, () => {
  console.log('系统ID变化，重新加载成员数据')
  fetchProjectMembers()
}, { immediate: true })

watch(() => formData.name, (newName) => {
  if (!newName) {
    formData.department = ''; // 清空部门
    return;
  }

  const selectedOption = getFilteredDictOptions().find(option => option.value === newName);
  if (selectedOption && selectedOption.department) {
    formData.department = selectedOption.department;
  } else {
    formData.department = '';
  }
});

onMounted(() => {
  // 默认加载项目成员
  fetchProjectMembers()
  getProjectList();
  getSystemByProject();

  // 监听事件总线上的成员数据更新事件
  emitter.on('update-members', handleUpdateMembers)
  console.log('已监听update-members事件', handleUpdateMembers);
  emitter.on('member-form-info', (info: MemberFormInfo) => {
    console.log('收到member-form-info事件:', info);
    if (info) {
      lastSelectedInfo.value = {
        systemName: info.systemName || '',
        projectName: info.projectName || '',
        projectId: info.projectId || '',
        systemId: info.systemId || ''
      };
      console.log('更新lastSelectedInfo:', lastSelectedInfo.value);
    }
  });
})

onBeforeUnmount(() => {
  // 移除事件监听
  emitter.off('update-members', handleUpdateMembers)
  emitter.off('member-form-info');
})
</script>

<style scoped>
.section-block {
  margin-bottom: 40px;
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  scroll-margin-top: 80px; /* 确保锚点定位有足够的边距 */
}

.text-muted {
  color: #909399;
  font-size: 12px;
}

:deep(.el-popover__reference) {
  width: 100%;
}

/* 操作按钮间距 */
.el-table .el-button + .el-button {
  margin-left: 8px;
}

/* 审批状态标签样式 */
.el-tag {
  font-weight: 500;
}

/* 驳回信息弹出框内容样式 */
.el-popover p {
  margin: 8px 0;
  line-height: 1.5;
}

.el-popover p:first-child {
  margin-top: 0;
}

.el-popover p:last-child {
  margin-bottom: 0;
}
</style>
