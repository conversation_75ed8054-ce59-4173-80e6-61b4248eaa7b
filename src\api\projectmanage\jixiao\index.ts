import request from '@/config/axios'

// 绩效考核 VO
export interface JixiaoVO {
  id: number // id
  contractId: number // 合同id
  projectId: number // 项目id
  projectName: string // 项目名称
  projectNature: string // 项目性质
  projectType: string // 项目类别
  projectCoefficient: string // 项目实施系数
  contractWorkday: string // 合同工期
  projectWorkday: string // 年内项目实施时间
  personNum: number // 项目成员人数
  projectManage: string // 项目负责人
  projectPerson: string // 项目人员
  projectProportions: string // 人员参与比例
  seasonWorkday: string // 季度工期
  seasonAllocate: string // 季度奖金分配
  seasonPersonallocate: string // 个人季度分配
  annualWorkday: string // 年度工期
  annualAllocate: string // 年度奖金分配
  annualPersonallocate: string // 个人年度分配
  exWorkday: string // 超期情况
  deductions: string // 扣减
  remarks: string // 备注
  yearMonth: string // 年月
}

// 部门经理绩效表 VO
export interface DepartmentPerformanceVO {
  projectPersonId: number // 项目人员ID
  projectId: number // 项目ID
  contractId: number // 合同ID
  monthPeriod: string // 月期
  projectName: string // 项目名称
  projectNature: string // 项目性质
  managingDepartment: string // 主办部门
  projectType: string // 项目类别
  type: string // 类型
  projectCoefficient: string // 项目系数
  projectManagerName: string // 项目经理
  contractDuration: string // 合同工期
  memberCount: number // 成员数量
  memberName: string // 成员姓名
  personalPercentage: string // 个人占比
}

// 绩效考核 API
export const JixiaoApi = {
  // 查询绩效考核分页
  getJixiaoPage: async (params: any) => {
    return await request.get({ url: `/projectmanage/jixiao/page`, params })
  },

  // 查询绩效考核详情
  getJixiao: async (id: number) => {
    return await request.get({ url: `/projectmanage/jixiao/get?id=` + id })
  },

  // 新增绩效考核
  createJixiao: async (data: JixiaoVO) => {
    return await request.post({ url: `/projectmanage/jixiao/create`, data })
  },

  // 修改绩效考核
  updateJixiao: async (data: JixiaoVO) => {
    return await request.put({ url: `/projectmanage/jixiao/update`, data })
  },

  // 删除绩效考核
  deleteJixiao: async (id: number) => {
    return await request.delete({ url: `/projectmanage/jixiao/delete?id=` + id })
  },

  // 导出绩效考核 Excel
  exportJixiao: async (params) => {
    return await request.download({ url: `/projectmanage/jixiao/export-excel`, params })
  },

  // 获取部门经理绩效表数据
  getDepartmentPerformanceData: async (params?: any) => {
    return await request.get({ url: `/projectmanage/jixiao/department-performance`, params })
  },

  // 确认部门绩效占比
  confirmDepartmentPerformance: async (data: any) => {
    return await request.post({ url: `/projectmanage/jixiao/confirm-department-performance`, data })
  },

  // 驳回部门绩效占比
  rejectDepartmentPerformance: async (data: any) => {
    return await request.post({ url: `/projectmanage/jixiao/reject-department-performance`, data })
  },

  // 撤销驳回部门绩效占比
  revokeRejectDepartmentPerformance: async (data: any) => {
    return await request.post({ url: `/projectmanage/jixiao/revoke-reject-department-performance`, data })
  },
}
