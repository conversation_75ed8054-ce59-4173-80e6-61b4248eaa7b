<template>
  <div id="project-finance" class="section-block">
    <h2>财务情况</h2>
    <el-card>
      <el-descriptions :column="3" border>
        <el-descriptions-item label="合同金额">{{ financeData.contractAmount }}</el-descriptions-item>
        <el-descriptions-item :label="one.paymentRelationship === '收' ? '已收款金额' : '已付款金额'">
          {{ one.paymentRelationship === '收' ? financeData.receivedAmount : financeData.expendAmount }}
        </el-descriptions-item>
        <el-descriptions-item :label="one.paymentRelationship === '收' ? '待收款金额' : '待付款金额'">
          {{ financeData.remainingAmount }}
        </el-descriptions-item>
      </el-descriptions>
      <div style="margin-top: 20px;">
        <h4>{{ one.paymentRelationship === '收' ? '收款计划' : '付款计划' }}</h4>
        <el-table :data="feeList" style="width: 100%" :row-class-name="RowClassName">
          <el-table-column label="序列" width="80" align="center">
            <template #default="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>

          <!-- 款项类型列 -->
          <el-table-column label="款项类型" prop="type" align="center">
            <template #default="scope">
              <el-input v-if="one.paymentRelationship=='付'" v-model="scope.row.type"
                        placeholder="请选择款项类型" readonly/>

              <el-input v-if="one.paymentRelationship=='收'" v-model="scope.row.type"
                        placeholder="请选择款项类型" readonly/>
            </template>
          </el-table-column>

          <!-- 占比列 -->
          <el-table-column label="占比（%）" prop="proportion" align="center">
            <template #default="scope">
              <el-input type="number" max="100" min="1" v-model="scope.row.proportion" readonly
                        @input="proportionChange(scope.row)" @blur="proportionBlur(scope.row)"/>
            </template>
          </el-table-column>

          <!-- 含税金额列 -->
          <el-table-column label="含税金额（元）" prop="amount" align="center">
            <template #default="scope">
              <el-input type="number" v-model="scope.row.amount" readonly/>
            </template>
          </el-table-column>

          <el-table-column label="实际发生额（元）" prop="actualAmount" align="center">
            <template #default="scope">
              <el-input type="number" v-model="scope.row.actualAmount" readonly/>
            </template>
          </el-table-column>

          <!-- 款项说明列 -->
          <el-table-column label="款项说明" prop="remarks" align="center">
            <template #default="scope">
              <el-input v-model="scope.row.remarks" readonly/>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
    <el-card style="margin-top: 20px">
      <h4>部门分配比例</h4>
      <el-table :data="departmentAllocations" border style="width: 100%">
        <el-table-column prop="division" label="部门" align="center">
          <template #default="scope">
            <span>{{ formatDepartmentName(scope.row.division) }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="amount" label="金额" align="center">
          <template #default="scope">
            {{ formatCurrency(scope.row.amount) }}
          </template>
        </el-table-column>

        <el-table-column prop="proportion" label="分配比例" align="center">
          <template #default="scope">
            {{ scope.row.proportion }}%
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, watch, defineEmits, onBeforeUnmount } from 'vue'
import { ProjectApi } from '@/api/projectmanage/project'
import { ContractApi } from "@/api/projectmanage/contract"
import emitter from '@/utils/eventBus';

// 定义props接收项目ID
const props = defineProps({
  id: {
    type: [String, Number],
    required: true
  },
  contractId: {
    type: [String, Number],
    required: true
  }
})

console.log('财务阶段组件接收到的参数：', {
  projectId: props.id,
  contractId: props.contractId,
})

const emit = defineEmits(['update'])

// 财务计划类型
interface FinancePlan {
  stage: string
  amount: string
  planDate: string
  status: string
}

// 财务数据类型
interface FinanceData {
  contractAmount: string
  receivedAmount: string
  expendAmount: string
  remainingAmount: string
  financePlans: FinancePlan[]
}

// 部门分配金额比例表
interface DepartmentAllocation {
  id: number
  division: string
  amount: number
  proportion: number
  contractId: string | number
}

// 新增响应式数据
const departmentAllocations = ref<DepartmentAllocation[]>([])

const feeList = ref([])

// 项目财务数据
const financeData = ref<FinanceData>({
  contractAmount: '',
  receivedAmount: '',
  expendAmount: '',
  remainingAmount: '',
  financePlans: []
})

const one = ref({
  id: undefined,
  contractType: undefined,
  handler: undefined,
  executingDeparment: undefined,
  businessType: undefined,
  startTime: undefined,
  endTime: undefined,
  advanceAmount: undefined,
  status: undefined,
  approvalStatus: undefined,
  projectId: undefined,
  projectName: undefined,
  projectGroupId: undefined,
  projectGroupCode: undefined,
  projectGroupName: undefined,
  contractCode: undefined,
  contractName: undefined,
  jiafangId: undefined,
  jiafangName: undefined,
  workDay: undefined,
  yifangId: undefined,
  yifangName: undefined,
  jiafangSignatory: undefined,
  yifangSignatory: undefined,
  signingTime: undefined,
  content: undefined,
  amount: undefined,
  currency: undefined,
  paymentRelationship: undefined,
  contractYear: undefined,
  isAdvancePayment: undefined,
  advanceCurrency: undefined,
  projectCode: undefined,
  remark: undefined,
  financialClassification: undefined,
  businessContractCode: undefined,
  sourceMethods: undefined,
  warrantyService: undefined,
  isWarranty: undefined,
  warrantyAmount: undefined,
  rate: undefined,
  ratesAndAmounts: [] as { rate: number | undefined, amount: number | undefined }[],
  paymentType: undefined,
  paymentDescription: undefined,
  taxRate: undefined,
  taxAmount: undefined,
  taxExclusiveAmount: undefined,
  mainOrPatch: undefined,
  mainId: undefined,
  patchId: undefined
})

// 格式化部门名称显示
const formatDepartmentName = (department: string) => {
  // 如果是主办部门
  if (department === '主办部门' || department == one.value.executingDeparment) {
    return `主办部门（${one.value.executingDeparment}）`;
  }
  // 如果是协办部门（根据部门名称判断，如果包含括号则认为是协办部门）
  else if (department.includes('（') && department.includes('）')) {
    // 如果已经是"协办部门（XX）"格式，则直接返回
    if (department.startsWith('协办部门')) {
      return department;
    }
    // 否则格式化为协办部门格式
    return `协办部门（${department.replace('协办部门（', '').replace('）', '')}）`;
  }
  // 其他情况，可能也是协办部门
  else {
    return `协办部门（${department}）`;
  }
}

// 获取项目金额（处理补合同逻辑）
const getProjectAmount = async (projectId: number) => {
  try {
    const projectInfo = await ProjectApi.getProject(projectId)
    const contractInfo = await ContractApi.getContract(projectInfo.contractId)

    // 处理补合同逻辑
    if (contractInfo.patchId) {
      const contractpatchInfo = await ContractApi.getContract(contractInfo.patchId)
      return parseFloat(contractpatchInfo.amount || '0')
    }
    return parseFloat(projectInfo.totalAmount || '0')
  } catch (error) {
    console.error('获取项目金额失败', error)
    return 0
  }
}

const fetchProjectFinance = async (projectId: string | number) => {
  if (!projectId) {
    return
  }
  try {
    //const projectId = Number(props.id)
    const projectAmount = await getProjectAmount(projectId)

    // 获取收付款金额
    const [received, paid] = await Promise.all([
      ProjectApi.getProjecNowAmounttByProjectIdAndYwType({
        projectId,
        ywType: '收款'
      }),
      ProjectApi.getProjecNowAmounttByProjectIdAndYwType({
        projectId,
        ywType: '付款'
      })
    ])

    financeData.value = {
      contractAmount: formatCurrency(projectAmount),
      receivedAmount: formatCurrency(received),
      expendAmount: formatCurrency(paid),
      remainingAmount: formatCurrency(
        one.value.paymentRelationship === '收'
          ? projectAmount - received
          : projectAmount - paid
      ),
      financePlans: []
    }

    console.log('6666666：', one.value.paymentRelationship)
    console.log('项目财务数据：', financeData.value)
  } catch (error) {
    console.error('获取财务信息失败', error)
  }
}

// 获取合同数据
const fetchContractData = async (contractId: string | number) => {
  if (!contractId) {
    return
  }
  try {
    const data = await ContractApi.getContractNew(contractId)
    console.log('合同数据：', data)
    one.value = reactive(data)
    console.log('合同数据：', one.value)
    feeList.value = data.fees || [];
    console.log('合同数据111：', financeData.value.financePlans)
    console.log('合同数据：', one.value)
    const diContract = await ContractApi.getdicontract({ contractId: contractId })
    if (diContract && Array.isArray(diContract) && diContract.length > 0) {
      departmentAllocations.value = diContract.map((item: any) => ({
        id: item.id,
        division: item.division || '未知部门',
        amount: parseFloat(item.amount || '0'),
        proportion: parseFloat(item.proportion || '0'),
        contractId: item.contractId
      }))
    } else if (data.executingDeparment != null) {
      departmentAllocations.value = [{
        division: data.executingDeparment,
        amount: parseFloat(one.value.amount || '0'), // 转换为数字
        proportion: 100,
        contractId: contractId
      }]
    } else {
      departmentAllocations.value = []
    }
  } catch (error) {
    console.error('获取合同数据失败', error)
  }
}

const RowClassName = ({ row, rowIndex, }) => {
  if (one.value.paymentRelationship == '收') {
    console.log('收款行：', row.status)
    return 'red-row'
  } else {
    return 'green-row'
  }
}

watch(() => [props.id, props.contractId], () => {
  console.log('财务进度参数变化，重新加载数据', props.id, props.contractId)
  fetchContractData()
  fetchProjectFinance()
}, { immediate: true })

// 存储事件处理函数引用
const financeEventHandler = (data: { projectId?: string | number, contractId?: string | number }) => {
  console.log('接收到财务更新事件，参数：', data)
  fetchContractData(data.contractId);
  fetchProjectFinance(data.projectId);
}

const setupFinanceListener = () => {
  emitter.on('refresh-project-finance', financeEventHandler)
}

// 金额显示规范
const formatCurrency = (amount: number | undefined): string => {
  if (amount === undefined) {
    return '¥ 0.00'
  }
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount)
}

onMounted(() => {
  console.log('财务组件挂载完成，参数：', {
    projectId: props.id,
    contractId: props.contractId
  })
  fetchProjectFinance()
  fetchContractData()
  setupFinanceListener()
})

onBeforeUnmount(() => {
  emitter.off('refresh-project-finance', financeEventHandler)
})
</script>

<style scoped>
.section-block {
  margin-bottom: 40px;
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  scroll-margin-top: 80px;
}

:deep(.el-table .green-row) {
  background: rgba(0, 128, 0, 0.5) !important;
}

:deep(.el-table .red-row) {
  background: rgba(255, 0, 0, 0.5) !important;
}
</style>
