<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.projectmanage.dal.mysql.project.projectperson.ProjectPersonMapper">

    <resultMap id="ProjectPersonResultMap" type="cn.iocoder.yudao.module.projectmanage.dal.dataobject.project.projectperson.ProjectPersonDO" >
        <id property="id" column="id"/>
        <result property="projectGroupId" column="project_group_id"/>
        <result property="projectGroupCode" column="project_group_code"/>
        <result property="projectGroupName" column="project_group_name"/>
        <result property="projectId" column="project_id"/>
        <result property="projectCode" column="project_code"/>
        <result property="projectName" column="project_name"/>
        <result property="name" column="name"/>
        <result property="department" column="department"/>
        <result property="occupation" column="occupation"/>
        <result property="status" column="status"/>
        <result property="remark" column="remark"/>
        <result property="systemName" column="system_name"/>
        <result property="systemId" column="system_id"/>
        <result property="percentage" column="percentage"/>
        <result property="time" column="time"/>
        <result property="managerConfirm" column="manager_confirm"/>
        <result property="rejectReason" column="reject_reason"/>
        <result property="managerRejectTime" column="manager_reject_time"/>
        <result property="managerOperateTime" column="manager_operate_time"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="creator" column="creator"/>
        <result property="updater" column="updater"/>
        <result property="deleted" column="deleted"/>
    </resultMap>

    <!-- 关联查询项目组名称、项目名称 -->
    <select id="selectListWithGroupAndProjectByProjectIdAndGroupId" resultMap="ProjectPersonResultMap">
        SELECT pp.*,
        pg.project_grop_name AS project_group_name,
        p.project_name AS project_name
        FROM projectmanage_project_person pp
        LEFT JOIN projectmanage_project_grop pg ON pp.project_group_id = pg.id
        LEFT JOIN projectmanage_project p ON pp.project_id = p.id
        <where>
            <if test="projectId != null and projectId != ''">
                AND pp.project_id = #{projectId}
            </if>
            <if test="projectGroupId != null and projectGroupId != ''">
                AND pp.project_group_id = #{projectGroupId}
            </if>
            AND pp.deleted = 0
        </where>
        ORDER BY pp.create_time DESC
    </select>

    <!-- 关联查询项目组名称、项目名称和附件，并按系统ID过滤 -->
    <select id="selectListWithGroupAndProjectByProjectIdAndGroupIdAndSystemId" resultMap="ProjectPersonResultMap">
        SELECT pp.*,
        pg.project_grop_name AS project_group_name,
        p.project_name AS project_name
        FROM projectmanage_project_person pp
        LEFT JOIN projectmanage_project_grop pg ON pp.project_group_id = pg.id
        LEFT JOIN projectmanage_project p ON pp.project_id = p.id
        <where>
            <if test="projectId != null and projectId != ''">
                AND pp.project_id = #{projectId}
            </if>
            <if test="projectGroupId != null and projectGroupId != ''">
                AND pp.project_group_id = #{projectGroupId}
            </if>
            <if test="systemId != null and systemId != ''">
                AND pp.system_id = #{systemId}
            </if>
            AND pp.deleted = 0
        </where>
        ORDER BY pp.create_time DESC
    </select>

</mapper>

