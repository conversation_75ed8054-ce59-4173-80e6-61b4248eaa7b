package cn.iocoder.yudao.module.projectmanage.controller.admin.bankwater;

import cn.iocoder.yudao.module.projectmanage.controller.admin.bankwater.util.*;
import cn.iocoder.yudao.module.projectmanage.dal.mysql.bankwater.BankWaterMapper;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.exceptions.MybatisPlusException;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.*;
import jakarta.servlet.http.*;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.io.IOException;
import java.util.stream.Collectors;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;
import static cn.iocoder.yudao.module.projectmanage.controller.admin.bankwater.util.DateUtils.YYYY_MM_DD_HH_MM_SS;

import cn.iocoder.yudao.module.projectmanage.controller.admin.bankwater.vo.*;
import cn.iocoder.yudao.module.projectmanage.dal.dataobject.bankwater.BankWaterDO;
import cn.iocoder.yudao.module.projectmanage.service.bankwater.BankWaterService;
import org.springframework.web.client.RestTemplate;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

@Tag(name = "管理后台 - 电子回单查询")
@RestController
@RequestMapping("/projectmanage/bank-water")
@Validated
public class BankWaterController {

    @Resource
    private BankWaterService bankWaterService;


    @Resource
    private BankWaterMapper bankwaterMapper;


    public static JSONObject doPost(String url, JSONObject data) {


        RestTemplate restTemplate = new RestTemplate();

        //跳过https证书认证
        SimpleClientHttpRequestFactory factory = new SSLFactory();
//        //设置代理访问
//        factory.setProxy(new Proxy(Proxy.Type.HTTP,new InetSocketAddress("*************",1180)));
//
        restTemplate.setRequestFactory(factory);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        //SimpleClientHttpRequestFactory reqfac = new SimpleClientHttpRequestFactory();
        //reqfac.setProxy(new Proxy(Proxy.Type.HTTP, new InetSocketAddress(ip, port)));
        //restTemplate.setRequestFactory(reqfac);
        restTemplate.getMessageConverters().set(1, new StringHttpMessageConverter(StandardCharsets.UTF_8));
        HttpEntity<Object> entity = new HttpEntity<Object>(data, headers);
        String result = restTemplate.postForObject(url, entity, String.class);
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject = JSON.parseObject(result);
        } catch (Exception e) {
            jsonObject.put("result", result);
        } finally {
            return jsonObject;
        }
    }

    @PostMapping("/create")
    @Operation(summary = "创建电子回单查询")
//    @PreAuthorize("@ss.hasPermission('projectmanage:statement:create')")
    public CommonResult<Long> createStatement(@Valid @RequestBody BankWaterSaveReqVO createReqVO) {
        return success(bankWaterService.createStatement(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新电子回单查询")
//    @PreAuthorize("@ss.hasPermission('projectmanage:statement:update')")
    public CommonResult<Boolean> updateStatement(@Valid @RequestBody BankWaterSaveReqVO updateReqVO) {
        bankWaterService.updateStatement(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除电子回单查询")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('projectmanage:statement:delete')")
    public CommonResult<Boolean> deleteStatement(@RequestParam("id") Long id) {
        bankWaterService.deleteStatement(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得电子回单查询")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('projectmanage:statement:query')")
    public CommonResult<BankWaterRespVO> getStatement(@RequestParam("id") Long id) {
        BankWaterDO statement = bankWaterService.getStatement(id);
        return success(BeanUtils.toBean(statement, BankWaterRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得电子回单查询分页")
//    @PreAuthorize("@ss.hasPermission('projectmanage:statement:query')")
    public CommonResult<PageResult<BankWaterRespVO>> getStatementPage(@Valid BankWaterPageReqVO pageReqVO) {
        PageResult<BankWaterDO> pageResult = bankWaterService.getStatementPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, BankWaterRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出电子回单查询 Excel")
//    @PreAuthorize("@ss.hasPermission('projectmanage:statement:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportStatementExcel(@Valid BankWaterPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<BankWaterDO> list = bankWaterService.getStatementPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "电子回单查询.xls", "数据", BankWaterRespVO.class,
                        BeanUtils.toBean(list, BankWaterRespVO.class));
    }


    // 1.获取app_token
    @RequestMapping("/getAppToken")
    public String getAppToken() {
        String OS = System.getProperty("os.name").toLowerCase();
        Map<String, Object> map = new HashMap();
        JSONObject result;
        map.put("appId", "TLYWXT");
        map.put("appSecuret", "Tuolunyewuxt@2023");
        map.put("tenantid", "ierp");
        map.put("accountId", "");
        map.put("language", "zh_CN");
        JSONObject request = JSONObject.parseObject(JSON.toJSONString(map));
        result = doPost("https://acc.gzport.com/ierp/api/getAppToken.do", request);
        // 接口的响应数据转换为java对象数据
        GetAppTokenRes getAppTokenRes = JSONObject.toJavaObject(result, GetAppTokenRes.class);
        // 获取app_token 错误
        if ("error".equals(getAppTokenRes.state)) {
            throw new MybatisPlusException("获取app_token 错误");
        }
        // 检查返回码
        return getAppTokenRes.data.app_token;
    }

    //2.获取Accesstoken
    @RequestMapping("/getAccessToken")
    public String getAccess_token(String apptoken) {
        String OS = System.getProperty("os.name").toLowerCase();
        Map<String, Object> map = new HashMap();
        JSONObject result;
//        if ("GZGBRTB".equals(SecurityUtils.getLoginUser().getUser().getCompanyCode())) {
//            // 数据公司的正式号码
//            map.put("user", "***********");
              // 数据公司的测试号码
            map.put("user", "***********");
            // 拖轮公司的号码
//            map.put("user", "***********");
//        }
        map.put("apptoken", apptoken);
        map.put("tenantid", "ierp");
        map.put("accountId", "");
        map.put("usertype", "Mobile");
        JSONObject request = JSONObject.parseObject(JSON.toJSONString(map));
//            result = CWRestfulUtils.doPost("https://acc.gzport.com/ierp/api/login.do", request);
        result = doPost("https://acc.gzport.com/ierp/api/login.do", request);

//        if (OS.indexOf("linux") >= 0) {
//            if ("GZGBRTB".equals(SecurityUtils.getLoginUser().getUser().getCompanyCode())) {
//                // 拖轮公司的号码
//                map.put("user", "***********");
//            } else {
//                // 海港公司的号码
//                if("yjw".equals(SecurityUtils.getUsername())){
//                    map.put("user", "***********");
//                }else {
//                    map.put("user", "***********");
//                }
//
//            }
//            map.put("apptoken", apptoken);
//            map.put("tenantid", "ierp");
//            map.put("accountId", "");
//            map.put("usertype", "Mobile");
//            JSONObject request = JSONObject.parseObject(JSON.toJSONString(map));
////            result = CWRestfulUtils.doPost("https://acc.gzport.com/ierp/api/login.do", request);
//            result = doPost("https://acc.gzport.com/ierp/api/login.do", request);
//        } else {
//            map.put("user", "***********");
//            map.put("apptoken", apptoken);
//            map.put("tenantid", "ierp");
//            map.put("accountId", "1132610131649888256");
//            map.put("usertype", "Mobile");
//            JSONObject request = JSONObject.parseObject(JSON.toJSONString(map));
//            result = doPost("https://uat.gzport.com/ierp/api/login.do", request);
//        }


        // 正式环境
//        map.put("user", "***********");
//        map.put("apptoken", apptoken);
//        map.put("tenantid", "ierp");
//        map.put("accountId", "");
//        map.put("usertype", "Mobile");
//        result = CWRestfulUtils.doPost("https://acc.gzport.com/ierp/api/login.do", request);


        // 接口的响应数据转换为java对象数据
        AccesTokenRes accesTokenRes = JSONObject.toJavaObject(result, AccesTokenRes.class);
        // 检查返回码
        return accesTokenRes.data.access_token;
    }



    //3.使用Accestoken请求金蝶接口获取数据
    @RequestMapping("/tongbuJindieList")
//    @Log(title = "金蝶流水同步", businessType = BusinessType.INSERT)
    public CommonResult<List<BankWaterDO>> tongbuJindieList() throws IOException {
        Date nowDate = new Date();
        Calendar ca = Calendar.getInstance();// 得到一个Calendar的实例
        ca.setTime(nowDate); // 设置时间为当前时间
        ca.add(Calendar.DATE, -10); // date减10
        Date lastDate = ca.getTime();
        String startDate = DateUtils.parseDateToStr(YYYY_MM_DD_HH_MM_SS, lastDate);
        String endDate = DateUtils.parseDateToStr(YYYY_MM_DD_HH_MM_SS, nowDate);
        // 拖轮 ********
        //海港  ********
        //数据公司
        String hRDeptId = "********";
//        if ("GZGBRTB".equals(SecurityUtils.getLoginUser().getUser().getCompanyCode())) {
//            // 拖轮公司的号码
//            hRDeptId = "********";
//        } else if ("GZOCTB".equals(SecurityUtils.getLoginUser().getUser().getCompanyCode())) {
//            // 海港公司的号码
//            hRDeptId = "********";
//        }
        String access_token= getAccess_token(getAppToken());
        JinDieBaseRes<List<BankWaterDO>> baseRes = JinDieUtils.jdTrans(access_token, hRDeptId, startDate, endDate, "https://acc.gzport.com/ierp/kapi/app/gzg_hr/SysElecReceipFind");
        if (StringUtils.isNotNull(baseRes)) {
            if (baseRes.isSuccess()) {
                List<BankWaterDO> list = baseRes.getData();
                // 过滤掉creditamount为null或0的数据    也要付款 && item.getCreditamount() != null && item.getCreditamount().compareTo(BigDecimal.ZERO) != 0
                list = list.stream()
                        .filter(item -> item != null )
                        .collect(Collectors.toList());
                System.out.println("金蝶流水获取成功！");
                for (BankWaterDO item : list) {
                    if (item.getReceiptno() != null && !"".equals(item.getReceiptno())) {
                        System.out.println(item.getReceiptno());
                    }
                    // 根据 detailid 查询
                    BankWaterDO bankWater = bankWaterService.getOne(new QueryWrapper<BankWaterDO>()
                            .eq("detailid", item.getDetailid()));
                    // 没查到,插入
                    if (bankWater == null || bankWater.getId() == null) {
                        // 同步人的公司号码
//                        item.setCompanyCode(SecurityUtils.getLoginUser().getUser().getCompanyCode());
                        bankWaterService.save(item);
                    } else {
                        // 查到，更新
                        item.setId(bankWater.getId());
                        bankWaterService.updateById(item);
                        // 以前没有附件，现在有，，已经绑定过，查看是否有附件，如果有附件，同步附件
                        // 以前没有附件，现在有附件，同时以前还绑定过，那么模拟插入附件
                        // 暂时不需要附件 2025.1.13
//                        if (sysElecReceipFind.getReceiptno() == null && item.getReceiptno() != null && !"".equals(item.getReceiptno()) && "1".equals(sysElecReceipFind.getIsBind())) {
//                            sysElecReceipFind.setReceiptno(item.getReceiptno());
//                            // 模拟勾选绑定收款记录
//                            String rq = sysElecReceipFind.getBizdate().substring(0, 10).replace("-", "");
//                            String filenamePrefix = "";
//                            filenamePrefix = "银行回单-" + sysElecReceipFind.getYwCode() + "-" + sysElecReceipFind.getOppunit() + "-" + rq + "-" + sysElecReceipFind.getCreditamount() + "元";
//                            String tpf = System.getProperty("file.separator");
//                            Date currDate = new Date();
//                            String yyyy_MM_dd = new SimpleDateFormat("yyyy" + tpf + "MM" + tpf + "dd").format(currDate);
//                            String dirPath = RuoYiConfig.getYHHDPath() + tpf + yyyy_MM_dd + tpf;
//                            //填充pdf文件路径
//                            String saveDir = dirPath + filenamePrefix + ".pdf";
//                            RestTemplate restTemplate = new RestTemplate();
//                            HttpHeaders headers = new HttpHeaders();
//                            headers.set("accessToken", access_token);
//                            headers.set("api", "true");
//                            String url = "https://acc.gzport.com/ierp/attachment/download.do?path=" + sysElecReceipFind.getReceiptno(); // 注意这里可能需要根据实际情况进行URL编码处理以避免特殊字符问题。
//                            HttpEntity<String> entity = new HttpEntity<>(null, headers);
//                            ResponseEntity<byte[]> responseEntity = restTemplate.exchange(
//                                    url,
//                                    HttpMethod.GET,
//                                    entity,
//                                    byte[].class);
//                            byte[] fileContent = responseEntity.getBody();
//                            // 保存fileContent文件,保存得路径为pdfPath
////                            StateUtils.deepCreateFile(saveDir);
//                            ByteArrayResource byteArrayResource = new ByteArrayResource(fileContent);
//                            InputStream inputStream = byteArrayResource.getInputStream();
//                            FileOutputStream outputStream = new FileOutputStream(saveDir);
//                            byte[] buffer = new byte[1024];
//                            int bytesRead;
//                            while ((bytesRead = inputStream.read(buffer)) != -1) {
//                                outputStream.write(buffer, 0, bytesRead);
//                            }
//                            outputStream.close();
//                            inputStream.close();
//                            String cnFileName = "";
//                            cnFileName = filenamePrefix;
//                            // 向SYS_FILE_WORKPLAN表插入交电子凭证pdf文件记录
//                            commonServiceImpl.recordInSysFileWorkPlanNoDelete("",
//                                    sysElecReceipFind.getYwCode(),
//                                    "/YHHD/" + yyyy_MM_dd,
//                                    filenamePrefix + ".pdf",
//                                    cnFileName,
//                                    encodingFilename(filenamePrefix) + ".pdf",
//                                    "1",
//                                    StateUtils.getUserName(),
//                                    "YHHD");
//                        }

                    }
                }
                return success(list);
            }
        }
        return null;
    }

    @PutMapping("/update-yw-status")
    @Operation(summary = "更新业务数据状态")
    @Parameter(name = "id", description = "编号", required = true)
    @Parameter(name = "isYw", description = "业务数据状态", required = true)
    public CommonResult<Boolean> updateYwStatus(@RequestParam("id") Long id, @RequestParam("isYw") String isYw) {
        // 创建更新条件
        LambdaUpdateWrapper<BankWaterDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(BankWaterDO::getId, id)
                    .set(BankWaterDO::getIsYw, isYw);
        
        // 执行更新
        boolean success = bankWaterService.update(updateWrapper);
        return success(success);
    }

    @GetMapping("/page-filtered")
    @Operation(summary = "获得过滤后的电子回单查询分页")
    public CommonResult<PageResult<BankWaterRespVO>> getFilteredStatementPage(@Valid BankWaterPageReqVO pageReqVO, 
                                                                            @RequestParam(required = true) String type) {
        // 调用Service方法，根据type过滤，type可能的值为"credit"(收款)或"debit"(付款)
        PageResult<BankWaterDO> pageResult = bankWaterService.getFilteredStatementPage(pageReqVO, type);
        return success(BeanUtils.toBean(pageResult, BankWaterRespVO.class));
    }

    @GetMapping("/match")
    @Operation(summary = "匹配银行流水记录")
    public CommonResult<List<BankWaterRespVO>> matchBankWater(
            @RequestParam("payer") String payer,
            @RequestParam("amount") BigDecimal amount) {
        // 查询匹配的银行流水记录，对方户名匹配收款方，付款金额匹配
        List<BankWaterDO> matches = bankWaterService.list(
                Wrappers.<BankWaterDO>lambdaQuery()
                        .eq(BankWaterDO::getOppunit, payer)
                        .eq(BankWaterDO::getDebitamount, amount)
                        .orderByDesc(BankWaterDO::getBizdate)
        );

        // 将结果转换为VO对象
        List<BankWaterRespVO> result = BeanUtils.toBean(matches, BankWaterRespVO.class);
        return success(result);
    }

    /**
     * 批量匹配银行流水记录
     * 
     * @param requestBody 包含多个匹配请求的数组，每个请求包含payer和amount
     * @return 匹配到的银行流水记录列表
     */
    @PostMapping("/batch-match")
    @Operation(summary = "批量匹配银行流水记录")
    public CommonResult<List<BankWaterRespVO>> batchMatchBankWater(
            @RequestBody List<Map<String, Object>> requestBody) {
        // 存储所有匹配的记录
        List<BankWaterDO> allMatches = new ArrayList<>();
        
        // 遍历请求中的每个匹配条件
        for (Map<String, Object> request : requestBody) {
            String payer = (String) request.get("payer");
            BigDecimal amount;
            
            // 处理金额，确保正确转换
            Object amountObj = request.get("amount");
            if (amountObj instanceof Number) {
                amount = new BigDecimal(amountObj.toString());
            } else if (amountObj instanceof String) {
                try {
                    amount = new BigDecimal((String) amountObj);
                } catch (NumberFormatException e) {
                    continue; // 跳过无效的金额
                }
            } else {
                continue; // 跳过无效的金额
            }
            
            // 查询匹配的银行流水记录
            List<BankWaterDO> matches = bankWaterService.list(
                    Wrappers.<BankWaterDO>lambdaQuery()
                            .eq(BankWaterDO::getOppunit, payer)
                            .eq(BankWaterDO::getDebitamount, amount)
                            .orderByDesc(BankWaterDO::getBizdate)
            );
            
            // 添加到总匹配列表中
            allMatches.addAll(matches);
        }
        
        // 将结果转换为VO对象
        List<BankWaterRespVO> result = BeanUtils.toBean(allMatches, BankWaterRespVO.class);
        return success(result);
    }

}
