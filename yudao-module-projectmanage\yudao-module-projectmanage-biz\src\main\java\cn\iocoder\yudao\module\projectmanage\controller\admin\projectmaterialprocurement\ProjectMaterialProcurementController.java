package cn.iocoder.yudao.module.projectmanage.controller.admin.projectmaterialprocurement;

import cn.hutool.json.JSONObject;
import cn.iocoder.yudao.framework.security.core.LoginUser;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.projectmanage.controller.admin.project.vo.ProjectSaveReqVO;
import cn.iocoder.yudao.module.projectmanage.controller.admin.projectapproval.vo.ProjectApprovalPageReqVO;
import cn.iocoder.yudao.module.projectmanage.controller.admin.projectapproval.vo.ProjectApprovalRespVO;
import cn.iocoder.yudao.module.projectmanage.controller.admin.projectapproval.vo.ProjectApprovalSaveReqVO;
import cn.iocoder.yudao.module.projectmanage.controller.admin.projectapproval.vo.ProjectApprovalTotal;
import cn.iocoder.yudao.module.projectmanage.controller.admin.projectmanagerapproval.vo.OaSignResult;
import cn.iocoder.yudao.module.projectmanage.controller.admin.projectmanagerapproval.vo.ProjectManagerApprovalSaveReqVO;
import cn.iocoder.yudao.module.projectmanage.dal.dataobject.infrafile.InfraFileDO;
import cn.iocoder.yudao.module.projectmanage.dal.dataobject.project.ProjectDO;
import cn.iocoder.yudao.module.projectmanage.dal.dataobject.projectapproval.ProjectApprovalDO;
import cn.iocoder.yudao.module.projectmanage.dal.mysql.MyMapper;
import cn.iocoder.yudao.module.projectmanage.service.project.ProjectService;
import com.alibaba.fastjson.JSON;
import jakarta.annotation.security.PermitAll;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;

import java.io.File;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.projectmanage.controller.admin.projectmaterialprocurement.vo.*;
import cn.iocoder.yudao.module.projectmanage.dal.dataobject.projectmaterialprocurement.ProjectMaterialProcurementDO;
import cn.iocoder.yudao.module.projectmanage.service.projectmaterialprocurement.ProjectMaterialProcurementService;
import org.springframework.web.client.RestTemplate;

@Tag(name = "管理后台 - 物资采购审批")
@RestController
@RequestMapping("/projectmanage/project-material-procurement")
@Validated
public class ProjectMaterialProcurementController {

    @Resource
    private ProjectMaterialProcurementService projectMaterialProcurementService;

    @Autowired
    private MyMapper mymapper;

    @Resource
    private ProjectService projectService;

    @PostMapping("/create")
    @Operation(summary = "创建物资采购审批")
    @PreAuthorize("@ss.hasPermission('projectmanage:project-material-procurement:create')")
    public CommonResult<Long> createProjectMaterialProcurement(@Valid @RequestBody ProjectMaterialProcurementSaveReqVO createReqVO) {
        return success(projectMaterialProcurementService.createProjectMaterialProcurement(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新物资采购审批")
    @PreAuthorize("@ss.hasPermission('projectmanage:project-material-procurement:update')")
    public CommonResult<Boolean> updateProjectMaterialProcurement(@Valid @RequestBody ProjectMaterialProcurementSaveReqVO updateReqVO) {
        projectMaterialProcurementService.updateProjectMaterialProcurement(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除物资采购审批")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('projectmanage:project-material-procurement:delete')")
    public CommonResult<Boolean> deleteProjectMaterialProcurement(@RequestParam("id") Long id) {
        projectMaterialProcurementService.deleteProjectMaterialProcurement(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得物资采购审批")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('projectmanage:project-material-procurement:query')")
    public CommonResult<ProjectMaterialProcurementRespVO> getProjectMaterialProcurement(@RequestParam("id") Long id) {
        ProjectMaterialProcurementDO projectMaterialProcurement = projectMaterialProcurementService.getProjectMaterialProcurement(id);
        return success(BeanUtils.toBean(projectMaterialProcurement, ProjectMaterialProcurementRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得物资采购审批分页")
    @PreAuthorize("@ss.hasPermission('projectmanage:project-material-procurement:query')")
    public CommonResult<PageResult<ProjectMaterialProcurementRespVO>> getProjectMaterialProcurementPage(@Valid ProjectMaterialProcurementPageReqVO pageReqVO) {
        PageResult<ProjectMaterialProcurementDO> pageResult = projectMaterialProcurementService.getProjectMaterialProcurementPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ProjectMaterialProcurementRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出物资采购审批 Excel")
    @PreAuthorize("@ss.hasPermission('projectmanage:project-material-procurement:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportProjectMaterialProcurementExcel(@Valid ProjectMaterialProcurementPageReqVO pageReqVO,
                                                      HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ProjectMaterialProcurementDO> list = projectMaterialProcurementService.getProjectMaterialProcurementPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "物资采购审批.xls", "数据", ProjectMaterialProcurementRespVO.class,
                BeanUtils.toBean(list, ProjectMaterialProcurementRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得物资采购列表")
    public CommonResult<ProjectMaterialProcurementRespVO> getProjectApprovalList(@RequestParam("projectId") Long projectId) {
        System.out.println(projectId);
        ProjectDO project = projectService.getProject(projectId);
//        ProjectApprovalSaveReqVO projectApprovalSaveReqVO = BeanUtils.toBean(projectMaterialProcurement, ProjectApprovalSaveReqVO.class);
        ProjectMaterialProcurementSaveReqVO projectMaterialProcurementSaveReqVO = BeanUtils.toBean(project, ProjectMaterialProcurementSaveReqVO.class);
//        projectApprovalSaveReqVO.setProjectManager(SecurityFrameworkUtils.getLoginUser().getInfo().get("nickname"));
//        projectApprovalSaveReqVO.setPhone(mymapper.selectUsernameById(SecurityFrameworkUtils.getLoginUser().getId()));
        projectMaterialProcurementSaveReqVO.setProjectName(project.getProjectName());
        projectMaterialProcurementSaveReqVO.setEstimateMoney(project.getTotalAmount());
        projectMaterialProcurementSaveReqVO.setProjectId(project.getId());
        projectMaterialProcurementSaveReqVO.setId(null);
        return success(BeanUtils.toBean(projectMaterialProcurementSaveReqVO, ProjectMaterialProcurementRespVO.class));
    }

    @PostMapping("/push")
    @Operation(summary = "推送OA")
    public CommonResult pushApproval(@RequestBody MaterialApproval materialApproval) {
//        ProjectApprovalPageReqVO projectApprovalPageReqVO = projectApprovalTotal.projectApprovalPageReqVO;
//        ProjectDO project = projectService.getProject(projectApprovalPageReqVO.getProjectId());
        ProjectMaterialProcurementPageReqVO projectMaterialProcurementPageReqVO = materialApproval.projectMaterialProcurementPageReqVO ;
//        ProjectMaterialProcurementDO projectMaterialProcurement = projectMaterialProcurementService.getProjectMaterialProcurement(projectMaterialProcurementPageReqVO.getProjectId());
        ProjectDO project = projectService.getProject(projectMaterialProcurementPageReqVO.getProjectId());
        String url = "https://oa.gzport.com/api/km-review/kmReviewRestService/addReview";
        String fdTemplateId = "1737572e3b9dc3e37c16f174e54a9451";
        RestTemplate yourRestTemplate = new RestTemplate();
        //把SysNewsParamterForm转换成MultiValueMap
        MultiValueMap<String, Object> wholeForm = new LinkedMultiValueMap<>();
        JSONObject formValues = new JSONObject();
        wholeForm.add("docSubject", projectMaterialProcurementPageReqVO.getProjectName());
        JSONObject person = new JSONObject();
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        String a = loginUser.getInfo().get("nickname");
        String personNo = mymapper.selectDictRemark(a);
        if (personNo == null || personNo.isEmpty()) {
            return CommonResult.error(400,"查不到发起人");
        }
        person.put("PersonNo", mymapper.selectDictRemark(a));
        wholeForm.add("docCreator", JSON.toJSONString(person));
        wholeForm.add("docStatus", 10);
        wholeForm.add("fdTemplateId", fdTemplateId);
        formValues.put("fd_38ae1c5c7fdcf0", projectMaterialProcurementPageReqVO.getProjectName());//物资名称
        formValues.put("fd_38ae1c5d8ef114", projectMaterialProcurementPageReqVO.getDetail());//具体内容
        formValues.put("fd_38ae1c5ed96f00", projectMaterialProcurementPageReqVO.getEstimateMoney());//预算金额
        formValues.put("fd_38ae1c606ef6fc", projectMaterialProcurementPageReqVO.getPriceComparison());//比价邀标情况
        formValues.put("fd_3b42d45d065e30", projectMaterialProcurementPageReqVO.getIsAssets());//是否列入固定资产

        wholeForm.add("formValues", formValues);

        List<InfraFileDO> infraFileDOlist = mymapper.selectMaterialApprovalInfraFile(projectMaterialProcurementPageReqVO.getProjectId());
        if (infraFileDOlist.isEmpty()) {
            return CommonResult.error(400, "至少上传一个附件");
        }
        for (int k = 0; k < infraFileDOlist.size(); k++) {
            // 获取文件 URL 和文件名
            String file_url = infraFileDOlist.get(k).getUrl();
            String fileName = infraFileDOlist.get(k).getName();
            // 创建临时文件用于存放下载的附件
            File file = null;
            try {
                // 基于 fileName 创建临时文件
                file = File.createTempFile(fileName, null);
                // 从 URL 下载文件内容并写入临时文件
                URL url_file = new URL(file_url);
                Files.copy(url_file.openStream(), Paths.get(file.getAbsolutePath()), StandardCopyOption.REPLACE_EXISTING);
            } catch (IOException e) {
                // 如果下载失败，打印错误信息或进行其他处理
                System.err.println("下载文件失败：" + file_url);
                e.printStackTrace();
                continue; // 跳过当前文件，继续处理下一个
            }
            // 添加附件到表单
            if (file != null) {
                wholeForm.add("attachmentForms[" + k + "].fdKey", "fd_38f4059f74ec2a");
                wholeForm.add("attachmentForms[" + k + "].fdFileName", fileName);
                wholeForm.add("attachmentForms[" + k + "].fdAttachment", new FileSystemResource(file));
            }

        }

        HttpHeaders headers = new HttpHeaders();
        // 如果EKP对该接口启用了Basic认证，那么客户端需要加入
        String authentication = "ekp" + ":" + "ekpGzport1#";
        headers.set("authorization", "Basic " + Base64.getEncoder().encodeToString(authentication.getBytes()));
        // addAuth(headers,"yourAccount"+":"+"yourPassword");是VO，则使用APPLICATION_JSON
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        // 必须设置上传类型，如果入参是字符串，使用MediaType.TEXT_PLAIN；如果
        HttpEntity<MultiValueMap<String, Object>> entity = new HttpEntity<MultiValueMap<String, Object>>(wholeForm, headers);

        // 有返回值的情况 VO可以替换成具体的JavaBean
        ResponseEntity<String> obj = yourRestTemplate.exchange(url, HttpMethod.POST, entity, String.class);
        String body = obj.getBody();
        // 判断是否返回32位，不是就是报错信息
        if (body.length() != 32) {
            //                AjaxResult.error(wholeForm+"--"+body);
            return CommonResult.error(body);
        }
        ProjectMaterialProcurementSaveReqVO projectMaterialProcurementSaveReqVO =BeanUtils.toBean(projectMaterialProcurementPageReqVO, ProjectMaterialProcurementSaveReqVO.class);
        projectMaterialProcurementSaveReqVO.setFlowId(body);
        projectMaterialProcurementSaveReqVO.setFdTemplataId(fdTemplateId);
        projectMaterialProcurementSaveReqVO.setStatus("审批中");
        Long mainId = projectMaterialProcurementService.createProjectMaterialProcurement(projectMaterialProcurementSaveReqVO);
        project.setApproved("1");
        projectService.updateProject(BeanUtils.toBean(project, ProjectSaveReqVO.class));
        return CommonResult.success(body);
    }

    @PostMapping("/getResult")
    @PermitAll
    @Operation(summary = "审批结果")
    public CommonResult getResult(@RequestBody OaSignResult oaSignResult) {
        System.out.println("1111111111111111111" + oaSignResult);
        if (oaSignResult.getFdTemplateId() == null) {
            return CommonResult.error(400, "无此流程id，请联系管理员检查");
        }

        String flowId = oaSignResult.getFdTemplateId();
        ProjectMaterialProcurementDO projectMaterialProcurementDO = projectMaterialProcurementService.getProjectByFlowId(flowId);
        if (projectMaterialProcurementDO == null) {
            System.out.println("未找到流程ID: " + flowId);
            return CommonResult.error(400, "无此流程id，请联系管理员检查");
        }

        if ("2".equals(oaSignResult.getPassflag())) {
            projectMaterialProcurementDO.setStatus("审批通过");
        } else {
            projectMaterialProcurementDO.setStatus("被驳回");
        }
        projectMaterialProcurementService.updateProjectMaterialProcurement(BeanUtils.toBean(projectMaterialProcurementDO, ProjectMaterialProcurementSaveReqVO.class));
        return CommonResult.oasuccess("操作成功");

    }

}