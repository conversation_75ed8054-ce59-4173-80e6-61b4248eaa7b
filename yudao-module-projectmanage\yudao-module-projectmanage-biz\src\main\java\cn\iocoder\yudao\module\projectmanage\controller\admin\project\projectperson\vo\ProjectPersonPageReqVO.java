package cn.iocoder.yudao.module.projectmanage.controller.admin.project.projectperson.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 项目人员分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProjectPersonPageReqVO extends PageParam {

    @Schema(description = "记录id", example = "26665")
    private Long id;

    @Schema(description = "项目组id", example = "15541")
    private Long projectGroupId;

    @Schema(description = "项目组编号")
    private String projectGroupCode;

    @Schema(description = "项目组名称", example = "李四")
    private String projectGroupName;

    @Schema(description = "项目id", example = "3773")
    private Long projectId;

    @Schema(description = "项目编号")
    private String projectCode;

    @Schema(description = "项目名称", example = "芋艿")
    private String projectName;

    @Schema(description = "姓名", example = "张三")
    private String name;

    @Schema(description = "部门")
    private String department;

    @Schema(description = "岗位")
    private String occupation;

    @Schema(description = "状态", example = "1")
    private String status;

    @Schema(description = "备注")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "删除符（0未删除1已删除）")
    private Boolean deleted;

    @Schema(description = "系统名")
    private  String systemName;

    @Schema(description = "系统ID")
    private String systemId;

    @Schema(description = "比例")
    private String percentage;

    @Schema(description = "时间")
    private String time;

    @Schema(description = "经理是否确认")
    private String managerConfirm;

    /**
     * 驳回原因
     */
    @Schema(description = "驳回原因")
    private String rejectReason;

    /**
     * 经理驳回时间
     */
    @Schema(description = "经理驳回时间")
    private LocalDateTime managerRejectTime;

    /**
     * 经理操作时间
     */
    @Schema(description = "经理操作时间")
    private LocalDateTime managerOperateTime;

    @Schema(description = "部门id")
    private Integer deptId;


}
