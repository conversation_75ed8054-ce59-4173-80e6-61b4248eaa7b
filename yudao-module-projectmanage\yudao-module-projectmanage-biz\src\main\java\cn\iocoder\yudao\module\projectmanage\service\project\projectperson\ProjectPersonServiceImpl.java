package cn.iocoder.yudao.module.projectmanage.service.project.projectperson;

import cn.iocoder.yudao.framework.security.core.LoginUser;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.projectmanage.controller.admin.project.projectperson.vo.ProjectPersonPageReqVO;
import cn.iocoder.yudao.module.projectmanage.controller.admin.project.projectperson.vo.ProjectPersonSaveReqVO;
import cn.iocoder.yudao.module.projectmanage.dal.dataobject.weekly.WeeklyDO;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import cn.iocoder.yudao.module.projectmanage.dal.dataobject.project.projectperson.ProjectPersonDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.projectmanage.dal.mysql.project.projectperson.ProjectPersonMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.projectmanage.enums.ErrorCodeConstants.*;

import java.util.List;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

/**
 * 项目人员 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ProjectPersonServiceImpl implements ProjectPersonService {

    @Resource
    private ProjectPersonMapper projectPersonMapper;

    @Override
    public Long createProjectPerson(ProjectPersonSaveReqVO createReqVO) {
        // 插入
        ProjectPersonDO projectPerson = BeanUtils.toBean(createReqVO, ProjectPersonDO.class);
        // 获取当前登录用户
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        // 插入部门id
        if (loginUser != null) {
            Integer deptId = Integer.valueOf(loginUser.getInfo().get("deptId"));
            projectPerson.setDeptId(deptId);
        }
        projectPersonMapper.insert(projectPerson);
        // 返回
        return projectPerson.getId();
    }

    @Override
    public void updateProjectPerson(ProjectPersonSaveReqVO updateReqVO) {
        // 校验存在
        validateProjectPersonExists(updateReqVO.getId());
        // 更新
        ProjectPersonDO updateObj = BeanUtils.toBean(updateReqVO, ProjectPersonDO.class);
        projectPersonMapper.updateById(updateObj);
    }

    @Override
    public void deleteProjectPerson(Long id) {
        // 校验存在
        validateProjectPersonExists(id);
        // 删除
        projectPersonMapper.deleteById(id);
    }

    private void validateProjectPersonExists(Long id) {
        if (projectPersonMapper.selectById(id) == null) {
            throw exception(PROJECT_PERSON_NOT_EXISTS);
        }
    }

    @Override
    public ProjectPersonDO getProjectPerson(Long id) {
        return projectPersonMapper.selectById(id);
    }

    @Override
    public PageResult<ProjectPersonDO> getProjectPersonPage(ProjectPersonPageReqVO pageReqVO) {
        return projectPersonMapper.selectPage(pageReqVO);
    }

    @Override
    public List<ProjectPersonDO> getProjectPersonListByProjectGroupCode(String projectGroupCode) {
        return projectPersonMapper.selectList(new LambdaQueryWrapper<ProjectPersonDO>()
                .eq(ProjectPersonDO::getProjectGroupCode, projectGroupCode));
    }

    @Override
    public List<ProjectPersonDO> getProjectPersonListByProjectGroupId(String projectGroupId) {
        return projectPersonMapper.selectList(new LambdaQueryWrapper<ProjectPersonDO>()
                .eq(ProjectPersonDO::getProjectGroupId, projectGroupId));
    }

    @Override
    public List<ProjectPersonDO> getProjectPersonListByProjectCode(String projectCode) {
        return projectPersonMapper.selectList(new LambdaQueryWrapper<ProjectPersonDO>()
                .eq(ProjectPersonDO::getProjectCode, projectCode));
    }

    @Override
    public List<ProjectPersonDO> getProjectPersonListBySystemId(String systemId) {
        return projectPersonMapper.selectList(new LambdaQueryWrapper<ProjectPersonDO>()
                .eq(ProjectPersonDO::getSystemId, systemId));
    }

    @Override
    public List<ProjectPersonDO> getProjectPersonListByProjectId(String projectId) {
        return projectPersonMapper.selectList(new LambdaQueryWrapper<ProjectPersonDO>()
                .eq(ProjectPersonDO::getProjectId, projectId));
    }

    @Override
    public List<ProjectPersonDO> getPersonListByProjectIdAndGroupId(String projectId, String projectGroupId) {
        return projectPersonMapper.selectListWithGroupAndProjectByProjectIdAndGroupId(projectId, projectGroupId);
    }

    @Override
    public List<ProjectPersonDO> getPersonListByProjectIdAndGroupId(String projectId, String projectGroupId, String systemId) {
        // 使用mybatis-plus的查询，结合自定义查询
        // 如果systemId为空，则直接调用原来的方法
        if (systemId == null || systemId.isEmpty()) {
            return getPersonListByProjectIdAndGroupId(projectId, projectGroupId);
        }

        // 否则，使用系统ID进行过滤查询
        return projectPersonMapper.selectListWithGroupAndProjectByProjectIdAndGroupIdAndSystemId(projectId, projectGroupId, systemId);
    }

}
